import { useState, useC<PERSON>back, useRef, useEffect, useMemo } from "react";
import { useTheme } from "next-themes"; // NEU für Theme-Handling
import { CodeHighlighter } from "./CodeHighlighter";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { MonacoE<PERSON>or, EditorError } from "./MonacoEditor"; // MODIFIED: Import EditorError
import { FileSizeWarning } from "./FileSizeWarning";
import { FileTypeIndicator } from "./FileTypeIndicator";
import { ValidationResultDisplay } from "./ValidationResultDisplay";
import brain from "brain";
import { CodeValidationRequest, CodeValidationResponse, LlmValidateCodeSyntaxRequest, LlmValidateCodeSyntaxResponse, SyntaxErrorDetail } from "types"; // MODIFIED: Import LLM types
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Upload, Alert<PERSON>riangle, FileCode2, CheckCircle, X, AlertCircle, Cpu } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Props {
  value: string;
  onChange: (value: string) => void;
  language: string;
  onLanguageChange?: (language: string) => void;
  height?: string;
  showGisFocusedValidation?: boolean;
  onGenerateDocumentation?: () => void;
  readOnly?: boolean;
  onFileUploaded?: (file: { name: string; extension: string; type?: string }) => void;
  errors: EditorError[]; // NEU: Receive errors as prop
  onErrorsChange: (errors: EditorError[]) => void; // NEU: Callback to update errors
}

export function CodeUploader({
  value,
  onChange,
  language,
  onLanguageChange,
  height = "500px",
  onGenerateDocumentation,
  readOnly = false,
  onFileUploaded,
  errors = [], // Default to empty array
  onErrorsChange = () => {}, // Default to no-op function
}: Props) {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<CodeValidationResponse & { errorLineNumber?: number; errorColumnNumber?: number } | null>(null);
  const [uploadedFileSize, setUploadedFileSize] = useState<number | null>(null);
  const [uploadedFile, setUploadedFile] = useState<{ name: string; extension: string; type?: string } | null>(null);
  const [dragHoverCount, setDragHoverCount] = useState(0); // Track multiple drag events
  // const [syntaxErrors, setSyntaxErrors] = useState<EditorError[]>([]); // ENTFERNT: State wird von oben verwaltet

  const validateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to validate code with debounce
  const { theme, systemTheme } = useTheme(); // NEU für Theme-Handling
  const editorRef = useRef<any>(null);

  // GIS specific keywords for enhanced detection
  const gisKeywords = useMemo(() => [
    // Common GIS libraries and modules
    'arcpy', 'arcgis', 'qgis', 'gdal', 'ogr', 'geopandas', 'shapely', 'fiona',
    'pyproj', 'rasterio', 'earthpy', 'leaflet', 'mapbox', 'openlayers',
    // Common GIS functions and concepts
    'spatial', 'buffer', 'intersect', 'projection', 'feature', 'geometry',
    'raster', 'vector', 'crs', 'epsg', 'wgs84', 'utm', 'overlay', 'clip',
    'dissolve', 'union', 'erase', 'merge', 'spatial.join', 'geotransform',
    // GIS data types
    'multipolygon', 'multipoint', 'multilinestring', 'featurelayer', 'pointcloud',
    'dem', 'tin', 'raster.dataset', 'feature.class', 'shapefile', 'geojson',
    'geopackage', 'gdb', 'geodatabase'
  ], []);

  // Memoized hexagonal pattern for the background
  const hexagonalGridBackground = useMemo(() => {
    return `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cpath d='M15 10L30 0L45 10L45 30L30 40L15 30L15 10Z' fill='none' stroke='%23cccccc' stroke-opacity='0.2' stroke-width='1'/%3E%3C/svg%3E")`;
  }, []);

  const highlightError = (line: number, column: number) => {
    if (editorRef.current) {
      editorRef.current.revealLineInCenter(line);
      // editorRef.current.applyErrorHighlighting(line, column, validationResult?.errorMessage || 'Syntax error'); // OLD single error highlighting
      // Multiple errors are now passed as a prop to MonacoEditor
    }

    // Die Toast-Nachricht bleibt, da sie nützliche Infos anzeigt
    const lines = value.split('\n');
    const lineContent = lines[line - 1] || '';
    const errorLinePreview = lineContent.trim().substring(0, 40) + (lineContent.length > 40 ? '...' : '');

    toast.info(
      <div className="flex items-start gap-2">
        <AlertCircle className="flex-shrink-0 mt-0.5" size={16} />
        <div className="w-full overflow-hidden">
          <span className="font-medium">Jumped to error</span>
          <p className="text-xs font-mono text-muted-foreground mt-0.5">
            Line {line}{column > 0 ? `, Column ${column}` : ''}
          </p>
          {errorLinePreview && (
            <div className="mt-1.5 p-1.5 bg-muted/20 rounded overflow-x-auto font-mono text-xs relative">
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-destructive/40"></div>
              <code className="whitespace-pre">{errorLinePreview}</code>
            </div>
          )}
        </div>
      </div>
    );
  };



  // State to track error details for the enhanced highlighter
  // const [errorDetails, setErrorDetails] = useState<{
  //   line: number;
  //   column: number;
  //   message: string;
  // } | null>(null); // REMOVED: Replaced by syntaxErrors for Monaco

  // Effect to validate code on initial load if there's content
  useEffect(() => {
    if (value && value.trim().length > 0) {
      validateCode(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Initial validation only, dependencies handled by direct calls


  const validateCode = useCallback(async (codeToValidate: string, explicitLanguage?: string) => {
    if (!codeToValidate.trim()) {
      setValidationResult(null);
      onErrorsChange([]); // Clear syntax errors via callback
      return;
    }

    if (validateTimeoutRef.current) {
      clearTimeout(validateTimeoutRef.current);
    }

    validateTimeoutRef.current = setTimeout(async () => {
      setIsValidating(true);
      onErrorsChange([]); // Clear previous errors before new validation
      let detectedLanguageForLlm = explicitLanguage || language || "";

      try {
        // Step 1: Initial validation for language detection, GIS check, and basic error
        const initialRequest: CodeValidationRequest = {
          code: codeToValidate,
          language: explicitLanguage || language || ""
        };
        toast.info(
          <div className="flex items-start gap-2">
            <div className="h-4 w-4 rounded-full border-2 border-blue-500 border-r-transparent animate-spin mt-0.5"></div>
            <div>
              <span className="font-medium">Validating Code (Phase 1/2)</span>
              <p className="text-xs text-muted-foreground">Syntax check & language detection...</p>
            </div>
          </div>,
          { id: 'code-validation-initial', duration: 4000 }
        );

        const initialResponse = await brain.validate_code_endpoint(initialRequest);
        const initialData: CodeValidationResponse = await initialResponse.json();
        
        detectedLanguageForLlm = initialData.detected_language || detectedLanguageForLlm;
        if (initialData.detected_language && initialData.detected_language !== "Unknown" && onLanguageChange) {
          onLanguageChange(initialData.detected_language);
        }

        let errorLineNumber: number | undefined;
        let errorColumnNumber: number | undefined;
        if (!initialData.is_valid && initialData.error_message) {
          const lineMatch = initialData.error_message.match(/line\s+(\d+)/i);
          const colMatch = initialData.error_message.match(/column\s+(\d+)/i);
          if (lineMatch) errorLineNumber = parseInt(lineMatch[1], 10);
          if (colMatch) errorColumnNumber = parseInt(colMatch[1], 10);

          // Ensure line/column numbers are at least 1, or undefined if not found
          if (errorLineNumber !== undefined && errorLineNumber < 1) errorLineNumber = 1;
          if (errorColumnNumber !== undefined && errorColumnNumber < 1) errorColumnNumber = 1;
        }

        setValidationResult({
          ...initialData,
          // Keep track of initial validation's first error, but LLM errors will be primary for display
          errorLineNumber: initialData.is_valid ? undefined : (initialData.error_line_number ? Math.max(1, initialData.error_line_number) : errorLineNumber),
          errorColumnNumber: initialData.is_valid ? undefined : (initialData.error_column_number ? Math.max(1, initialData.error_column_number) : errorColumnNumber),
        });
        toast.dismiss('code-validation-initial');

        if (initialData.is_valid) {
          toast.success(
            <div className="flex items-start gap-2">
              <CheckCircle className="flex-shrink-0 mt-0.5" size={16} />
              <div>
                <span className="font-medium">Initial Check OK</span>
                <p className="text-xs text-muted-foreground">
                  Lang: {initialData.detected_language}{initialData.is_gis_code && " (GIS)"}. Proceeding to LLM validation...
                </p>
              </div>
            </div>,
            { duration: 2000 }
          );
        } else {
          toast.warning(
            <div className="flex items-start gap-2">
              <AlertCircle className="flex-shrink-0 mt-0.5" size={16} />
              <div>
                <span className="font-medium">Initial Check Found Issues</span>
                <p className="text-xs text-muted-foreground">
                  {initialData.error_message?.substring(0,100)}... Proceeding to LLM for details.
                </p>
              </div>
            </div>,
            { duration: 3000 }
          );
        }

        // Step 2: LLM-based validation for multiple syntax errors
        toast.info(
          <div className="flex items-start gap-2">
            <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-r-transparent animate-spin mt-0.5"></div>
            <div>
              <span className="font-medium">LLM Validation (Phase 2/2)</span>
              <p className="text-xs text-muted-foreground">Detailed syntax analysis with AI...</p>
            </div>
          </div>,
          { id: 'code-validation-llm', duration: 10000 } // Longer duration for LLM
        );
        const llmRequest: LlmValidateCodeSyntaxRequest = {
          code: codeToValidate,
          language: detectedLanguageForLlm,
        };
        const llmApiResponse = await brain.llm_validate_code_syntax_endpoint(llmRequest);
        const llmData: LlmValidateCodeSyntaxResponse = await llmApiResponse.json();
        // START OF ADDED LOGGING
        console.log("LLM Validation API Raw Response (llmApiResponse):", llmApiResponse);
        console.log("LLM Validation API Parsed Data (llmData):", JSON.stringify(llmData, null, 2));
        if (llmData && typeof llmData.isValid !== 'undefined') {
          console.log("LLM Validation - llmData.isValid:", llmData.isValid);
          console.log("LLM Validation - llmData.errors:", JSON.stringify(llmData.errors, null, 2));
        } else if (llmData) {
          console.log("LLM Validation - llmData received, but isValid is undefined.");
        } else {
          console.log("LLM Validation - No llmData object after parsing JSON.");
        }
        // END OF ADDED LOGGING
        toast.dismiss('code-validation-llm');

        if (llmData.errors && llmData.errors.length > 0) {
          const editorErrors: EditorError[] = llmData.errors.map((err: SyntaxErrorDetail) => ({
            line: Math.max(1, err.line || 1), // Ensure line is at least 1
            column: Math.max(1, err.column || 1), // Ensure column is at least 1
            message: err.message,
            severity: err.severity || 'error', // Default to error if severity is missing
          }));
          onErrorsChange(editorErrors); // Update errors via callback
          toast.error(
            <div className="flex items-start gap-2">
              <AlertCircle className="flex-shrink-0 mt-0.5" size={16} />
              <div>
                <span className="font-medium">LLM Found {llmData.errors.length} Syntax Issue(s)</span>
                <p className="text-xs text-muted-foreground">
                  Check editor for details. First: {llmData.errors[0].message.substring(0,100)}...
                </p>
              </div>
            </div>,
            { duration: 5000 }
          );
          // Update overall validation result status if LLM found errors
          // The detailed errors are in syntaxErrors
          setValidationResult(prev => ({
            ...(prev || initialData || {} as CodeValidationResponse),
            isValid: false,
            // If initialData had an error_message, keep it, otherwise indicate LLM found issues.
            error_message: prev?.is_valid ? `LLM found ${llmData.errors.length} issue(s). Check details below.` : prev?.error_message || `LLM found ${llmData.errors.length} issue(s). Check details below.`
          }));

        } else {
          onErrorsChange([]); // Clear errors if LLM says it's clean via callback
          // If LLM is clean AND initial check was also okay
          if (initialData.is_valid) {
            toast.success(
              <div className="flex items-start gap-2">
                <CheckCircle className="flex-shrink-0 mt-0.5" size={16} />
                <div>
                  <span className="font-medium">LLM Confirmed: Code Looks Good!</span>
                  <p className="text-xs text-muted-foreground">
                    Language: {detectedLanguageForLlm}
                  </p>
                </div>
              </div>,
              { duration: 3000 }
            );
          }
        }

      } catch (error) {
        console.error("Error during multi-stage code validation:", error);
        toast.dismiss('code-validation-initial'); // Ensure all toasts are dismissed on error
        toast.dismiss('code-validation-llm');
        toast.error(
          <div className="flex items-start gap-2">
            <AlertTriangle className="flex-shrink-0 mt-0.5" size={16} />
            <div>
              <span className="font-medium">Validation Process Error</span>
              <p className="text-xs text-muted-foreground">
                An issue occurred. Please try again.
              </p>
            </div>
          </div>
        );
        setSyntaxErrors([]);
      } finally {
        setIsValidating(false);
      }
    }, 800); // Debounce for 800ms
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [language, onLanguageChange, onErrorsChange]); // Added onErrorsChange to dependencies

  const handleCodeChange = (newCode: string | undefined) => {
    onChange(newCode || '');
    // Clear all previous error states immediately on code change before new validation cycle
    onErrorsChange([]); // Use callback
    setValidationResult(null); 
    // Debounced validation will still pick up the new code
    validateCode(newCode || '');
  };

  // Handle file upload via dropzone
  // Enhanced drag event handlers with better visual feedback
  const onDragEnter = useCallback(() => {
    setDragHoverCount(prev => prev + 1);
  }, []);

  const onDragLeave = useCallback(() => {
    setDragHoverCount(prev => Math.max(0, prev - 1));
  }, []);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setDragHoverCount(0); // Reset drag count
      const file = acceptedFiles[0];
      if (!file) return;

      // Check file size (5MB limit)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`File size exceeds the 5MB limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
        return;
      }

      // Reset validation state for new upload
      setValidationResult(null);
      // setErrorDetails(null); // REMOVED
      onErrorsChange([]); // Clear syntax errors on new file upload via callback

      // Verify file is a text file
      const validExtensions = [
        // Python
        '.py', '.pyw', '.pyc', '.pyd', '.pyo', '.pyi',
        // JavaScript/TypeScript
        '.js', '.jsx', '.mjs', '.cjs', '.ts', '.tsx',
        // R
        '.r', '.rmd',
        // SQL
        '.sql',
        // Java
        '.java',
        // C/C++
        '.c', '.cpp', '.cc', '.h', '.hpp',
        // C#
        '.cs', 
        // Other text formats
        '.txt', '.md', '.json', '.xml', '.yaml', '.yml'
      ];

      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      let detectedLangFromExtension: string | undefined; // Variable to store lang from extension

      if (!validExtensions.includes(fileExtension)) {
        toast.warning(
          <div className="flex items-start gap-2">
            <AlertCircle className="flex-shrink-0 mt-0.5" size={16} />
            <div>
              <span className="font-medium">Unsupported file type</span>
              <p className="text-sm text-muted-foreground">
                {fileExtension} files may not be properly validated. Attempting to read as text...
              </p>
            </div>
          </div>
        );
      }

      // Store enhanced file metadata with content type detection
      const fileType = file.type || "text/plain";
      setUploadedFile({
        name: file.name,
        extension: fileExtension,
        type: fileType
      });

      // Notify parent about the file upload if callback provided
      if (onFileUploaded) {
        onFileUploaded({
          name: file.name,
          extension: fileExtension,
          type: fileType
        });
      }

      // Auto-detect language from extension AND call onLanguageChange
      // This is important for Monaco Editor syntax highlighting to update quickly
      const extensionToLanguage: Record<string, string> = {
        '.py': 'Python',
        '.js': 'JavaScript',
        '.jsx': 'JavaScript',
        '.ts': 'TypeScript',
        '.tsx': 'TypeScript',
        '.r': 'R',
        '.rmd': 'R',
        '.sql': 'SQL',
        '.java': 'Java',
        '.c': 'C',
        '.cpp': 'C++',
        '.cc': 'C++',
        '.h': 'C++',
        '.hpp': 'C++',
        '.cs': 'C#',
      };
      
      detectedLangFromExtension = extensionToLanguage[fileExtension];
      if (onLanguageChange && detectedLangFromExtension) {
        onLanguageChange(detectedLangFromExtension); // Update language in parent (Editor.tsx)
      }

      // Store file size for warning component
      setUploadedFileSize(file.size);

      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;

        // Update the code content
        onChange(content);
        // Pass the language detected from extension to validateCode
        validateCode(content, detectedLangFromExtension); // <--- WICHTIGE ÄNDERUNG HIER
        toast.success(`File "${file.name}" uploaded successfully`);
      };
      reader.onerror = () => {
        toast.error("Failed to read the file");
      };
      reader.readAsText(file);
    },
    [onChange, validateCode, onLanguageChange, onFileUploaded, language] // <--- ANGEPASSTE DEPENDENCIES
  );

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDragEnter,
    onDragLeave,
    onDrop,
    accept: {
      "text/plain": ['.txt', '.py', '.js', '.ts', '.r', '.sql', '.java', '.cpp', '.c', '.h', '.cs'],
      "application/x-python": ['.py'],
      "application/javascript": ['.js'],
      "application/x-javascript": ['.js'],
      "text/javascript": ['.js']
    },
    noClick: true,
    noKeyboard: true
  });

  // Clear file size warning after some time
  const clearFileSizeWarning = () => {
    setUploadedFileSize(null);
  };

  return (
    <div className="space-y-4" data-testid="code-uploader">
      <style jsx="true">{`
        @keyframes slideIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-slideIn {
          animation: slideIn 0.3s ease-out forwards;
        }

        /* Error highlighting styles for Ace Editor */
        :global(.ace_editor .error-line) {
          position: absolute;
          background-color: rgba(255, 0, 0, 0.08);
          z-index: 5;
          width: 100% !important;
          pointer-events: none;
        }
      `}</style>
      {/* File size warning for large files */}
      {uploadedFileSize && uploadedFileSize > 100 * 1024 && (
        <FileSizeWarning 
          fileSize={uploadedFileSize} 
          onDismiss={clearFileSizeWarning} 
          isGisCode={validationResult?.isGisCode} 
        />
      )}

      {/* Drag and drop area with glassmorphic accent - further reduced height */}
      <div
        {...(readOnly ? {} : getRootProps())}
        className={`relative border-2 border-dashed rounded-lg p-2 transition-colors flex flex-col items-center justify-center ${readOnly ? '' : 'cursor-pointer'} 
        ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50 hover:bg-muted/10'} ${readOnly ? 'opacity-70' : ''}`}
        style={{ minHeight: "60px" }}
      >
        {/* Animated Glassmorphic hexagonal patterns for background - GIS inspired */}
        <div className="absolute inset-0 overflow-hidden opacity-20 pointer-events-none transition-opacity duration-300"
             style={{
               opacity: isDragActive ? 0.35 : 0.2,
               backgroundImage: hexagonalGridBackground,
               backgroundSize: '60px 60px',
               filter: isDragActive ? 'blur(0)' : 'blur(1px)'
             }}>
          {/* Dynamic glassmorphic elements */}
          <div className="absolute -top-5 -right-5 w-24 h-24 rotate-12 opacity-20 bg-primary rounded-xl blur-xl transition-all duration-700"
               style={{
                 transform: isDragActive ? 'rotate(12deg) scale(1.2)' : 'rotate(12deg) scale(1)',
                 opacity: isDragActive ? 0.3 : 0.2
               }}></div>
          <div className="absolute bottom-3 left-10 w-16 h-16 bg-card opacity-40 rounded-lg transition-all duration-500"
               style={{
                 transform: isDragActive ? 'rotate(60deg) scale(1.1)' : 'rotate(45deg) scale(1)',
                 clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)'
               }}></div>
          <div className="absolute top-1/3 left-1/4 w-10 h-10 bg-card opacity-30 rounded-lg transition-all duration-300"
               style={{
                 transform: isDragActive ? 'rotate(60deg) scale(1.15)' : 'rotate(45deg) scale(1)',
                 clipPath: isDragActive ? 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' : 'none'
               }}></div>
          <div className="absolute top-1/2 right-1/3 w-12 h-12 bg-card opacity-20 rounded-full blur-sm transition-all duration-400"
               style={{
                 transform: isDragActive ? 'rotate(-30deg) scale(1.2)' : 'rotate(12deg) scale(1)',
                 opacity: isDragActive ? 0.4 : 0.2
               }}></div>
        </div>
        <input {...getInputProps()} />
        <div className="text-center space-y-1 flex items-center p-1">
          <div className="relative mx-auto w-10 h-10 flex items-center justify-center">
            {/* Glassmorphic hexagon background */}
            <div className="absolute w-8 h-8 bg-primary/10 rotate-[30deg] rounded-xl backdrop-blur-sm"></div>
            <div className="absolute w-8 h-8 bg-primary/5 rotate-[60deg] rounded-xl"></div>
            <Upload size={16} className="relative z-10 text-primary" />
          </div>
          <div className="flex-1 flex flex-col ml-2">
            <h3 className="text-sm font-mono tracking-wide transition-all duration-300"
                style={{
                  transform: isDragActive ? 'scale(1.05)' : 'scale(1)',
                  color: isDragActive ? 'var(--primary)' : 'inherit',
                }}>
              {isDragActive
                ? "Drop your code file here..."
                : "Drag & drop or select a file"}
            </h3>

            <div className="flex flex-wrap gap-1 mt-1 justify-start">
            <TooltipProvider>
              {['.py', '.js', '.ts', '.r', '.sql', '.java', '.cpp', '.c', '.cs'].map((ext) => (
                <Tooltip key={ext}>
                  <TooltipTrigger asChild>
                    <span className="cursor-help">
                      <FileTypeIndicator fileExtension={ext} />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-[200px]">
                    {{
                      '.py': 'Python files (.py, .pyw, .pyi)',
                      '.js': 'JavaScript files (.js, .jsx, .mjs, .cjs)',
                      '.ts': 'TypeScript files (.ts, .tsx)',
                      '.r': 'R files (.r, .rmd)',
                      '.sql': 'SQL files (.sql)',
                      '.java': 'Java files (.java)',
                      '.cpp': 'C++ files (.cpp, .cc, .h, .hpp)',
                      '.c': 'C files (.c, .h)',
                      '.cs': 'C# files (.cs)'
                    }[ext]}
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
          </div>
          </div>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={open}
            className="ml-2 font-mono tracking-wide whitespace-nowrap"
            disabled={readOnly}
          >
            <FileCode2 size={14} className="mr-1" />
            Select
          </Button>
        </div>
      </div>

      {/* Generate Documentation Button with hexagonal GIS design accents */}
      {onGenerateDocumentation && !readOnly && (
        <div className="mt-2 mb-2 relative overflow-hidden">
          <Button 
            onClick={(e) => {
              // Prevent any default behavior that might cause navigation
              e.preventDefault();
              e.stopPropagation();
              
              if (onGenerateDocumentation) {
                onGenerateDocumentation();
              }
            }} 
            disabled={!value.trim()}
            className="w-full relative overflow-hidden group transition-all duration-200 bg-gradient-to-r from-primary/80 to-primary hover:from-primary hover:to-primary/90 font-mono tracking-wide"
          >
            {/* Hexagonal accent pattern for GIS-inspired design */}
            <div className="absolute inset-0 opacity-10 pointer-events-none group-hover:opacity-20 transition-opacity duration-200 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+')]"></div>
            
            {/* Hexagonal decorative accents */}
            <div className="absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10" 
              style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }}></div>
            <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10" 
              style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }}></div>
            
            {/* Button content */}
            <span className="relative z-10 flex items-center justify-center gap-2">
              <Cpu size={16} className="mr-1" />
              Generate Documentation
            </span>
          </Button>
        </div>
      )}

      {/* Code editor */}
      <div className="relative border shadow-sm rounded-md overflow-hidden">
        <MonacoEditor
          ref={editorRef}
          value={value}
          onChange={handleCodeChange} 
          language={language}
          height={height}
          placeholder="// Or paste your code here..."
          readOnly={readOnly}
          theme={theme === 'system' ? (systemTheme === 'dark' ? 'vs-dark' : 'light') : (theme === 'dark' ? 'vs-dark' : 'light')}
          errors={errors} // Pass syntax errors to MonacoEditor from props
        />
      </div>

      {/* Validation result with improved animation */}
      {/* Validation result display - now shows LLM errors if present, or initial validation error */}
      {(validationResult || errors.length > 0) && (
        <ValidationResultDisplay 
          fileName={uploadedFile?.name || "Current Code"}
          isValid={errors.length > 0 ? false : (validationResult ? validationResult.isValid : true)}
          errors={errors.map(err => ({ line: Math.max(1, err.line), column: Math.max(1, err.column), message: err.message, severity: err.severity || 'error' }))}
          isSpecificGIS={validationResult?.is_gis_code || false} // Ensure it's always boolean
          isLoading={isValidating}
        />
      )}

      {/* Error details popup with enhanced visualization and brutalist design */}
      {/* REMOVED CodeHighlighter as Monaco now shows errors inline */}
      {/* {errorDetails && (
        <CodeHighlighter
          code={value}
          errorLine={errorDetails.line}
          errorColumn={errorDetails.column}
          language={language || validationResult?.detectedLanguage || 'plaintext'}
          onClose={() => setErrorDetails(null)}
        />
      )} */}

      {isValidating && (
        <Alert className="bg-card/80 backdrop-blur-sm border-primary/20 animate-pulse border-l-4" style={{ borderLeftColor: 'var(--primary)' }}>
          <div className="h-4 w-4 rounded-full border-2 border-primary border-r-transparent animate-spin mr-2"></div>
          <AlertTitle>Validating your code</AlertTitle>
          <AlertDescription>
            Checking syntax and detecting language...
            {language === "Python" && (
              <span className="text-xs block mt-1 text-muted-foreground">
                Auch GIS-spezifische Funktionen (wie arcpy, QGIS) werden erkannt.
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* File information display with improved GIS-inspired brutalist design */}
      {uploadedFile && (
        <div className="flex items-center gap-2 text-sm animate-slideIn p-2 bg-muted/20 rounded-md backdrop-blur-sm border-l-2 border-primary/30 relative overflow-hidden">
          {/* Brutalist angular decoration */}
          <div className="absolute -right-3 top-0 h-full w-6 bg-primary/5 skew-x-[-20deg]"></div>
          <FileCode2 size={16} className="text-muted-foreground" />
          <span className="text-muted-foreground truncate max-w-[180px]">
            {uploadedFile.name}
          </span>
          <FileTypeIndicator 
            fileExtension={uploadedFile.extension} 
            isGisCode={validationResult?.isGisCode}
          />
        </div>
      )}
    </div>
  );
}