import{u as b,U as v,_ as x}from"./index-3b130772.js";import{j as e,A as y,L as o}from"./vendor-8eb8bd34.js";import{$ as N,u as i}from"./index-fcfcda09.js";import{B as s,C as l,a as c,b as d,c as m,d as w}from"./card-b935221e.js";import{j as k}from"./index-ae1e7bbf.js";import{D as C,a as M,b as I,c as u}from"./dropdown-menu-752f7312.js";import{c as j}from"./createLucideIcon-6c39ff51.js";import"./index-89be2a26.js";import"./react-icons.esm-600f1a11.js";/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=j("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=j("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);function z(){const{setTheme:a}=k();return e.jsxs(C,{children:[e.jsx(M,{asChild:!0,children:e.jsxs(s,{variant:"outline",size:"icon",children:[e.jsx(S,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),e.jsx(D,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),e.jsxs(I,{align:"end",children:[e.jsx(u,{onClick:()=>a("light"),children:"Light"}),e.jsx(u,{onClick:()=>a("dark"),children:"Dark"}),e.jsx(u,{onClick:()=>a("system"),children:"System"})]})]})}function R(){const{user:a,loading:p}=b(),n=y(),f=async()=>{try{const{firebaseAuth:r}=await x(()=>import("./index-ca3eaa57.js"),["assets/index-ca3eaa57.js","assets/index-3b130772.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await r.signOut(),i.success("Erfolgreich abgemeldet")}catch(r){console.error("Logout failed:",r),i.error("Abmeldung fehlgeschlagen. Bitte versuche es erneut.")}};return e.jsx(v,{children:e.jsxs("div",{className:"min-h-screen bg-background bg-gis-grid overflow-x-hidden",children:[e.jsx(N,{position:"top-right",toastOptions:{className:"mt-14"}}),e.jsxs("header",{className:"relative w-full bg-gradient-to-r from-secondary to-background border-b border-border/40 glassmorphic",children:[e.jsxs("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("div",{className:"relative w-16 h-16 flex items-center justify-center",children:[e.jsx("div",{className:"absolute bg-primary/10 w-12 h-12 rotate-45 rounded-sm backdrop-blur-md border border-border/30"}),e.jsx("span",{className:"relative font-mono font-bold text-xl tracking-wider text-primary z-10",children:"CSG"})]}),e.jsx("h1",{className:"ml-3 text-xl font-mono tracking-widest text-foreground",children:"CodeScribe GIS"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-6 font-mono text-sm tracking-wide",children:[e.jsx("a",{href:"#features",className:"text-foreground hover:text-primary transition-colors",children:"Features"}),e.jsx("a",{href:"#workflow",className:"text-foreground hover:text-primary transition-colors",children:"Workflow"}),e.jsx("a",{href:"#documentation",className:"text-foreground hover:text-primary transition-colors",children:"Documentation"}),e.jsx("a",{href:"#contact",className:"text-foreground hover:text-primary transition-colors",children:"Contact"})]}),e.jsxs("div",{className:"flex space-x-3 items-center",children:[e.jsx(z,{}),e.jsx(s,{variant:"outline",size:"sm",className:"hidden font-mono tracking-wide bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-700 dark:text-yellow-300 mr-2",onClick:()=>{try{x(()=>import("./index-ca3eaa57.js"),["assets/index-ca3eaa57.js","assets/index-3b130772.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]).then(({firebaseAuth:r})=>{r?i.success("Firebase connection successful!"):i.error("Failed to connect to Firebase.")}).catch(r=>{console.error("Firebase connection test failed:",r),i.error("Firebase connection test failed. See console for details.")})}catch(r){console.error("Firebase connection test failed:",r),i.error("Firebase connection test failed. See console for details.")}},children:"Test Connection"}),e.jsx(s,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>{var r;return(r=document.getElementById("mobile-menu"))==null?void 0:r.classList.toggle("hidden")},children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16m-7 6h7"})})}),!p&&a?e.jsxs(e.Fragment,{children:[e.jsx(o,{to:"documents",children:e.jsx(s,{variant:"outline",size:"sm",className:"hidden md:inline-flex font-mono tracking-wide",children:"My Documents"})}),e.jsx(o,{to:"profile",children:e.jsx(s,{variant:"outline",size:"sm",className:"hidden md:inline-flex font-mono tracking-wide",children:"Profil"})}),e.jsx(s,{variant:"glass",size:"sm",className:"font-mono tracking-wide bg-primary/20",onClick:f,children:"Log Out"})]}):e.jsx(o,{to:"register",children:e.jsx(s,{variant:"glass",size:"sm",className:"font-mono tracking-wide bg-primary/20",children:"Sign Up"})})]})]}),e.jsx("div",{className:"absolute -bottom-6 left-0 right-0 h-12 bg-background skew-y-1 z-0"}),e.jsx("div",{id:"mobile-menu",className:"hidden absolute top-full left-0 right-0 bg-background z-50 border-b border-border/40 backdrop-blur-md",children:e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("nav",{className:"flex flex-col space-y-4 font-mono text-sm tracking-wide",children:[e.jsx("a",{href:"#features",className:"hover:text-primary transition-colors py-2 border-b border-border/20",children:"Features"}),e.jsx("a",{href:"#workflow",className:"hover:text-primary transition-colors py-2 border-b border-border/20",children:"Workflow"}),e.jsx("a",{href:"#documentation",className:"hover:text-primary transition-colors py-2 border-b border-border/20",children:"Documentation"}),e.jsx("a",{href:"#contact",className:"hover:text-primary transition-colors py-2 border-b border-border/20",children:"Contact"}),!p&&a?e.jsxs(e.Fragment,{children:[e.jsx(o,{to:"documents",className:"w-full py-2",children:e.jsx(s,{variant:"outline",size:"sm",className:"w-full font-mono tracking-wide",children:"My Documents"})}),e.jsx(o,{to:"profile",className:"w-full py-2",children:e.jsx(s,{variant:"outline",size:"sm",className:"w-full font-mono tracking-wide",children:"Profil"})}),e.jsx("div",{className:"w-full py-2",children:e.jsx(s,{variant:"glass",size:"sm",className:"w-full font-mono tracking-wide bg-primary/20",onClick:f,children:"Log Out"})})]}):e.jsxs(e.Fragment,{children:[e.jsx(o,{to:"login",className:"w-full py-2",children:e.jsx(s,{variant:"outline",size:"sm",className:"w-full font-mono tracking-wide",children:"Log In"})}),e.jsx(o,{to:"login",className:"w-full py-2",children:e.jsx(s,{variant:"glass",size:"sm",className:"w-full font-mono tracking-wide bg-primary/20",children:"Sign Up"})})]})]})})})]}),e.jsxs("main",{className:"relative container mx-auto px-4 pt-16 pb-24 z-10",children:[e.jsx("section",{className:"relative py-16",children:e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 items-center",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("h2",{className:"text-4xl md:text-5xl font-mono tracking-wider font-light leading-tight",children:["Automate Your ",e.jsx("span",{className:"text-primary font-normal",children:"GIS Script"})," Documentation"]}),e.jsx("p",{className:"text-lg text-muted-foreground",children:"Upload your code, get comprehensive documentation. CodeScribe GIS automatically extracts parameters, workflows, and metadata from your Python scripts for GIS applications."}),e.jsxs("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-4",children:[e.jsx(o,{to:"editor?new=true",onClick:r=>{if(r.preventDefault(),!a){i.error("Sie müssen angemeldet sein, um ein Dokument zu erstellen"),n("/login");return}x(()=>import("./documentStore-4a8258c1.js"),["assets/documentStore-4a8258c1.js","assets/vendor-8eb8bd34.js","assets/index-3b130772.js","assets/index-2ce5e6d7.css"]).then(async({useDocumentStore:t})=>{try{const{canCreate:h,currentCount:L,maxAllowed:g}=await t.getState().checkDocumentLimit(a.uid);h?n("/editor?new=true"):i.error(e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{children:e.jsx("strong",{children:"Dokumentenlimit erreicht!"})}),e.jsxs("p",{children:["Sie haben das Limit von ",g," Dokumenten für Ihren aktuellen Plan erreicht."]}),e.jsx("p",{className:"text-sm",children:"Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente."})]}),{duration:8e3})}catch(h){console.error("Error checking document limit:",h),n("/editor?new=true")}}).catch(t=>{console.error("Failed to import document store:",t),n("/editor?new=true")})},children:e.jsxs(s,{size:"lg",className:"relative overflow-hidden group transition-all duration-200 bg-gradient-to-r from-primary/80 to-primary hover:from-primary hover:to-primary/90 font-mono tracking-wide",children:[e.jsx("div",{className:"absolute inset-0 opacity-10 pointer-events-none group-hover:opacity-20 transition-opacity duration-200 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+')]"}),e.jsx("div",{className:"absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10",style:{clipPath:"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"}}),e.jsx("div",{className:"absolute -right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10",style:{clipPath:"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"}}),e.jsxs("span",{className:"relative z-10 flex items-center justify-center gap-2",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-cpu",children:[e.jsx("rect",{x:"4",y:"4",width:"16",height:"16",rx:"2"}),e.jsx("rect",{x:"9",y:"9",width:"6",height:"6"}),e.jsx("path",{d:"M15 2v2"}),e.jsx("path",{d:"M15 20v2"}),e.jsx("path",{d:"M2 15h2"}),e.jsx("path",{d:"M20 15h2"}),e.jsx("path",{d:"M2 9h2"}),e.jsx("path",{d:"M20 9h2"}),e.jsx("path",{d:"M9 2v2"}),e.jsx("path",{d:"M9 20v2"})]}),"Start Documenting"]})]})}),e.jsx(s,{variant:"outline",size:"lg",className:"font-mono tracking-wide",children:"Learn More"})]})]}),e.jsxs("div",{className:"relative h-[400px] md:h-[450px] rounded-lg overflow-hidden border border-border/40 shadow-lg",children:[e.jsx("div",{className:"absolute inset-0 bg-black/5 backdrop-blur-[2px]"}),e.jsxs("div",{className:"absolute inset-0 flex flex-col md:flex-row",children:[e.jsxs("div",{className:"md:w-1/2 h-1/2 md:h-full border-b md:border-b-0 md:border-r border-border/40 bg-secondary/30 backdrop-blur-sm p-4 overflow-hidden",children:[e.jsx("div",{className:"font-mono text-xs text-foreground/80 mb-2",children:"// GIS Python Script"}),e.jsx("pre",{className:"text-xs font-code text-foreground overflow-hidden h-[360px]",children:e.jsx("code",{children:`import arcpy
import os

# Define parameters
def buffer_analysis(input_fc, output_fc, buffer_distance):
    """Create buffer zones around input features.
    
    Args:
        input_fc: Input feature class
        output_fc: Output feature class
        buffer_distance: Buffer distance in meters
    """
    # Validate inputs
    if not arcpy.Exists(input_fc):
        raise ValueError("Input feature class does not exist")
    
    # Create buffer
    arcpy.analysis.Buffer(
        input_fc,
        output_fc,
        buffer_distance,
        "FULL",
        "ROUND",
        "ALL"
    )
    
    print(f"Buffer created at {output_fc}")
    return output_fc`})})]}),e.jsxs("div",{className:"md:w-1/2 h-1/2 md:h-full bg-card/70 p-4 overflow-hidden text-foreground",children:[e.jsx("h3",{className:"text-sm font-semibold mb-3 font-mono tracking-wider",children:"DOCUMENTATION PREVIEW"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-semibold text-muted-foreground",children:"FUNCTION"}),e.jsx("p",{className:"text-sm",children:"buffer_analysis"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-semibold text-muted-foreground",children:"DESCRIPTION"}),e.jsx("p",{className:"text-sm",children:"Create buffer zones around input features."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-semibold text-muted-foreground",children:"PARAMETERS"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"input_fc:"})," Input feature class"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"output_fc:"})," Output feature class"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"buffer_distance:"})," Buffer distance in meters"]})]})]})]})]})]})]})]})}),e.jsxs("section",{id:"features",className:"py-24 relative",children:[e.jsx("div",{className:"absolute inset-0 z-0 opacity-5",children:Array.from({length:6}).map((r,t)=>e.jsx("div",{className:"absolute w-32 h-32 rotate-45 border border-primary",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,opacity:.1+Math.random()*.2}},t))}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-mono tracking-wider font-light mb-4",children:"Key Features"}),e.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"CodeScribe GIS provides comprehensive documentation automation for your GIS scripts, saving you hours of manual documentation work."})]}),e.jsx("div",{className:"grid md:grid-cols-3 gap-8",children:[{title:"Automatic Parameter Extraction",description:"Identifies input/output parameters, their types, and requirements directly from your code."},{title:"Workflow Visualization",description:"Generates interactive diagrams that visualize your script's data flow and processing steps."},{title:"Code Parsing & Analysis",description:"Extracts metadata, descriptions, and functional details from your GIS scripts."},{title:"Split-Pane Interface",description:"View your code and generated documentation side-by-side for real-time feedback."},{title:"Multiple Format Export",description:"Export your documentation in Markdown, HTML, or PDF formats for various use cases."},{title:"Version Tracking",description:"Automatically tracks code versions and changes to provide historical documentation."}].map((r,t)=>e.jsxs(l,{className:"border-border/40 glassmorphic-hover transition-colors",children:[e.jsxs(c,{children:[e.jsxs("div",{className:"w-12 h-12 flex items-center justify-center mb-2",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:t+1})]}),e.jsx(d,{className:"font-mono tracking-wide",children:r.title})]}),e.jsx(m,{children:e.jsx(w,{className:"text-muted-foreground",children:r.description})})]},t))})]})]}),e.jsxs("section",{id:"workflow",className:"py-24 relative",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-mono tracking-wider font-light mb-4",children:"Documentation Workflow"}),e.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Simply upload your code and let CodeScribe GIS handle the rest. Our intuitive process makes documentation effortless."})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-1/2 top-8 bottom-8 w-0.5 bg-border z-0 hidden md:block"}),e.jsx("div",{className:"space-y-16 relative z-10",children:[{title:"Upload Your Code",description:"Paste your code or upload script files directly. We support Python and other languages used in GIS applications."},{title:"Automatic Analysis",description:"Our system parses your code, identifying parameters, functions, workflows, and embedded documentation."},{title:"Review & Edit",description:"Review the generated documentation in the split-pane interface and make any necessary adjustments."},{title:"Export & Share",description:"Export your documentation in your preferred format and share it with your team or clients."}].map((r,t)=>e.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-4 md:gap-12",children:[e.jsx("div",{className:`md:w-1/2 ${t%2===1?"md:order-2":""}`,children:e.jsxs(l,{className:"border-border/40 glassmorphic",children:[e.jsxs(c,{children:[e.jsxs("div",{className:"w-12 h-12 flex items-center justify-center mb-4",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:t+1})]}),e.jsx(d,{className:"font-mono tracking-wide",children:r.title})]}),e.jsx(m,{children:e.jsx("p",{className:"text-muted-foreground",children:r.description})})]})}),e.jsx("div",{className:"relative w-12 h-12 rounded-full border-2 border-border bg-card flex items-center justify-center md:mx-12",children:e.jsx("div",{className:"w-4 h-4 rounded-sm rotate-45 bg-primary/80"})})]},t))})]})]}),e.jsx("section",{id:"documentation",className:"py-24",children:e.jsxs("div",{className:"relative overflow-hidden rounded-lg border border-border/40 bg-gradient-to-br from-primary/5 to-secondary/20 glassmorphic p-12",children:[Array.from({length:4}).map((r,t)=>e.jsx("div",{className:"absolute w-32 h-32 rotate-45 border border-primary/30",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,opacity:.05+Math.random()*.1}},t)),e.jsxs("div",{className:"relative z-10 text-center max-w-3xl mx-auto",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-mono tracking-wider font-light mb-6",children:"Start Documenting Your GIS Code Today"}),e.jsx("p",{className:"text-lg text-muted-foreground mb-8",children:"Save hours of manual documentation work and ensure your GIS scripts are well-documented for your team, clients, and future reference."}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[e.jsx(s,{size:"lg",className:"font-mono tracking-wide",children:"Sign Up for Free"}),e.jsx(s,{variant:"glass",size:"lg",className:"font-mono tracking-wide bg-primary/20",children:"Watch Demo"})]})]})]})})]}),e.jsx("section",{id:"contact",className:"py-24 bg-card/20",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-mono tracking-wider font-light mb-4",children:"Contact Us"}),e.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Have questions about CodeScribe GIS? We're here to help. Reach out to our team for more information."})]}),e.jsxs("div",{className:"max-w-3xl mx-auto grid md:grid-cols-2 gap-8",children:[e.jsxs(l,{className:"border-border/40 glassmorphic",children:[e.jsxs(c,{children:[e.jsxs("div",{className:"w-12 h-12 flex items-center justify-center mb-2",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})]}),e.jsx(d,{className:"font-mono tracking-wide",children:"Email Us"})]}),e.jsxs(m,{children:[e.jsx("p",{className:"text-muted-foreground mb-4",children:"For general inquiries, support questions, or more information about our services."}),e.jsx(s,{variant:"outline",className:"w-full font-mono tracking-wide",children:"<EMAIL>"})]})]}),e.jsxs(l,{className:"border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(c,{children:[e.jsxs("div",{className:"w-12 h-12 flex items-center justify-center mb-2",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"})})})]}),e.jsx(d,{className:"font-mono tracking-wide",children:"Live Chat"})]}),e.jsxs(m,{children:[e.jsx("p",{className:"text-muted-foreground mb-4",children:"Chat with our support team in real-time for immediate assistance with your questions."}),e.jsx(s,{className:"w-full font-mono tracking-wide",children:"Start a Conversation"})]})]})]})]})}),e.jsx("footer",{className:"border-t border-border/40 bg-secondary/20 glassmorphic py-12",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsxs("div",{className:"relative w-8 h-8 flex items-center justify-center",children:[e.jsx("div",{className:"absolute bg-primary/10 w-8 h-8 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative font-mono font-bold text-sm tracking-wider text-primary z-10",children:"CSG"})]}),e.jsx("h3",{className:"ml-2 text-sm font-mono tracking-widest",children:"CodeScribe GIS"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Automated documentation generator for GIS scripts and applications."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-mono tracking-wider text-sm font-medium mb-4",children:"Product"}),e.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[e.jsx("li",{children:e.jsx("a",{href:"#features",className:"hover:text-primary transition-colors",children:"Features"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Pricing"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Documentation"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Release Notes"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-mono tracking-wider text-sm font-medium mb-4",children:"Company"}),e.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"About Us"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Careers"})}),e.jsx("li",{children:e.jsx("a",{href:"#contact",className:"hover:text-primary transition-colors",children:"Contact"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Privacy Policy"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-mono tracking-wider text-sm font-medium mb-4",children:"Connect"}),e.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Twitter"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"GitHub"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"LinkedIn"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"hover:text-primary transition-colors",children:"Discord Community"})})]})]})]}),e.jsx("div",{className:"border-t border-border/40 mt-12 pt-8 text-center text-sm text-muted-foreground",children:e.jsxs("p",{children:["© ",new Date().getFullYear()," CodeScribe GIS. All rights reserved."]})})]})}),e.jsxs("a",{href:"#",className:"fixed bottom-6 right-6 w-12 h-12 flex items-center justify-center group z-50",onClick:r=>{r.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})},children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm backdrop-blur-md border border-border/30 group-hover:bg-primary/20 transition-colors"}),e.jsx("span",{className:"relative z-10 text-foreground",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 15l7-7 7 7"})})})]})]})})}export{R as default};
