/**
 * Utility functions for exporting documents in different formats
 */

// Function to export document as Markdown
export const exportAsMarkdown = (document: any): string => {
  if (!document) {
    throw new Error('Document is required');
  }

  // Check if document.content exists
  if (!document.content) {
    console.error('Document content is undefined');
    document.content = { originalCode: '', generatedDocumentation: null };
  }

  // Parse the generated documentation if it exists
  let docData;
  try {
    if (document.content.generatedDocumentation) {
      console.log('Parsing documentation JSON in HTML export');
      try {
        docData = JSON.parse(document.content.generatedDocumentation);
        console.log('Successfully parsed docData:', docData);
      } catch (parseErr) {
        console.error('Failed to parse documentation JSON:', parseErr);
        console.error('Raw content:', document.content.generatedDocumentation);
        // Try to extract any valid JSON structure
        try {
          const cleaned = document.content.generatedDocumentation.trim()
            .replace(/^['"`]/, '')
            .replace(/['"`]$/, '');
          docData = JSON.parse(cleaned);
          console.log('Successfully parsed cleaned docData:', docData);
        } catch (cleanErr) {
          console.error('Failed to parse cleaned documentation JSON:', cleanErr);
          docData = null;
        }
      }
    } else {
      console.warn('No generatedDocumentation found in document content');
      docData = null;
    }
  } catch (err) {
    console.error('Error parsing documentation:', err);
    console.error('Raw content:', document.content.generatedDocumentation);
    docData = null;
  }

  // Start with document title and description
  let markdown = `# ${document.title}\n\n`;
  
  if (document.description) {
    markdown += `${document.description}\n\n`;
  }

  // Add metadata section
  markdown += `## Metadata\n\n`;
  markdown += `- **Language**: ${document.language}\n`;
  markdown += `- **Created**: ${document.createdAt.toLocaleString()}\n`;
  markdown += `- **Last Updated**: ${document.updatedAt.toLocaleString()}\n`;
  
  if (document.tags && document.tags.length > 0) {
    markdown += `- **Tags**: ${document.tags.join(', ')}\n`;
  }
  markdown += `\n`;

  // Add generated documentation if available
  if (docData) {
    // Add textual description
    if (docData.description) {
      markdown += `## Description\n\n${docData.description}\n\n`;
    }

    // Add parameters table if available
    if (docData.parameters && docData.parameters.length > 0) {
      markdown += `## Parameters\n\n`;
      markdown += `| Name | Type | Required | Description |\n`;
      markdown += `|------|------|----------|-------------|\n`;
      
      docData.parameters.forEach((param: any) => {
        markdown += `| ${param.name} | ${param.type} | ${param.required ? 'Yes' : 'No'} | ${param.description} |\n`;
      });
      
      markdown += `\n`;
    }

    // Add tools and functions section if available
    if (docData.toolsUsed && docData.toolsUsed.length > 0) {
      markdown += `## Functions and Tools\n\n`;
      docData.toolsUsed.forEach((tool: any) => {
        if (typeof tool === 'string') {
          // Handle case where toolsUsed is an array of strings
          markdown += `- ${tool}\n`;
        } else if (typeof tool === 'object' && tool !== null) {
          // Handle case where toolsUsed is an array of objects
          markdown += `### ${tool.name || 'Unnamed Tool'}\n\n`;
          if (tool.description) {
            markdown += `${tool.description}\n\n`;
          }
        }
      });
      markdown += `\n`;
    }

    // Add workflow description if available
    if (docData.workflow) {
      markdown += `## Workflow\n\n`;
      markdown += `${docData.workflow.description || 'No workflow description available.'}\n\n`;
      
      if (docData.workflow.steps && docData.workflow.steps.length > 0) {
        docData.workflow.steps.forEach((step: any, index: number) => {
          markdown += `### Step ${index + 1}: ${step.name}\n\n`;
          markdown += `${step.description}\n\n`;
        });
      }
    }
  }

  // Add original code section
  markdown += `## Original Code\n\n`;
  markdown += '```' + document.language.toLowerCase() + '\n';
  markdown += document.content.originalCode || 'No code available';
  markdown += '\n```\n';

  return markdown;
};

// Function to export document as HTML
export const exportAsHTML = (document: any): string => {
  if (!document) {
    throw new Error('Document is required');
  }

  // Check if document.content exists
  if (!document.content) {
    console.error('Document content is undefined');
    document.content = { originalCode: '', generatedDocumentation: null };
  }

  // Parse the generated documentation if it exists
  let docData;
  try {
    docData = document.content.generatedDocumentation 
      ? JSON.parse(document.content.generatedDocumentation) 
      : null;
  } catch (err) {
    console.error('Error parsing documentation:', err);
    docData = null;
  }

  // Create HTML template with styling to match the document preview exactly
  // This ensures exported documents look the same as they do in the app
  // Force light theme for all exports to ensure consistent appearance
  const forceLight = true;
  
  // Check if we already have light theme variables in the document
  const hasLightTheme = document.content.hasLightThemeForExport;
  
  // If we don't have light theme variables yet, we need to add them
  if (!hasLightTheme) {
    console.log('Adding light theme variables to document');
    // Mark that we've added light theme variables to avoid duplication
    document.content.hasLightThemeForExport = true;
  }
  
  let html = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <meta name="color-scheme" content="light">
    <title>${document.title} - CodeScribe GIS Documentation</title>
    <style>
      /* CSS Variables for theming */
      :root {
        --primary: #3b82f6;
        --primary-foreground: #ffffff;
        --background: #121212;
        --foreground: #ffffff;
        --muted: #374151;
        --muted-foreground: #9ca3af;
        --border: #2e2e2e;
        --card: rgba(30, 30, 30, 0.8);
        --accent: rgba(59, 130, 246, 0.7);
        --font-mono: 'Courier New', monospace;
        --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      /* Base styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: var(--font-sans);
        background-color: var(--background);
        color: var(--foreground);
        line-height: 1.6;
        min-height: 100vh;
        padding-bottom: 5rem;
        background-image: radial-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 0);
        background-size: 30px 30px;
        background-position: -11px -11px;
      }
      
      /* Container with functional brutalist style */
      .container {
        max-width: 1100px;
        margin: 0 auto;
        padding: 0 1.5rem;
      }
      
      /* Header with angled cut at bottom */
      header {
        position: relative;
        background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(30, 30, 30, 0.5));
        padding: 3rem 0 2rem;
        margin-bottom: 4rem;
        border-bottom: 1px solid var(--border);
        clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
      }
      
      header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none"><path d="M20 0L40 20L20 40L0 20L20 0Z" fill="rgba(59, 130, 246, 0.03)"/></svg>');
        background-size: 40px 40px;
        z-index: -1;
      }
      
      /* Typography */
      h1, h2, h3, h4 {
        font-family: var(--font-mono);
        letter-spacing: 0.05em;
        font-weight: 500;
        line-height: 1.2;
      }
      
      h1 {
        font-size: 2.5rem;
        letter-spacing: 0.1em;
        margin-bottom: 0.8rem;
        position: relative;
        display: inline-block;
        color: var(--primary-foreground);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      h1::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 60%;
        height: 3px;
        background-color: var(--primary);
        opacity: 0.7;
        transform: skewX(-12deg);
      }
      
      h2 {
        font-size: 1.75rem;
        margin: 2.5rem 0 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--border);
        position: relative;
        display: inline-block;
      }
      
      h2::before {
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        width: 10px;
        height: 10px;
        background-color: var(--primary);
        opacity: 0.7;
        transform: translateY(-50%) rotate(45deg);
      }
      
      h3 {
        font-size: 1.35rem;
        margin: 1.5rem 0 0.75rem;
        color: var(--primary);
      }
      
      p {
        margin-bottom: 1.25rem;
        font-size: 1.05rem;
      }
      
      /* Card with glassmorphism */
      .card {
        background-color: var(--card);
        border-radius: 6px;
        border: 1px solid var(--border);
        padding: 1.5rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
      
      .card::before {
        content: '';
        position: absolute;
        top: -10%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: radial-gradient(var(--primary), transparent 70%);
        opacity: 0.05;
        border-radius: 100%;
        z-index: -1;
      }
      
      /* Metadata section */
      .metadata {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
      }
      
      .metadata-item {
        padding: 0.75rem;
        background-color: rgba(59, 130, 246, 0.07);
        border-radius: 4px;
      }
      
      .metadata-label {
        display: block;
        font-weight: bold;
        font-family: var(--font-mono);
        color: var(--primary);
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
      }
      
      .metadata-value {
        font-family: var(--font-sans);
      }
      
      /* Table styles with brutalist angular design */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0 2.5rem;
        background-color: var(--card);
        border: 1px solid var(--border);
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        position: relative;
      }
      
      /* Hexagonal background pattern for brutalist GIS-inspired design */
      table::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0.03;
        z-index: 0;
        background-image: repeating-linear-gradient(120deg, var(--primary) 0, var(--primary) 1px, transparent 1px, transparent 60px),
                          repeating-linear-gradient(60deg, var(--primary) 0, var(--primary) 1px, transparent 1px, transparent 60px),
                          repeating-linear-gradient(0deg, var(--primary) 0, var(--primary) 1px, transparent 1px, transparent 60px);
        background-size: 70px 120px;
        pointer-events: none;
      }
      
      thead {
        position: relative;
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 2px solid var(--primary);
      }
      
      th {
        text-align: left;
        padding: 1rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        font-family: var(--font-mono);
        font-size: 0.95rem;
        color: var(--primary);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
      }
      
      /* Angled header background for brutalist style */
      th::before {
        content: '';
        position: absolute;
        top: 0;
        left: -5px;
        width: calc(100% + 10px);
        height: 100%;
        background-color: rgba(var(--primary-rgb), 0.08);
        transform: skew(-15deg);
        transform-origin: left;
        z-index: 0;
      }
      
      th > * {
        position: relative;
        z-index: 1;
      }
      
      td {
        text-align: left;
        padding: 0.9rem 1rem;
        border-bottom: 1px solid var(--border);
        position: relative;
        background-color: transparent;
        z-index: 1;
      }
      
      /* Parameter type badges with angular cuts */
      .param-type {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        font-family: var(--font-mono);
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
        border-radius: 0;
        position: relative;
        clip-path: polygon(0 0, 100% 0, 95% 100%, 5% 100%);
      }
      
      /* Required parameter indicator */
      .param-required {
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: var(--primary);
        margin-left: 0.5rem;
        transform: rotate(45deg);
      }
      
      /* Zebra striping for rows with angled gradient */
      tbody tr:nth-child(even) {
        background-color: rgba(var(--primary-rgb), 0.02);
        position: relative;
      }
      
      tbody tr:nth-child(even)::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(105deg, transparent 0%, rgba(var(--primary-rgb), 0.03) 100%);
        pointer-events: none;
      }
      
      /* Workflow with vertical timeline */
      .workflow-steps {
        position: relative;
        margin: 2rem 0;
        padding-left: 2.5rem;
        border-left: 2px solid var(--primary);
      }
      
      .workflow-step {
        position: relative;
        margin-bottom: 3rem;
        padding: 1.5rem;
        background-color: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(5px);
        border: 1px solid var(--border);
        border-radius: 4px;
      }
      
      .workflow-step::before {
        content: '';
        position: absolute;
        left: -2.9rem;
        top: 1.5rem;
        width: 22px;
        height: 22px;
        background-color: var(--background);
        border: 2px solid var(--primary);
        border-radius: 0;
        z-index: 1;
        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      }
      
      .workflow-step::after {
        content: '';
        position: absolute;
        left: -1.25rem;
        top: 1.8rem;
        width: 1.25rem;
        height: 2px;
        background-color: var(--primary);
        z-index: 0;
      }
      
      .workflow-step h3 {
        font-family: var(--font-mono);
        font-size: 1.2rem;
        margin-top: 0;
        margin-bottom: 0.75rem;
        display: inline-block;
        padding-left: 0.75rem;
        border-left: 3px solid var(--primary);
      }
      
      /* Step number indicator */
      .step-number {
        position: absolute;
        top: -1rem;
        right: 1rem;
        font-family: var(--font-mono);
        font-size: 3rem;
        font-weight: bold;
        color: var(--primary);
        opacity: 0.1;
      }
      
      /* Code section */
      .code-section {
        position: relative;
      }
      
      pre {
        background-color: rgba(0, 0, 0, 0.4);
        padding: 1.5rem;
        border-radius: 4px;
        overflow-x: auto;
        margin: 1rem 0 2rem;
        border-left: 3px solid var(--primary);
        font-size: 0.9rem;
        line-height: 1.5;
        max-height: 600px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
      }
      
      code {
        font-family: var(--font-mono);
        color: var(--foreground);
      }
      
      /* Tags */
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.25rem;
      }
      
      .tag {
        background-color: rgba(59, 130, 246, 0.1);
        padding: 0.25rem 0.6rem;
        border-radius: 3px;
        font-size: 0.8rem;
        display: inline-block;
        font-family: var(--font-mono);
        border: 1px solid rgba(59, 130, 246, 0.2);
      }
      
      /* Function items with brutalist design */
      .functions-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        position: relative;
      }
      
      .function-item {
        padding: 1.25rem;
        margin-bottom: 0.5rem;
        background-color: rgba(255, 255, 255, 0.03);
        border-radius: 0;
        position: relative;
        transition: transform 0.2s ease-out;
        backdrop-filter: blur(3px);
        border: 1px solid var(--border);
        overflow: hidden;
      }
      
      /* Brutalist decorative corner cut */
      .function-item::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        border-style: solid;
        border-width: 0 24px 24px 0;
        border-color: transparent var(--primary) transparent transparent;
        opacity: 0.2;
      }
      
      .function-item h3 {
        font-family: var(--font-mono);
        font-size: 1rem;
        margin-top: 0;
        margin-bottom: 0.5rem;
        padding-left: 0.75rem;
        border-left: 3px solid var(--primary);
        line-height: 1.5;
      }
      
      /* Responsive adjustments for function items */
      @media (max-width: 768px) {
        .functions-list {
          grid-template-columns: 1fr;
        }
      }
      
      /* Description section with special formatting */
      .description {
        background-color: rgba(59, 130, 246, 0.02);
        padding: 1.25rem;
        border-radius: 4px;
        border: 1px solid rgba(59, 130, 246, 0.1);
        font-size: 1.05rem;
        line-height: 1.7;
      }
      
      /* Footer */
      footer {
        margin-top: 3rem;
        padding: 2rem 0;
        text-align: center;
        font-size: 0.85rem;
        color: var(--muted-foreground);
        border-top: 1px solid var(--border);
      }
      
      /* Light mode theme - always applied for exports */
      :root {
        --primary: #2563eb;
        --primary-foreground: #ffffff;
        --background: #f8fafc;
        --foreground: #1e293b;
        --muted: #f1f5f9;
        --muted-foreground: #64748b;
        --border: #e2e8f0;
        --card: rgba(255, 255, 255, 0.8);
      }
      
      body {
        background-color: var(--background);
        color: var(--foreground);
        background-image: radial-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 0);
      }
      
      pre {
        background-color: rgba(0, 0, 0, 0.03);
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
      }
      
      code {
        color: #334155;
      }
      
      h1 {
        color: #1e293b;
        text-shadow: none;
      }
      
      /* Responsive adjustments */
      @media (max-width: 768px) {
        h1 {
          font-size: 2rem;
        }
        
        h2 {
          font-size: 1.5rem;
        }
        
        .metadata {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="container">
        <h1>${document.title}</h1>
        ${document.description ? `<p class="header-description">${document.description}</p>` : ''}
      </div>
    </header>
    
    <main class="container">
      <!-- Metadata Card -->
      <div class="card">
        <h2>Metadata</h2>
        <div class="metadata">
          <div class="metadata-item">
            <span class="metadata-label">LANGUAGE</span>
            <span class="metadata-value">${document.language || 'Not specified'}</span>
          </div>
          <div class="metadata-item">
            <span class="metadata-label">CREATED</span>
            <span class="metadata-value">${document.createdAt?.toLocaleString() || 'Unknown'}</span>
          </div>
          <div class="metadata-item">
            <span class="metadata-label">LAST UPDATED</span>
            <span class="metadata-value">${document.updatedAt?.toLocaleString() || 'Unknown'}</span>
          </div>
          ${document.tags && document.tags.length > 0 ? 
            `<div class="metadata-item">
              <span class="metadata-label">TAGS</span>
              <div class="tag-list">
                ${document.tags.map((tag: string) => `<span class="tag">${tag}</span>`).join('')}
              </div>
            </div>` : ''
          }
        </div>
      </div>
`;

  // Add generated documentation if available
  if (docData) {
    // Add textual description
    if (docData.textualDescription || docData.description) {
      const description = docData.textualDescription || docData.description;
      if (description) {
        console.log('Adding description to HTML:', description);
        html += `
        <!-- Description Card -->
        <div class="card">
          <h2>Description</h2>
          <div class="description">
            ${description.replace(/\n/g, '<br>')}
          </div>
        </div>
        `;
      }
    }

    // Add parameters table if available
    if (docData.parameters && docData.parameters.length > 0) {
      html += `
      <!-- Parameters Card with brutalist design -->
      <div class="card">
        <h2>Parameters</h2>
        <table>
          <thead>
            <tr>
              <th><div>Name</div></th>
              <th><div>Type</div></th>
              <th><div>Required</div></th>
              <th><div>Description</div></th>
              <th><div>Constraints</div></th>
            </tr>
          </thead>
          <tbody>
      `;
      
      docData.parameters.forEach((param: any) => {
        html += `
            <tr>
              <td class="font-medium">${param.name || ''}</td>
              <td>${param.type || 'Unknown'}</td>
              <td>${param.required ? 
                '<span style="display:inline-block;padding:0.25rem 0.6rem;background-color:rgba(59,130,246,0.1);color:var(--primary);font-size:0.8rem;border-radius:3px;font-weight:500;">Required</span>' : 
                '<span style="display:inline-block;padding:0.25rem 0.6rem;background-color:rgba(100,100,100,0.1);color:var(--muted-foreground);font-size:0.8rem;border-radius:3px;font-weight:500;">Optional</span>'}</td>
              <td>${param.description || ''}</td>
              <td>${param.constraints || 'None'}</td>
            </tr>
        `;
      });
      
      html += `
          </tbody>
        </table>
      </div>
      `;
    }

    // Add tools and functions section if available
    const toolsUsed = Array.isArray(docData.toolsUsed) ? docData.toolsUsed : [];
    if (toolsUsed.length > 0) {
      html += `
      <!-- Functions and Tools Card -->
      <div class="card">
        <h2>Functions and Tools</h2>
        <div class="functions-list">
      `;
      
      toolsUsed.forEach((tool: any) => {
        // Handle both string items and object items with name/description
        if (typeof tool === 'string') {
          html += `
          <div class="function-item">
            <h3>${tool}</h3>
          </div>
          `;
        } else {
          html += `
          <div class="function-item">
            <h3>${tool.name || 'Unnamed Function'}</h3>
            ${tool.description ? `<p>${tool.description}</p>` : ''}
          </div>
          `;
        }
      });
      
      html += `
        </div>
      </div>
      `;
    }

    // Add workflow description if available
    if (docData.workflow) {
      html += `
      <!-- Workflow Card -->
      <div class="card">
        <h2>Workflow</h2>
        <p>${docData.workflow.description || 'This script follows a structured workflow with multiple steps.'}</p>
      `;
      
      if (docData.workflow.steps && docData.workflow.steps.length > 0) {
        html += `<div class="workflow-steps">`;
        
        docData.workflow.steps.forEach((step: any, index: number) => {
          html += `
          <div class="workflow-step">
            <div class="step-number">${index + 1}</div>
            <h3>${step.name || `Step ${index + 1}`}</h3>
            <p>${step.description || 'No description available for this step.'}</p>
          </div>
          `;
        });
        
        html += `</div>`;
      }
      
      html += `
      </div>
      `;
    }
  }

  // Add original code section
  html += `
  <!-- Code Card -->
  <div class="card code-section">
    <h2>Original Code</h2>
    <pre><code>${(document.content.originalCode || 'No code available').replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
  </div>
  
  <footer>
    <p>Generated by CodeScribe GIS Documentation Generator</p>
  </footer>
  </main>
  </body>
  </html>
  `;

  return html;
};

// Function to export document as plain text
export const exportAsText = (document: any): string => {
  if (!document) {
    throw new Error('Document is required');
  }

  // Check if document.content exists
  if (!document.content) {
    console.error('Document content is undefined');
    document.content = { originalCode: '', generatedDocumentation: null };
  }

  // Parse the generated documentation if it exists
  let docData;
  try {
    docData = document.content.generatedDocumentation 
      ? JSON.parse(document.content.generatedDocumentation) 
      : null;
  } catch (err) {
    console.error('Error parsing documentation:', err);
    docData = null;
  }

  // Start with document title and description
  let text = `${document.title}\n\n`;
  
  if (document.description) {
    text += `${document.description}\n\n`;
  }

  // Add metadata section
  text += `METADATA\n========\n`;
  text += `Language: ${document.language}\n`;
  text += `Created: ${document.createdAt.toLocaleString()}\n`;
  text += `Last Updated: ${document.updatedAt.toLocaleString()}\n`;
  
  if (document.tags && document.tags.length > 0) {
    text += `Tags: ${document.tags.join(', ')}\n`;
  }
  text += `\n`;

  // Add generated documentation if available
  if (docData) {
    // Add textual description
    if (docData.description) {
      text += `DESCRIPTION\n===========\n${docData.description}\n\n`;
    }

    // Add parameters table if available
    if (docData.parameters && docData.parameters.length > 0) {
      text += `PARAMETERS\n==========\n`;
      
      docData.parameters.forEach((param: any) => {
        text += `Name: ${param.name}\n`;
        text += `Type: ${param.type}\n`;
        text += `Required: ${param.required ? 'Yes' : 'No'}\n`;
        text += `Description: ${param.description}\n\n`;
      });
    }

    // Add tools and functions section if available
    if (docData.toolsUsed && docData.toolsUsed.length > 0) {
      text += `FUNCTIONS AND TOOLS\n==================\n`;
      docData.toolsUsed.forEach((tool: any) => {
        if (typeof tool === 'string') {
          // Handle case where toolsUsed is an array of strings
          text += `- ${tool}\n`;
        } else if (typeof tool === 'object' && tool !== null) {
          // Handle case where toolsUsed is an array of objects
          text += `${tool.name || 'Unnamed Tool'}\n`;
          if (tool.description) {
            text += `${tool.description}\n\n`;
          }
        }
      });
      text += `\n`;
    }

    // Add workflow description if available
    if (docData.workflow) {
      text += `WORKFLOW\n========\n`;
      text += `${docData.workflow.description || 'No workflow description available.'}\n\n`;
      
      if (docData.workflow.steps && docData.workflow.steps.length > 0) {
        docData.workflow.steps.forEach((step: any, index: number) => {
          text += `Step ${index + 1}: ${step.name}\n`;
          text += `${step.description}\n\n`;
        });
      }
    }
  }

  // Add original code section
  text += `ORIGINAL CODE\n=============\n`;
  text += document.content.originalCode || 'No code available';

  return text;
};

// Function to generate document as Word (DOCX) using HTML conversion
export const generateDocx = async (document: any): Promise<Blob> => {
  // Make a deep copy of the document to avoid modifying the original
  const documentCopy = JSON.parse(JSON.stringify(document));
  
  // Ensure all nested objects are properly structured to avoid 'str' object has no attribute 'get' error
  if (documentCopy.content && documentCopy.content.generatedDocumentation) {
    try {
      // Parse the documentation if it's a string
      let docData = documentCopy.content.generatedDocumentation;
      if (typeof docData === 'string') {
        docData = JSON.parse(docData);
      }
      
      // Ensure parameters are properly structured
      if (docData.parameters) {
        // If parameters is a string, try to parse it
        if (typeof docData.parameters === 'string') {
          try {
            docData.parameters = JSON.parse(docData.parameters);
          } catch (e) {
            console.warn('Could not parse parameters string to JSON, keeping as string');
          }
        }
        
        // Ensure each parameter has get() compatible structure
        if (Array.isArray(docData.parameters)) {
          docData.parameters = docData.parameters.map(param => {
            if (typeof param === 'string') {
              return { name: param, type: 'Unknown', required: false, description: '' };
            }
            return param;
          });
        }
      }
      
      // Update the document with the properly structured data
      documentCopy.content.generatedDocumentation = JSON.stringify(docData);
    } catch (e) {
      console.error('Error preparing document for DOCX export:', e);
    }
  }
  
  // Using the HTML content as a base and convert it to DOCX format
  const html = exportAsHTML(documentCopy);
  
  // Create a wrapper object with the HTML content and document metadata
  const data = {
    title: documentCopy.title,
    html: html,
    metadata: {
      language: documentCopy.language,
      created: documentCopy.createdAt.toLocaleString(),
      updated: documentCopy.updatedAt.toLocaleString(),
      tags: documentCopy.tags || []
    }
  };
  
  // Convert to JSON string
  const jsonString = JSON.stringify(data);
  
  // Create a Blob with the JSON data
  return new Blob([jsonString], { type: 'application/json' });
};

// Function to generate document as PDF
export const generatePdf = async (document: any): Promise<Blob> => {
  // Make a deep copy of the document to avoid modifying the original
  const documentCopy = JSON.parse(JSON.stringify(document));
  
  // Set a flag to indicate that this is for PDF export
  // This will be used in exportAsHTML to ensure light theme styling
  documentCopy.forPdfExport = true;
  
  // Ensure all nested objects are properly structured to avoid errors
  if (documentCopy.content && documentCopy.content.generatedDocumentation) {
    try {
      // Parse the documentation if it's a string
      let docData = documentCopy.content.generatedDocumentation;
      if (typeof docData === 'string') {
        docData = JSON.parse(docData);
      }
      
      // Ensure parameters are properly structured
      if (docData.parameters) {
        // If parameters is a string, try to parse it
        if (typeof docData.parameters === 'string') {
          try {
            docData.parameters = JSON.parse(docData.parameters);
          } catch (e) {
            console.warn('Could not parse parameters string to JSON, keeping as string');
          }
        }
        
        // Ensure each parameter has proper structure
        if (Array.isArray(docData.parameters)) {
          docData.parameters = docData.parameters.map(param => {
            if (typeof param === 'string') {
              return { name: param, type: 'Unknown', required: false, description: '' };
            }
            return param;
          });
        }
      }
      
      // Update the document with the properly structured data
      documentCopy.content.generatedDocumentation = JSON.stringify(docData);
    } catch (e) {
      console.error('Error preparing document for PDF export:', e);
    }
  }
  
  const html = exportAsHTML(documentCopy);
  
  // Create a wrapper object with the HTML content and document metadata
  const data = {
    title: document.title,
    html: html,
    metadata: {
      language: document.language,
      created: document.createdAt.toLocaleString(),
      updated: document.updatedAt.toLocaleString(),
      tags: document.tags || []
    }
  };
  
  // Convert to JSON string
  const jsonString = JSON.stringify(data);
  
  // Create a Blob with the JSON data
  return new Blob([jsonString], { type: 'application/json' });
};

// Function to download content as a file
export const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

/**
 * Helper function to download a blob
 * @param blob The blob to download
 * @param filename The filename
 */
export const downloadBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}








