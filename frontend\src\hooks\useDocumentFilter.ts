/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Hook für Dokumentenfilterung und Suche
 */

import { useState, useEffect, useMemo } from 'react';
import { DocumentMeta } from 'utils/documentStore';

interface UseDocumentFilterProps {
  documents: DocumentMeta[];
}

interface UseDocumentFilterReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  activeTag: string | null;
  setActiveTag: (tag: string | null) => void;
  filteredDocs: DocumentMeta[];
  allTags: string[];
  clearFilters: () => void;
  hasActiveFilters: boolean;
}

export function useDocumentFilter({
  documents
}: UseDocumentFilterProps): UseDocumentFilterReturn {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTag, setActiveTag] = useState<string | null>(null);
  const [filteredDocs, setFilteredDocs] = useState<DocumentMeta[]>([]);

  // Alle eindeutigen Tags aus Dokumenten extrahieren
  const allTags = useMemo(() => {
    return Array.from(new Set(
      documents.flatMap(doc => doc.tags || [])
    )).sort();
  }, [documents]);

  // Prüfen ob Filter aktiv sind
  const hasActiveFilters = useMemo(() => {
    return searchTerm.trim() !== "" || activeTag !== null;
  }, [searchTerm, activeTag]);

  // Filter löschen
  const clearFilters = () => {
    setSearchTerm("");
    setActiveTag(null);
  };

  // Dokumente filtern wenn Suchbegriff oder aktiver Tag sich ändert
  useEffect(() => {
    if (!documents) {
      setFilteredDocs([]);
      return;
    }

    let filtered = [...documents];

    // Suchfilter anwenden
    if (searchTerm.trim()) {
      const lowerSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        doc => 
          doc.title.toLowerCase().includes(lowerSearch) ||
          (doc.description?.toLowerCase().includes(lowerSearch)) ||
          (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(lowerSearch)))
      );
    }

    // Tag-Filter anwenden falls aktiv
    if (activeTag) {
      filtered = filtered.filter(doc => 
        doc.tags && doc.tags.includes(activeTag)
      );
    }

    setFilteredDocs(filtered);
  }, [searchTerm, activeTag, documents]);

  return {
    searchTerm,
    setSearchTerm,
    activeTag,
    setActiveTag,
    filteredDocs,
    allTags,
    clearFilters,
    hasActiveFilters
  };
}
