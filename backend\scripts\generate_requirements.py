#!/usr/bin/env python3
"""
Generate requirements.txt from pyproject.toml
This ensures pyproject.toml is the single source of truth for dependencies.
"""

import sys
import subprocess
from pathlib import Path

def generate_requirements():
    """Generate requirements.txt from pyproject.toml using uv"""
    backend_dir = Path(__file__).parent.parent
    pyproject_path = backend_dir / "pyproject.toml"
    requirements_path = backend_dir / "requirements.txt"
    
    if not pyproject_path.exists():
        print(f"Error: {pyproject_path} not found!")
        sys.exit(1)
    
    try:
        # Use uv to compile requirements from pyproject.toml
        result = subprocess.run([
            "uv", "pip", "compile", 
            str(pyproject_path), 
            "-o", str(requirements_path),
            "--no-header"
        ], capture_output=True, text=True, check=True)
        
        # Add header to requirements.txt
        with open(requirements_path, 'r') as f:
            content = f.read()
        
        header = """# CodeScribe GIS Backend Dependencies
# This file is AUTO-GENERATED from pyproject.toml - DO NOT EDIT MANUALLY
# To update dependencies, edit pyproject.toml and run: python scripts/generate_requirements.py
# Or use: uv pip compile pyproject.toml -o requirements.txt

"""
        
        with open(requirements_path, 'w') as f:
            f.write(header + content)
        
        print(f"✅ Successfully generated {requirements_path}")
        print(f"📦 Generated from {pyproject_path}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error generating requirements.txt: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ Error: 'uv' command not found. Please install uv first:")
        print("   pip install uv")
        sys.exit(1)

if __name__ == "__main__":
    generate_requirements()
