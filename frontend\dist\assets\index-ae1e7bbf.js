import{m as n}from"./vendor-8eb8bd34.js";var g=["light","dark"],k="(prefers-color-scheme: dark)",E=n.createContext(void 0),b={setTheme:a=>{},themes:[]},w=()=>{var a;return(a=n.useContext(E))!=null?a:b};n.memo(({forcedTheme:a,storageKey:$,attribute:c,enableSystem:v,enableColorScheme:d,defaultTheme:l,value:e,attrs:y,nonce:S})=>{let u=l==="system",m=c==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${y.map(r=>`'${r}'`).join(",")})`};`:`var d=document.documentElement,n='${c}',s='setAttribute';`,h=d?g.includes(l)&&l?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${l}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",t=(r,i=!1,p=!0)=>{let o=e?e[r]:r,f=i?r+"|| ''":`'${o}'`,s="";return d&&p&&!i&&g.includes(r)&&(s+=`d.style.colorScheme = '${r}';`),c==="class"?i||o?s+=`c.add(${f})`:s+="null":o&&(s+=`d[s](n,${f})`),s},x=a?`!function(){${m}${t(a)}}()`:v?`!function(){try{${m}var e=localStorage.getItem('${$}');if('system'===e||(!e&&${u})){var t='${k}',m=window.matchMedia(t);if(m.media!==t||m.matches){${t("dark")}}else{${t("light")}}}else if(e){${e?`var x=${JSON.stringify(e)};`:""}${t(e?"x[e]":"e",!0)}}${u?"":"else{"+t(l,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${$}');if(e){${e?`var x=${JSON.stringify(e)};`:""}${t(e?"x[e]":"e",!0)}}else{${t(l,!1,!1)};}${h}}catch(t){}}();`;return n.createElement("script",{nonce:S,dangerouslySetInnerHTML:{__html:x}})});export{w as j};
