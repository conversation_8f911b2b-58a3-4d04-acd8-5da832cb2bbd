import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserGuardContext, firebaseAuth } from 'app';
import { useUserProfileStore } from 'utils/userProfileStore';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { toast } from 'sonner';
import { AccountPlan, PLAN_FEATURES } from 'utils/firebaseConfig';
import { EmailAuthProvider, reauthenticateWithCredential, updatePassword, deleteUser, GoogleAuthProvider, linkWithPopup } from 'firebase/auth';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function Profile() {
  const { user } = useUserGuardContext();
  const { profile, updateProfile } = useUserProfileStore();
  const navigate = useNavigate();
  
  const [displayName, setDisplayName] = useState(profile?.displayName || '');
  const [isUpdating, setIsUpdating] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(profile?.preferences?.emailNotifications || false);
  const [language, setLanguage] = useState(profile?.preferences?.language || 'en');
  
  // Password change state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);
  
  // Account deletion state
  const [deleteConfirmPassword, setDeleteConfirmPassword] = useState('');
  const [deletingAccount, setDeletingAccount] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  // Function to update user profile information
  const handleUpdateProfile = async () => {
    try {
      setIsUpdating(true);
      await updateProfile({ 
        displayName,
        preferences: {
          ...profile?.preferences,
          emailNotifications,
          language
        }
      });
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsUpdating(false);
    }
  };
  
  // Function to handle password change
  const handlePasswordChange = async () => {
    if (newPassword !== confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }
    
    if (newPassword.length < 6) {
      toast.error('Password must be at least 6 characters');
      return;
    }
    
    try {
      setChangingPassword(true);
      const credential = EmailAuthProvider.credential(
        user.email || '',
        currentPassword
      );
      
      // Re-authenticate user
      await reauthenticateWithCredential(user, credential);
      
      // Update password
      await updatePassword(user, newPassword);
      
      toast.success('Password updated successfully');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error('Failed to change password. Please check your current password.');
    } finally {
      setChangingPassword(false);
    }
  };
  
  // Function to handle account deletion with different auth methods
  const handleDeleteAccount = async () => {
    try {
      setDeletingAccount(true);
      
      // Check which provider is being used
      const isPasswordProvider = user.providerData.some(p => p.providerId === 'password');
      
      if (isPasswordProvider) {
        // Re-authenticate with password if email/password auth is used
        if (!deleteConfirmPassword) {
          toast.error('Please enter your password to confirm deletion');
          setDeletingAccount(false);
          return;
        }
        
        // Re-authenticate user before deletion
        const credential = EmailAuthProvider.credential(
          user.email || '',
          deleteConfirmPassword
        );
        
        await reauthenticateWithCredential(user, credential);
      }
      
      // Delete user profile from Firestore first
      const { useUserProfileStore } = await import('utils/userProfileStore');
      await useUserProfileStore.getState().deleteUserProfile(user.uid);
      
      // Delete the user authentication account
      await deleteUser(user);
      
      toast.success('Account deleted successfully');
      
      // Navigate to home page
      navigate('/');
    } catch (error: any) {
      console.error('Error deleting account:', error);
      if (error.code === 'auth/requires-recent-login') {
        toast.error('For security, please log out and log in again before deleting your account.');
      } else {
        toast.error(error.message || 'Failed to delete account. Please try again.');
      }
    } finally {
      setDeletingAccount(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm p-4 pt-16">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="font-mono"
          >
            ← Back to home
          </Button>
        </div>
        
        <h1 className="text-3xl font-mono tracking-wide mb-8 before:content-['#'] before:text-primary before:mr-2">
          User Profile
        </h1>
        
        <Tabs defaultValue="profile" className="w-full mb-8">
          <TabsList className="grid w-full md:w-auto grid-cols-3 mb-6">
            <TabsTrigger value="profile" className="font-mono">Profile</TabsTrigger>
            <TabsTrigger value="subscription" className="font-mono">Subscription</TabsTrigger>
            <TabsTrigger value="security" className="font-mono">Security</TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="border-border/40 bg-card/80 backdrop-blur-sm col-span-1">
                <CardHeader>
                  <CardTitle className="font-mono text-lg">User Info</CardTitle>
                  <CardDescription>Your account information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="w-20 h-20 mx-auto rounded-md overflow-hidden bg-muted flex items-center justify-center mb-4">
                    {profile?.photoURL ? (
                      <img src={profile.photoURL} alt="Profile" className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xl font-mono">{profile?.displayName?.[0] || user.email?.[0] || '?'}</span>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-mono">{user.email}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Account Plan</p>
                    <p className="font-mono uppercase">{profile?.accountPlan || AccountPlan.FREE}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">User ID</p>
                    <p className="font-mono text-xs truncate">{user.uid}</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="border-border/40 bg-card/80 backdrop-blur-sm col-span-1 md:col-span-2">
                <CardHeader>
                  <CardTitle className="font-mono text-lg">Edit Profile</CardTitle>
                  <CardDescription>Update your profile information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">Receive emails about account updates and new features</p>
                    </div>
                    <Switch 
                      checked={emailNotifications}
                      onCheckedChange={setEmailNotifications}
                    />
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div className="space-y-2">
                    <Label htmlFor="language">Documentation Language</Label>
                    <p className="text-sm text-muted-foreground">Select the language for generated documentation</p>
                    <Select value={language} onValueChange={setLanguage}>
                      <SelectTrigger id="language" className="w-full font-mono">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="de">German (Deutsch)</SelectItem>
                        <SelectItem value="fr">French (Français)</SelectItem>
                        <SelectItem value="it">Italian (Italiano)</SelectItem>
                        <SelectItem value="es">Spanish (Español)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Separator className="my-4" />
                  
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Account Created</p>
                    <p className="font-mono">
                      {profile?.createdAt ? new Date(profile.createdAt).toLocaleString() : 'Unknown'}
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setDisplayName(profile?.displayName || '');
                      setEmailNotifications(profile?.preferences?.emailNotifications || false);
                      setLanguage(profile?.preferences?.language || 'en');
                    }}
                  >
                    Reset
                  </Button>
                  <Button 
                    onClick={handleUpdateProfile} 
                    disabled={isUpdating || 
                      (displayName === profile?.displayName && 
                      emailNotifications === profile?.preferences?.emailNotifications &&
                      language === profile?.preferences?.language)}
                  >
                    {isUpdating ? 'Updating...' : 'Save Changes'}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="subscription" className="space-y-6">
            <Card className="border-border/40 bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="font-mono text-lg">Subscription Plan</CardTitle>
                <CardDescription>Manage your subscription and billing</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="p-4 bg-primary/5 rounded-md border border-border/40">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold font-mono tracking-wide uppercase">
                        {profile?.accountPlan || AccountPlan.FREE} Plan
                      </h3>
                      {profile?.accountPlan !== AccountPlan.FREE && profile?.hasActiveSubscription && (
                        <p className="text-sm text-green-500">Active</p>
                      )}
                    </div>
                    {profile?.accountPlan !== AccountPlan.FREE && profile?.hasActiveSubscription && (
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">Next Billing Date</p>
                        <p className="font-mono">
                          {profile?.accountExpiryDate 
                            ? new Date(profile.accountExpiryDate).toLocaleDateString() 
                            : 'Unknown'}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <Separator className="my-4" />
                  
                  {/* Display plan features */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold mb-2">Plan Features:</h4>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>
                          {PLAN_FEATURES[profile?.accountPlan || AccountPlan.FREE].maxDocuments} Documents
                        </span>
                      </li>
                      <li className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>
                          {(PLAN_FEATURES[profile?.accountPlan || AccountPlan.FREE].maxStorageBytes / (1024 * 1024)).toFixed(0)} MB Storage
                        </span>
                      </li>
                      {PLAN_FEATURES[profile?.accountPlan || AccountPlan.FREE].allowAdvancedAnalytics && (
                        <li className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Advanced Analytics</span>
                        </li>
                      )}
                      {PLAN_FEATURES[profile?.accountPlan || AccountPlan.FREE].allowBatchProcessing && (
                        <li className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Batch Processing</span>
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
                
                {/* Plan upgrade section */}
                <div>
                  <h3 className="text-lg font-semibold font-mono tracking-wide mb-4">Upgrade Your Plan</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    {profile?.accountPlan !== AccountPlan.PRO && (
                      <Card className="border-border/40 hover:border-primary/40 transition-colors cursor-pointer">
                        <CardHeader>
                          <CardTitle className="font-mono text-lg">PRO</CardTitle>
                          <CardDescription className="text-2xl font-bold">
                            ${PLAN_FEATURES[AccountPlan.PRO].price}<span className="text-sm font-normal">/month</span>
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <ul className="space-y-1">
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>{PLAN_FEATURES[AccountPlan.PRO].maxDocuments} Documents</span>
                            </li>
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>{(PLAN_FEATURES[AccountPlan.PRO].maxStorageBytes / (1024 * 1024)).toFixed(0)} MB Storage</span>
                            </li>
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>Advanced Analytics</span>
                            </li>
                          </ul>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full font-mono" disabled={profile?.accountPlan === AccountPlan.PRO}>
                            Upgrade to Pro
                          </Button>
                        </CardFooter>
                      </Card>
                    )}
                    
                    {profile?.accountPlan !== AccountPlan.ENTERPRISE && (
                      <Card className="border-border/40 hover:border-primary/40 transition-colors cursor-pointer">
                        <CardHeader>
                          <CardTitle className="font-mono text-lg">ENTERPRISE</CardTitle>
                          <CardDescription className="text-2xl font-bold">
                            ${PLAN_FEATURES[AccountPlan.ENTERPRISE].price}<span className="text-sm font-normal">/month</span>
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <ul className="space-y-1">
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>{PLAN_FEATURES[AccountPlan.ENTERPRISE].maxDocuments} Documents</span>
                            </li>
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>{(PLAN_FEATURES[AccountPlan.ENTERPRISE].maxStorageBytes / (1024 * 1024 * 1024)).toFixed(0)} GB Storage</span>
                            </li>
                            <li className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>Team Collaboration</span>
                            </li>
                          </ul>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full font-mono" disabled={profile?.accountPlan === AccountPlan.ENTERPRISE}>
                            Upgrade to Enterprise
                          </Button>
                        </CardFooter>
                      </Card>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground mt-4">
                    * Payment processing will be available soon. Contact support for manual upgrades.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="security" className="space-y-6">
            {/* Authentication Methods Card */}
            <Card className="border-border/40 bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="font-mono text-lg">Authentication Methods</CardTitle>
                <CardDescription>Manage how you sign in to your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Email Password Method */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Email and Password</h3>
                    <p className="text-sm text-muted-foreground">
                      {user.email}
                      {user.providerData.some(p => p.providerId === 'password') ? 
                        ' (Connected)' : ' (Not connected)'}
                    </p>
                  </div>

                </div>
                
                {/* Google Auth Method */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">Google</h3>
                    <p className="text-sm text-muted-foreground">
                      {user.providerData.some(p => p.providerId === 'google.com') ? 
                        'Connected to Google Account' : 'Not connected'}
                    </p>
                  </div>
                  <Button 
                    variant="outline"
                    onClick={async () => {
                      if (user.providerData.some(p => p.providerId === 'google.com')) {
                        toast.info('Google account already connected');
                        return;
                      }
                      
                      try {
                        const provider = new GoogleAuthProvider();
                        provider.addScope('profile');
                        provider.addScope('email');
                        await linkWithPopup(user, provider);
                        toast.success('Google account connected successfully');
                        // Force refresh to update UI
                        window.location.reload();
                      } catch (error: any) {
                        console.error('Error linking Google account:', error);
                        toast.error(error.message || 'Failed to connect Google account');
                      }
                    }}>
                    {user.providerData.some(p => p.providerId === 'google.com') ? 'Connected' : 'Connect'}
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Password Management Card - Only shown if user has password auth */}
            {user.providerData.some(p => p.providerId === 'password') && (
              <Card className="border-border/40 bg-card/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="font-mono text-lg">Password Management</CardTitle>
                  <CardDescription>Update your password</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                </CardContent>
                <CardFooter className="justify-end">
                  <Button 
                    onClick={handlePasswordChange} 
                    disabled={changingPassword || !currentPassword || !newPassword || !confirmPassword || newPassword !== confirmPassword}
                  >
                    {changingPassword ? 'Updating Password...' : 'Update Password'}
                  </Button>
                </CardFooter>
              </Card>
            )}
            
            <Card className="border-border/40 bg-card/80 backdrop-blur-sm border-destructive/20">
              <CardHeader>
                <CardTitle className="font-mono text-lg text-destructive">Delete Account</CardTitle>
                <CardDescription>Permanently delete your account and all data</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm mb-4">This action cannot be undone. All your data will be permanently removed.</p>
                
                {showDeleteConfirm ? (
                  <div className="space-y-4">
                    {user.providerData.some(p => p.providerId === 'password') && (
                      <div className="space-y-2">
                        <Label htmlFor="deleteConfirmPassword">Enter your password to confirm</Label>
                        <Input
                          id="deleteConfirmPassword"
                          type="password"
                          value={deleteConfirmPassword}
                          onChange={(e) => setDeleteConfirmPassword(e.target.value)}
                          className="font-mono"
                        />
                      </div>
                    )}
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowDeleteConfirm(false);
                          setDeleteConfirmPassword('');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDeleteAccount}
                        disabled={deletingAccount || (user.providerData.some(p => p.providerId === 'password') && !deleteConfirmPassword)}
                      >
                        {deletingAccount ? 'Deleting...' : 'Confirm Delete'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="destructive"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    Delete Account
                  </Button>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}