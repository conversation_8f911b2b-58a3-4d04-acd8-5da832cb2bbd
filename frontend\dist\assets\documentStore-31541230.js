import{a as T,q as v,I as A,C as f,J as p,B as E,v as C,x as L,g as y,a4 as b,a5 as I,D as U}from"./vendor-8eb8bd34.js";import{f as S,C as m,b as N,A as k,P as x}from"./index-14585119.js";const g=T(S),M=v((i,d)=>({documents:[],currentDocument:null,isLoading:!1,error:null,fetchUserDocuments:async e=>{try{i({isLoading:!0,error:null}),console.log("Fetching documents for user:",e);const t=A(f(g,m.DOCUMENTS),p("userId","==",e),p("isDeleted","==",!1));console.log("Executing Firestore query...");const r=await E(t);console.log(`Retrieved ${r.size} documents`);const a=[];r.forEach(o=>{var c,u;const n=o.data();a.push({id:o.id,title:n.title,description:n.description,createdAt:(c=n.createdAt)==null?void 0:c.toDate(),updatedAt:(u=n.updatedAt)==null?void 0:u.toDate(),userId:n.userId,language:n.language,codeSize:n.codeSize,isPublic:n.isPublic,tags:n.tags||[]})}),a.sort((o,n)=>o.updatedAt&&n.updatedAt?n.updatedAt.getTime()-o.updatedAt.getTime():0),console.log(`Sorted and processed ${a.length} documents`),i({documents:a,isLoading:!1})}catch(t){console.error("Error fetching documents:",t),i({error:t,isLoading:!1})}},getDocumentById:async e=>{var t,r,a;try{i({isLoading:!0,error:null});const o=C(g,m.DOCUMENTS,e),n=await L(o);if(!n.exists())throw new Error("Document not found");const c=n.data(),u=await E(f(o,"content"));if(u.empty)throw new Error("Document content not found");const s=u.docs[0].data(),h={id:e,title:c.title,description:c.description,createdAt:(t=c.createdAt)==null?void 0:t.toDate(),updatedAt:(r=c.updatedAt)==null?void 0:r.toDate(),userId:c.userId,language:c.language,codeSize:c.codeSize,isPublic:c.isPublic,tags:c.tags||[],content:{originalCode:s.originalCode,generatedDocumentation:s.generatedDocumentation,parameters:s.parameters,toolsUsed:s.toolsUsed,workflow:s.workflow,manualEdits:(a=s.manualEdits)==null?void 0:a.map(w=>{var D;return{...w,timestamp:(D=w.timestamp)==null?void 0:D.toDate()}})}};i({currentDocument:h,isLoading:!1})}catch(o){console.error("Error fetching document:",o),i({error:o,isLoading:!1,currentDocument:null})}},createDocument:async e=>{try{i({isLoading:!0,error:null});const r=y(S).currentUser;if(!r)throw console.error("Authentication error: No current user found"),new Error("User must be authenticated to create documents");if(e.userId!==r.uid)throw console.error(`User ID mismatch: Document user ID (${e.userId}) does not match current user (${r.uid})`),new Error("Document user ID must match authenticated user");const a=b(),o=await I(f(g,m.DOCUMENTS),{title:e.title,description:e.description||"",createdAt:a,updatedAt:a,userId:r.uid,language:e.language,codeSize:e.content.originalCode.length,isPublic:e.isPublic,tags:e.tags||[]});return await I(f(g,m.DOCUMENTS,o.id,"content"),{originalCode:e.content.originalCode,generatedDocumentation:e.content.generatedDocumentation||"",parameters:e.content.parameters||{},toolsUsed:e.content.toolsUsed||[],workflow:e.content.workflow||null,manualEdits:e.content.manualEdits||[]}),await d().fetchUserDocuments(e.userId),i({isLoading:!1}),o.id}catch(t){throw console.error("Error creating document:",t),i({error:t,isLoading:!1}),t}},updateDocument:async(e,t)=>{var r,a;try{if(i({isLoading:!0,error:null}),!y(S).currentUser)throw new Error("User must be authenticated to update documents");const c=b(),u=C(g,m.DOCUMENTS,e),s={updatedAt:c};if(t.title&&(s.title=t.title),t.description!==void 0&&(s.description=t.description),t.isPublic!==void 0&&(s.isPublic=t.isPublic),t.tags&&(s.tags=t.tags),await U(u,s),t.content){const w=A(f(u,"content")),D=await E(w);if(!D.empty){const P=D.docs[0].ref,l={};t.content.originalCode!==void 0&&(l.originalCode=t.content.originalCode,s.codeSize=t.content.originalCode.length,await U(u,{codeSize:t.content.originalCode.length})),t.content.generatedDocumentation!==void 0&&(l.generatedDocumentation=t.content.generatedDocumentation),t.content.parameters!==void 0&&(l.parameters=t.content.parameters),t.content.toolsUsed!==void 0&&(l.toolsUsed=t.content.toolsUsed),t.content.workflow!==void 0&&(l.workflow=t.content.workflow),t.content.manualEdits!==void 0&&(l.manualEdits=t.content.manualEdits),await U(P,l)}}((r=d().currentDocument)==null?void 0:r.id)===e&&await d().getDocumentById(e);const h=(a=d().currentDocument)==null?void 0:a.userId;h&&await d().fetchUserDocuments(h),i({isLoading:!1})}catch(o){console.error("Error updating document:",o),i({error:o,isLoading:!1})}},deleteDocument:async e=>{var t;try{i({isLoading:!0,error:null});const a=y(S).currentUser;if(!a)throw console.error("Authentication error: No current user found for deletion"),new Error("User must be authenticated to delete documents");const o=C(g,m.DOCUMENTS,e),n=await L(o);if(!n.exists())throw new Error("Document not found");const c=n.data();if(c.userId!==a.uid)throw console.error(`Permission error: User ${a.uid} does not own document ${e} (owned by ${c.userId})`),new Error("Missing or insufficient permissions to delete this document");await U(o,{isDeleted:!0,deletedAt:new Date}),((t=d().currentDocument)==null?void 0:t.id)===e&&i({currentDocument:null}),await d().fetchUserDocuments(a.uid),i({isLoading:!1})}catch(r){throw console.error("Error deleting document:",r),i({error:r,isLoading:!1}),r}},clearCurrentDocument:()=>{i({currentDocument:null})},checkDocumentLimit:async e=>{try{const t=N.getState().profile,r=(t==null?void 0:t.accountPlan)||k.FREE,o=x[r].maxDocuments,n=A(f(g,m.DOCUMENTS),p("userId","==",e),p("isDeleted","==",!1)),u=(await E(n)).size;return{canCreate:u<o,currentCount:u,maxAllowed:o}}catch(t){return console.error("Error checking document limit:",t),{canCreate:!0,currentCount:0,maxAllowed:5}}}}));export{M as useDocumentStore};
