import { firebaseAuth } from "app";
import { GoogleAuthProvider, signInWithPopup, signInWithEmailAndPassword, sendPasswordResetEmail } from "firebase/auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { useDevBypass } from "utils/devBypass";
import { mode, Mode } from "app";
import { toast } from "sonner";

export default function Login() {
  const navigate = useNavigate();
  const { bypassAuth, isDev } = useDevBypass();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 flex items-center justify-center mb-2">
            <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
            <span className="relative z-10 font-mono font-bold">GIS</span>
          </div>
          <CardTitle className="text-2xl font-mono tracking-wide">Sign In</CardTitle>
          <CardDescription>
            Log in to access your documentation and tools
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Email/Password Form */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="<EMAIL>" 
                value={email} 
                onChange={(e) => setEmail(e.target.value)}
                className="font-mono"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password" 
                type="password" 
                placeholder="••••••••" 
                value={password} 
                onChange={(e) => setPassword(e.target.value)}
                className="font-mono"
              />
            </div>
            

            
            {error && (
              <div className="text-sm font-medium text-destructive p-2 border border-destructive/20 bg-destructive/5 rounded-md mb-2">{error}</div>
            )}
            
            <Button 
              type="submit"
              className="w-full justify-center" 
              disabled={isLoading || !email || !password}
              onClick={async () => {
                try {
                  setIsLoading(true);
                  setError('');
                  
                  // Sign in with email and password
                  await signInWithEmailAndPassword(firebaseAuth, email, password);
                  
                  // Navigate to editor on success
                  toast.success("Login successful!");
                  
                  // Wait for Firebase Auth to update
                  setTimeout(async () => {
                    // Get the current user after auth state updates
                    const currentUser = firebaseAuth.currentUser;
                    if (currentUser) {
                      try {
                        // Import and use profile store directly to avoid hook rules issues
                        const { useUserProfileStore } = await import('utils/userProfileStore');
                        await useUserProfileStore.getState().createProfileIfNotExists(
                          currentUser.uid,
                          currentUser.email === undefined ? null : currentUser.email,
                          currentUser.displayName === undefined ? null : currentUser.displayName,
                          currentUser.photoURL === undefined ? null : currentUser.photoURL
                        );
                      } catch (profileError) {
                        console.warn('Non-critical error updating profile:', profileError);
                        // Continue with navigation even if profile update fails
                      }
                    }
                    
                    // Navigate to the home page
                    navigate('/');
                  }, 500); // Small delay to ensure auth state updates
                } catch (error: any) {
                  console.error('Authentication failed:', error);
                  // Handle specific Firebase auth errors
                  if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
                    setError('Invalid email or password');
                  } else {
                    setError('Authentication failed. Please try again.');
                  }
                } finally {
                  setIsLoading(false);
                }
              }}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Signing in...</span>
                </div>
              ) : 'Sign in'}
            </Button>
            
            <div className="text-center">
              <Button 
                variant="link" 
                className="font-mono text-xs"
                asChild
              >
                <Link to="/register">Need an account? Register</Link>
              </Button>
            </div>
            
            <div className="text-center mt-2">
              <Button 
                variant="ghost" 
                className="font-mono text-xs"
                onClick={() => {
                  setIsResettingPassword(true);
                  setError('');
                }}
              >
                Forgot password?
              </Button>
            </div>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-border/40"></span>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
              </div>
            </div>
            
            <Button 
              variant="outline"
              className="w-full justify-center space-x-2" 
              onClick={async () => {
                try {
                  const provider = new GoogleAuthProvider();
              // Add scopes for additional user information
              provider.addScope('profile');
              provider.addScope('email');
              
              await signInWithPopup(firebaseAuth, provider);
                  toast.success("Login successful!");
                  
                  // Small delay to ensure auth state updates
                  setTimeout(async () => {
                    try {
                      // Get the current user after auth state updates
                      const currentUser = firebaseAuth.currentUser;
                      if (currentUser) {
                        // Import and use profile store directly to avoid hook rules issues
                        const { useUserProfileStore } = await import('utils/userProfileStore');
                        await useUserProfileStore.getState().createProfileIfNotExists(
                          currentUser.uid,
                          currentUser.email === undefined ? null : currentUser.email,
                          currentUser.displayName === undefined ? null : currentUser.displayName,
                          currentUser.photoURL === undefined ? null : currentUser.photoURL
                        );
                      }
                    } catch (profileError) {
                      console.warn('Non-critical error updating profile:', profileError);
                      // Continue with navigation even if profile update fails
                    }
                    
                    // Navigate to the home page
                    navigate('/');
                  }, 500); // Small delay to ensure auth state updates
                } catch (error) {
                  console.error('Authentication failed:', error);
                  setError('Google authentication failed');
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="h-5 w-5">
                <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"/>
                <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"/>
                <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"/>
                <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"/>
              </svg>
              <span>Sign in with Google</span>
            </Button>
          </div>
          
          <div className="mt-6 text-center">
            
            {/* Password Reset Dialog */}
            {isResettingPassword && (
              <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                <div className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full space-y-4 border border-border">
                  <h2 className="text-lg font-semibold">Reset Password</h2>
                  <p className="text-sm text-muted-foreground">Enter your email address and we'll send you a link to reset your password.</p>
                  
                  <div className="space-y-2">
                    <Label htmlFor="resetEmail">Email</Label>
                    <Input
                      id="resetEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={resetEmail}
                      onChange={(e) => setResetEmail(e.target.value)}
                      className="font-mono"
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsResettingPassword(false);
                        setResetEmail('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={async () => {
                        if (!resetEmail) {
                          toast.error('Please enter your email address');
                          return;
                        }
                        
                        try {
                          setIsLoading(true);
                          await sendPasswordResetEmail(firebaseAuth, resetEmail);
                          toast.success('Password reset email sent! Check your inbox.');
                          setIsResettingPassword(false);
                          setResetEmail('');
                        } catch (error: any) {
                          console.error('Password reset failed:', error);
                          if (error.code === 'auth/user-not-found') {
                            toast.error('No account found with this email address');
                          } else {
                            toast.error('Failed to send password reset email. Please try again.');
                          }
                        } finally {
                          setIsLoading(false);
                        }
                      }}
                      disabled={isLoading || !resetEmail}
                    >
                      {isLoading ? 'Sending...' : 'Send Reset Link'}
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            {/* Dev bypass button - only shown in development */}
            {isDev && (
              <div className="mt-4 pt-4 border-t border-muted">
                <p className="text-xs text-muted-foreground mb-2">
                  Development mode detected
                </p>
                <Button 
                  variant="secondary" 
                  size="sm"
                  className="font-mono tracking-wide bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-700 dark:text-yellow-300"
                  onClick={() => bypassAuth()}
                >
                  Bypass Login (Dev Only)
                </Button>
              </div>
            )}

            <div className="mt-6 text-center">
              <Button 
                variant="ghost" 
                size="sm"
                className="font-mono text-xs"
                asChild
              >
                <Link to="/">Back to Home</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
