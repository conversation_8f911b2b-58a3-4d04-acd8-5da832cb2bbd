import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Document } from "../../services/documentStore";
import { FileText, FileCode, File, File as FileDocument, FileType, Loader2 } from "lucide-react";

interface FormatOption {
  id: string;
  label: string;
  icon: React.ReactNode;
  mimeType: string;
  extension: string;
}

interface ExportDialogProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
  onExport: (document: Document, formats: string[]) => Promise<void>;
}

const formatOptions: FormatOption[] = [
  {
    id: "markdown",
    label: "Markdown",
    icon: <FileText size={16} />,
    mimeType: "text/markdown",
    extension: ".md",
  },
  {
    id: "html",
    label: "HTML",
    icon: <FileCode size={16} />,
    mimeType: "text/html",
    extension: ".html",
  },
  {
    id: "text",
    label: "Plain Text",
    icon: <File size={16} />,
    mimeType: "text/plain",
    extension: ".txt",
  },
  {
    id: "docx",
    label: "Word Document",
    icon: <FileDocument size={16} />,
    mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    extension: ".docx",
  },
  {
    id: "pdf",
    label: "PDF Document",
    icon: <FileType size={16} />,
    mimeType: "application/pdf",
    extension: ".pdf",
  },
];

export function ExportDialog({ document, isOpen, onClose, onExport }: ExportDialogProps) {
  const [selectedFormats, setSelectedFormats] = useState<string[]>(["markdown"]);
  const [isExporting, setIsExporting] = useState(false);
  const [isDocumentValid, setIsDocumentValid] = useState(true);

  console.log('ExportDialog rendered with document:', document?.id);
  
  // Validate document when it changes
  useEffect(() => {
    if (!document) {
      console.error('Document is undefined in ExportDialog');
      setIsDocumentValid(false);
      return;
    }
    
    if (!document.content) {
      console.error('Document content is undefined in ExportDialog:', document.id);
      setIsDocumentValid(false);
      return;
    }
    
    // More detailed logging for document structure
    console.log('ExportDialog document structure check:', {
      documentId: document.id,
      contentExists: !!document.content,
      originalCodeExists: !!document.content.originalCode,
      originalCodeLength: document.content.originalCode?.length || 0,
      generatedDocExists: !!document.content.generatedDocumentation,
      generatedDocLength: document.content.generatedDocumentation?.length || 0
    });
    
    if (!document.content.originalCode) {
      console.warn('Document original code is missing in ExportDialog, but continuing anyway');
    }

    if (!document.content.generatedDocumentation) {
      console.warn('Document generated documentation is missing in ExportDialog, but continuing anyway');
    }

    console.log('Document validated in ExportDialog:', document.id);
    setIsDocumentValid(true);
  }, [document]);

  const handleFormatToggle = (formatId: string) => {
    setSelectedFormats((current) => {
      if (current.includes(formatId)) {
        return current.filter((id) => id !== formatId);
      } else {
        return [...current, formatId];
      }
    });
  };

  const handleExport = async () => {
    if (selectedFormats.length === 0) {
      toast.error("Please select at least one format");
      return;
    }
    
    // Double-check document validity before export
    if (!isDocumentValid) {
      console.error('Invalid document for export (isDocumentValid is false)');
      toast.error('Document data is invalid or missing');
      return;
    }
    
    if (!document) {
      console.error('Document is undefined in handleExport');
      toast.error('Document data is missing');
      return;
    }
    
    if (!document.content) {
      console.error('Document content is undefined in handleExport:', document.id);
      toast.error('Document content is missing');
      return;
    }

    // Log export attempt with details
    console.log('Starting export with:', {
      documentId: document.id,
      formats: selectedFormats,
      contentAvailable: !!document.content,
      codeAvailable: !!document.content.originalCode,
      docAvailable: !!document.content.generatedDocumentation,
      cachedFormats: document.content.exportCache?.map(c => c.format) || []
    });
    
    console.log('Export button clicked in dialog, formats:', selectedFormats, 'document:', document.id);
    
    // Create an array of toast IDs for each format
    const toastIds = selectedFormats.map(format => `export-${format}-${Date.now()}`);
    
    // Show a loading toast for all formats
    toast.loading(`Preparing export for ${selectedFormats.length} format(s)...`, { id: 'export-all' });
    
    setIsExporting(true);
    try {
      // Make a deep copy of the document to avoid modifying the original
      const docCopy = JSON.parse(JSON.stringify(document));
      
      console.log('Calling onExport handler with formats:', selectedFormats);
      await onExport(docCopy, selectedFormats);
      
      console.log('Export completed successfully');
      toast.success(`Documents exported successfully in ${selectedFormats.length} format(s)`, { id: 'export-all' });
      onClose();
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export document: " + (error instanceof Error ? error.message : 'Unknown error'), { id: 'export-all' });
      
      // Log detailed error information for debugging
      if (error instanceof Error) {
        console.error('Error stack:', error.stack);
      }
      
      // Try to extract useful information about what went wrong
      let errorInfo = "Unknown error occurred";
      if (error instanceof Error) {
        errorInfo = error.message;
        
        // Look for specific error patterns
        if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
          errorInfo = "Browser security prevented download. Try again or use a different format.";
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorInfo = "Network error. Check your internet connection and try again.";
        } else if (error.message.includes('timeout')) {
          errorInfo = "Export timed out. Try again or use a simpler format.";
        }
      }
      
      toast.error(`Export failed: ${errorInfo}`, { id: 'export-all' });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Document</DialogTitle>
          <DialogDescription>
            Select the formats you want to export your document to. Text formats (Markdown, HTML, Text) 
            are generated instantly, while rich formats (Word, PDF) may take a moment to process.
          </DialogDescription>
        </DialogHeader>

        {!isDocumentValid ? (
          <div className="my-6 text-center">
            <p className="text-destructive mb-4">Document data is missing or invalid</p>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </div>
        ) : (
          <>
            <div className="grid gap-4 py-4">
              {formatOptions.map((format) => (
                <div
                  key={format.id}
                  className="flex items-center space-x-3 space-y-0"
                >
                  <Checkbox
                    id={`format-${format.id}`}
                    checked={selectedFormats.includes(format.id)}
                    onCheckedChange={() => handleFormatToggle(format.id)}
                  />
                  <Label
                    htmlFor={`format-${format.id}`}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    {format.icon}
                    <span>{format.label}</span>
                  </Label>
                </div>
              ))}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={onClose} disabled={isExporting}>
                Cancel
              </Button>
              <Button onClick={handleExport} disabled={isExporting || selectedFormats.length === 0}>
                {isExporting ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>Export Selected ({selectedFormats.length})</>
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
