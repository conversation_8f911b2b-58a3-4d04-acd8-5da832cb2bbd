/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Hook für Dokumentenverwaltung (Erstellen, Löschen, Navigation)
 */

import { useState } from 'react';
import { doc, getDoc, updateDoc, getFirestore } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { COLLECTIONS, AccountPlan, PLAN_FEATURES } from '../utils/firebaseConfig';
import { fixAllDocumentPermissions } from '../utils/documentPermissions';
import { formatDate } from '../utils/commonUtils';
import { toast } from 'sonner';

interface UseDocumentManagerProps {
  user: any;
  userPlan: AccountPlan;
  documentsCount: number;
  onDocumentDeleted: (docId: string) => void;
  onRefreshDocuments: () => Promise<void>;
}

interface UseDocumentManagerReturn {
  deleteTargetId: string | null;
  isDeleting: boolean;
  canCreateNew: boolean;
  handleCreateNew: () => void;
  handleSelectDocument: (docId: string) => void;
  handleDeleteDocument: (docId: string) => Promise<void>;
  formatDate: (date: Date) => string;
}

export function useDocumentManager({
  user,
  userPlan,
  documentsCount,
  onDocumentDeleted,
  onRefreshDocuments
}: UseDocumentManagerProps): UseDocumentManagerReturn {
  const [deleteTargetId, setDeleteTargetId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Prüfen ob neue Dokumente erstellt werden können
  const maxDocuments = PLAN_FEATURES[userPlan].maxDocuments;
  const canCreateNew = documentsCount < maxDocuments;

  // Neues Dokument erstellen
  const handleCreateNew = () => {
    if (!canCreateNew) {
      toast.error(`Sie haben Ihr Plan-Limit von ${maxDocuments} Dokumenten erreicht. Upgraden Sie für mehr Dokumente.`);
      return;
    }

    if (!user?.uid) {
      toast.error('Sie müssen angemeldet sein, um ein Dokument zu erstellen');
      return;
    }

    try {
      toast.success("Neues Dokument wird erstellt...");
      // Direkte Navigation für zuverlässige Weiterleitung
      const basePath = window.location.pathname.endsWith('/') ? 
        window.location.pathname : 
        window.location.pathname + '/';
      window.location.href = './editor?new=true';
    } catch (error) {
      console.error('Fehler bei der Navigation zum Editor:', error);
      toast.error('Navigation zum Editor fehlgeschlagen. Bitte versuchen Sie es erneut.');
    }
  };

  // Dokument auswählen
  const handleSelectDocument = (docId: string) => {
    // Direkte Navigation für zuverlässige Weiterleitung
    const basePath = window.location.pathname.endsWith('/') ? 
      window.location.pathname : 
      window.location.pathname + '/';
    window.location.href = `./editor?id=${docId}`;
  };

  // Dokument löschen mit Bestätigung
  const handleDeleteDocument = async (docId: string) => {
    console.log('Dokument löschen ausgelöst für ID:', docId);
    
    if (!window.confirm('Sind Sie sicher, dass Sie dieses Dokument löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.')) {
      console.log('Löschbestätigung abgelehnt');
      return;
    }

    setIsDeleting(true);
    setDeleteTargetId(docId);

    try {
      const toastId = toast.loading('Dokument wird gelöscht...');
      console.log('Löschbestätigung akzeptiert, Toast ID:', toastId);

      // Prüfen ob Benutzer angemeldet ist
      if (!user?.uid) {
        toast.error('Sie müssen angemeldet sein, um Dokumente zu löschen');
        return;
      }

      console.log('Dokument in Firestore referenzieren:', docId);
      // Direkte Referenz aus Firestore
      const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);

      // Prüfen ob Dokument existiert und dem aktuellen Benutzer gehört
      console.log('Dokument-Snapshot abrufen');
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        toast.error('Dokument nicht gefunden');
        return;
      }

      const docData = docSnap.data();
      console.log('Dokumentdaten abgerufen:', docData);
      
      if (docData.userId !== user.uid) {
        toast.error('Sie haben keine Berechtigung, dieses Dokument zu löschen');
        return;
      }

      // Berechtigungen reparieren falls nötig
      if (docData.needsPermissionFix) {
        console.log('Berechtigungen vor dem Löschen reparieren');
        await fixAllDocumentPermissions(user.uid);
      }

      console.log('Soft-Delete-Operation durchführen');
      // Soft-Delete durch direktes Firestore-Update implementieren
      await updateDoc(docRef, {
        isDeleted: true,
        deletedAt: new Date()
      });
      console.log('Soft-Delete erfolgreich abgeschlossen');

      // Callback für Parent-Komponente aufrufen
      onDocumentDeleted(docId);
      console.log('Dokument aus lokalem Zustand entfernt');

      // Dokumentenliste aktualisieren
      if (user?.uid) {
        console.log('Dokumentenliste aktualisieren');
        await onRefreshDocuments();
      }

      toast.success('Dokument erfolgreich gelöscht');
      console.log('Löschvorgang erfolgreich abgeschlossen');
      
    } catch (deleteErr) {
      console.error('Fehler beim Löschen des Dokuments:', deleteErr);
      toast.error('Berechtigungsfehler: Versuchen Sie zuerst die Schaltfläche "Berechtigungen reparieren" und versuchen Sie es dann erneut');
    } finally {
      setIsDeleting(false);
      toast.dismiss();
      setTimeout(() => setDeleteTargetId(null), 300); // Nach Animation löschen
    }
  };

  // formatDate wird jetzt aus commonUtils importiert

  return {
    deleteTargetId,
    isDeleting,
    canCreateNew,
    handleCreateNew,
    handleSelectDocument,
    handleDeleteDocument,
    formatDate
  };
}
