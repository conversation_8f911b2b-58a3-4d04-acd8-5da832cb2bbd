/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.demo-page--typography {
  color: rgba(0, 0, 0, 0.87);
}

.demo-page--typography table th {
  padding-right: 80px;

  vertical-align: top;
  text-align: left;
}

.demo-typography--white {
  background-color: white;
  color: black;
}

.demo-typography--black {
  background-color: black;
  color: white;
}

.demo-typography--white,
.demo-typography--black,
.demo-typography--img-1,
.demo-typography--img-2 {
  width: 360px;
  height: 272px;

  padding: 16px;
  box-sizing: border-box;
}

.demo-typography--img-1 {
  background-image: url(../demo-images/img-1.png);
  color: white;
}

.demo-typography--img-2 {
  background-image: url(../demo-images/img-2.png);
  color: white;
}
