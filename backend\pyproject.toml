[project]
name = "codescribe-gis-backend"
version = "0.1.0"
description = "CodeScribe GIS - Automated documentation generator for GIS scripts"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    # Core FastAPI dependencies
    "fastapi==0.115.12",
    "uvicorn[standard]==0.34.2",
    "python-multipart==0.0.9",
    "python-dotenv==1.0.1",

    # Authentication & Firebase
    "firebase-admin==6.5.0",

    # LLM Integration
    "openai==1.58.1",
    "google-generativeai==0.8.3",

    # Document Processing & Export
    "python-docx==1.1.2",
    "weasyprint==62.3",
    "pypdf==5.1.0",
    "beautifulsoup4==4.12.3",

    # Image Processing & OCR
    "Pillow==10.4.0",
    "pytesseract==0.3.13",

    # HTTP & Utilities
    "requests==2.32.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "safety>=3.5.2",
    "bandit>=1.8.5",
    "setuptools>=78.1.1",  # Security fix for CVE-2025-47273
]

security = [
    "safety>=3.5.2",
    "bandit>=1.8.5",
    "setuptools>=78.1.1",  # Security fix for CVE-2025-47273
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "databutton_app"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "firebase_admin.*",
    "google.generativeai.*",
    "weasyprint.*",
    "pytesseract.*",
]
ignore_missing_imports = true
