import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileDown, Loader2 } from "lucide-react";

interface Props {
  documentation: string;
  isLoading: boolean;
  onExportRequest: () => void;
  projectContext: string;
}

export function DocumentationPreview({ documentation, isLoading, onExportRequest, projectContext }: Props) {
  
  const handleExport = () => {
    onExportRequest();
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-8 h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="ml-2">Generiere Dokumentation...</p>
        </div>
      );
    }
    if (documentation) {
      return (
          <div
            className="prose prose-sm dark:prose-invert max-w-none p-4"
            dangerouslySetInnerHTML={{ __html: documentation }}
          />
      );
    }
    return <div className="p-4 text-muted-foreground"><p>Hier wird die generierte Dokumentation angezeigt.</p></div>;
  };

  return (
    <div className="flex flex-col h-full border rounded-md bg-card">
      <div className="flex items-center justify-between p-2 border-b bg-muted/30">
        <h2 className="text-lg font-semibold px-2">Vorschau</h2>
        <Button variant="outline" size="sm" onClick={handleExport} disabled={!documentation || isLoading}>
          <FileDown className="h-4 w-4 mr-2" />
          Exportieren
        </Button>
      </div>
      <ScrollArea className="flex-grow">
        {renderContent()}
      </ScrollArea>
    </div>
  );
}
