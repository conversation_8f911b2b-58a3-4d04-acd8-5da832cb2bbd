/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { ReceiverHandler, _EventType, _ReceiverResponse, _SenderRequest } from './index';
/**
 * Interface class for receiving messages.
 *
 */
export declare class Receiver {
    private readonly eventTarget;
    private static readonly receivers;
    private readonly boundEventHandler;
    private readonly handlersMap;
    constructor(eventTarget: EventTarget);
    /**
     * Obtain an instance of a Receiver for a given event target, if none exists it will be created.
     *
     * @param eventTarget - An event target (such as window or self) through which the underlying
     * messages will be received.
     */
    static _getInstance(eventTarget: EventTarget): Receiver;
    private isListeningto;
    /**
     * Fans out a MessageEvent to the appropriate listeners.
     *
     * @remarks
     * Sends an {@link Status.ACK} upon receipt and a {@link Status.DONE} once all handlers have
     * finished processing.
     *
     * @param event - The MessageEvent.
     *
     */
    private handleEvent;
    /**
     * Subscribe an event handler for a particular event.
     *
     * @param eventType - Event name to subscribe to.
     * @param eventHandler - The event handler which should receive the events.
     *
     */
    _subscribe<T extends _ReceiverResponse, S extends _SenderRequest>(eventType: _EventType, eventHandler: ReceiverHandler<T, S>): void;
    /**
     * Unsubscribe an event handler from a particular event.
     *
     * @param eventType - Event name to unsubscribe from.
     * @param eventHandler - Optinoal event handler, if none provided, unsubscribe all handlers on this event.
     *
     */
    _unsubscribe<T extends _ReceiverResponse, S extends _SenderRequest>(eventType: _EventType, eventHandler?: ReceiverHandler<T, S>): void;
}
