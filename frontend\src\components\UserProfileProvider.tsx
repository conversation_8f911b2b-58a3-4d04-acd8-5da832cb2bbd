import React, { useEffect } from 'react';
import { useCurrentUser } from 'app';
import { useUserProfileStore } from 'utils/userProfileStore';
import { toast } from 'sonner';

interface UserProfileProviderProps {
  children: React.ReactNode;
}

export const UserProfileProvider: React.FC<UserProfileProviderProps> = ({ children }) => {
  const { user, loading } = useCurrentUser();
  const { subscribeToProfile, createProfileIfNotExists } = useUserProfileStore();
  
  // Subscribe to user profile changes when user changes
  useEffect(() => {
    let unsubscribe = () => {};
    
    // If user is logged in, subscribe to their profile
    if (user && !loading) {
      try {
        // Create user profile if it doesn't exist
        createProfileIfNotExists(
          user.uid,
          user.email === undefined ? null : user.email,
          user.displayName === undefined ? null : user.displayName,
          user.photoURL === undefined ? null : user.photoURL
        ).catch(error => {
          // Silent catch - don't block rendering on profile creation errors
          console.warn('Could not create profile. This may be due to Firestore permissions.', error);
        });
        
        // Subscribe to profile changes
        unsubscribe = subscribeToProfile(user.uid);
      } catch (error) {
        console.warn('Error in UserProfileProvider:', error);
        // Don't block rendering on subscription errors
      }
    }
    
    // Cleanup subscription on unmount or user change
    return () => {
      unsubscribe();
    };
  }, [user, loading, subscribeToProfile, createProfileIfNotExists]);
  
  return <>{children}</>;
};
