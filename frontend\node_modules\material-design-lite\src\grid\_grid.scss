/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
* NOTE: Some rules here are applied using duplicate selectors.
* This is on purpose to increase their specificity when applied.
* For example: `.mdl-cell--1-col-phone.mdl-cell--1-col-phone`
*/

@import "../variables";

.mdl-grid {
  display: flex;
  flex-flow: row wrap;
  margin: 0 auto 0 auto;
  align-items: stretch;

  &.mdl-grid--no-spacing {
    padding: 0;
  }
}

.mdl-cell {
  box-sizing: border-box;
}


.mdl-cell--top {
  align-self: flex-start;
}

.mdl-cell--middle {
  align-self: center;
}

.mdl-cell--bottom {
  align-self: flex-end;
}

.mdl-cell--stretch {
  align-self: stretch;
}

.mdl-grid.mdl-grid--no-spacing > .mdl-cell {
  margin: 0;
}

// Define order override classes.
@for $i from 1 through $grid-max-columns {
  .mdl-cell--order-#{$i} {
    order: $i;
  }
}


// Mixins for width calculation.
@mixin partial-size($size, $columns, $gutter) {
  width: calc(#{(($size / $columns) * 100)+"%"} - #{$gutter});

  .mdl-grid--no-spacing > & {
    width: #{(($size / $columns) * 100)+"%"};
  }
}

@mixin full-size($gutter) {
  @include partial-size(1, 1, $gutter);
}

@mixin offset-size($size, $columns, $gutter) {
  margin-left: calc(#{(($size / $columns) * 100)+"%"} + #{$gutter / 2});

  .mdl-grid.mdl-grid--no-spacing > & {
    margin-left: #{(($size / $columns) * 100)+"%"};
  }
}



////////// Phone //////////

@media (max-width: $grid-tablet-breakpoint - 1) {
  .mdl-grid {
    padding: $grid-phone-margin - ($grid-phone-gutter / 2);
  }

  .mdl-cell {
    margin: $grid-phone-gutter / 2;
    @include partial-size($grid-cell-default-columns, $grid-phone-columns,
        $grid-phone-gutter);
  }

  .mdl-cell--hide-phone {
    display: none !important;
  }

  // Define order override classes.
  @for $i from 1 through $grid-max-columns {
    .mdl-cell--order-#{$i}-phone.mdl-cell--order-#{$i}-phone {
      order: $i;
    }
  }

  // Define partial sizes for columnNumber < totalColumns.
  @for $i from 1 through ($grid-phone-columns - 1) {
    .mdl-cell--#{$i}-col,
    .mdl-cell--#{$i}-col-phone.mdl-cell--#{$i}-col-phone {
      @include partial-size($i, $grid-phone-columns, $grid-phone-gutter);
    }
  }

  // Define 100% for everything else.
  @for $i from $grid-phone-columns through $grid-desktop-columns {
    .mdl-cell--#{$i}-col,
    .mdl-cell--#{$i}-col-phone.mdl-cell--#{$i}-col-phone {
      @include full-size($grid-phone-gutter);
    }
  }

  // Define valid phone offsets.
  @for $i from 1 through ($grid-phone-columns - 1) {
    .mdl-cell--#{$i}-offset,
    .mdl-cell--#{$i}-offset-phone.mdl-cell--#{$i}-offset-phone {
      @include offset-size($i, $grid-phone-columns, $grid-phone-gutter);
    }
  }
}


////////// Tablet //////////

@media (min-width: $grid-tablet-breakpoint) and (max-width: $grid-desktop-breakpoint - 1) {
  .mdl-grid {
    padding: $grid-tablet-margin - ($grid-tablet-gutter / 2);
  }

  .mdl-cell {
    margin: $grid-tablet-gutter / 2;
    @include partial-size($grid-cell-default-columns, $grid-tablet-columns,
        $grid-tablet-gutter);
  }

  .mdl-cell--hide-tablet {
    display: none !important;
  }

  // Define order override classes.
  @for $i from 1 through $grid-max-columns {
    .mdl-cell--order-#{$i}-tablet.mdl-cell--order-#{$i}-tablet {
      order: $i;
    }
  }

  // Define partial sizes for columnNumber < totalColumns.
  @for $i from 1 through ($grid-tablet-columns - 1) {
    .mdl-cell--#{$i}-col,
    .mdl-cell--#{$i}-col-tablet.mdl-cell--#{$i}-col-tablet {
      @include partial-size($i, $grid-tablet-columns, $grid-tablet-gutter);
    }
  }

  // Define 100% for everything else.
  @for $i from $grid-tablet-columns through $grid-desktop-columns {
    .mdl-cell--#{$i}-col,
    .mdl-cell--#{$i}-col-tablet.mdl-cell--#{$i}-col-tablet {
      @include full-size($grid-tablet-gutter);
    }
  }

  // Define valid tablet offsets.
  @for $i from 1 through ($grid-tablet-columns - 1) {
    .mdl-cell--#{$i}-offset,
    .mdl-cell--#{$i}-offset-tablet.mdl-cell--#{$i}-offset-tablet {
      @include offset-size($i, $grid-tablet-columns, $grid-tablet-gutter);
    }
  }
}


////////// Desktop //////////

@media (min-width: $grid-desktop-breakpoint) {
  .mdl-grid {
    padding: $grid-desktop-margin - ($grid-desktop-gutter / 2);
  }

  .mdl-cell {
    margin: $grid-desktop-gutter / 2;
    @include partial-size($grid-cell-default-columns, $grid-desktop-columns,
        $grid-desktop-gutter);
  }

  .mdl-cell--hide-desktop {
    display: none !important;
  }

  // Define order override classes.
  @for $i from 1 through $grid-max-columns {
    .mdl-cell--order-#{$i}-desktop.mdl-cell--order-#{$i}-desktop {
      order: $i;
    }
  }

  // Define partial sizes for all numbers of columns.
  @for $i from 1 through $grid-desktop-columns {
    .mdl-cell--#{$i}-col,
    .mdl-cell--#{$i}-col-desktop.mdl-cell--#{$i}-col-desktop {
      @include partial-size($i, $grid-desktop-columns, $grid-desktop-gutter);
    }
  }

  // Define valid desktop offsets.
  @for $i from 1 through ($grid-desktop-columns - 1) {
    .mdl-cell--#{$i}-offset,
    .mdl-cell--#{$i}-offset-desktop.mdl-cell--#{$i}-offset-desktop {
      @include offset-size($i, $grid-desktop-columns, $grid-desktop-gutter);
    }
  }
}
