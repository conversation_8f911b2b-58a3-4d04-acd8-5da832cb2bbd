{"name": "code-scribe-gis", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "echo 'Tests verfügbar unter: http://localhost:5173/src/tests/index.html'", "test:open": "start http://localhost:5173/src/tests/index.html"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@monaco-editor/react": "4.7.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.6.0", "file-saver": "2.0.5", "file-type": "20.1.0", "firebase": "10.8.0", "html2canvas": "1.4.1", "html2pdf.js": "0.10.3", "jspdf": "2.5.1", "lucide-react": "^0.439.0", "next-themes": "^0.3.0", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-error-boundary": "4.0.11", "react-firebase-hooks": "5.1.1", "react-hook-form": "^7.53.0", "react-markdown": "9.0.1", "react-router-dom": "6.17.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "4.5.5", "react-helmet": "^6.1.0", "react-firebaseui": "^6.0.0"}, "devDependencies": {"@types/file-saver": "^2", "@types/node": "20.6.2", "@types/react": "18.2.32", "@types/react-dom": "18.2.7", "@types/react-helmet": "^6.1.0", "@vitejs/plugin-react": "^4.0.0", "@vitejs/plugin-react-swc": "3.3.2", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "8.45.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.3", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "typescript": "5.2.2", "vite": "4.4.5", "vite-plugin-html-inject": "^1.0.1", "vite-tsconfig-paths": "4.2.2"}}