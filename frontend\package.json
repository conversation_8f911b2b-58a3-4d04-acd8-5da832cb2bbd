{"name": "code-scribe-gis", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@11labs/react": "0.0.4", "@ag-grid-enterprise/all-modules": "27.3.0", "@ag-ui/core": "0.0.28", "@algolia/autocomplete-js": "^1.17.6", "@amcharts/amcharts5": "5.11.2", "@apollo/client": "^3.11.8", "@auth0/auth0-react": "2.3.0", "@aws-sdk/client-cognito-identity-provider": "^3.645.0", "@aws-sdk/credential-providers": "^3.645.0", "@blocknote/core": "0.26.0", "@blocknote/mantine": "0.26.0", "@blocknote/react": "0.26.0", "@builder.io/dev-tools": "1.6.19", "@builder.io/react": "8.0.12", "@bwip-js/browser": "4.5.2", "@capsizecss/core": "4.1.2", "@chakra-ui/icons": "2.1.1", "@chakra-ui/react": "2.8.2", "@chakra-ui/system": "2.6.2", "@ckeditor/ckeditor5-build-classic": "42.0.2", "@ckeditor/ckeditor5-react": "8.0.0", "@clerk/clerk-react": "5.22.13", "@copilotkit/backend": "0.37.0", "@copilotkit/react-core": "1.8.14", "@copilotkit/react-textarea": "1.8.14", "@copilotkit/react-ui": "1.8.14", "@dnd-kit/core": "6.1.0", "@dnd-kit/sortable": "8.0.0", "@dnd-kit/utilities": "3.2.2", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@fontsource/roboto": "5.1.1", "@grapesjs/react": "^2.0.0", "@headlessui/react": "^2.2.1", "@hello-pangea/dnd": "18.0.1", "@heroicons/react": "2.2.0", "@heygen/streaming-avatar": "2.0.8", "@hookform/resolvers": "^3.9.0", "@intercom/messenger-js-sdk": "^0.0.14", "@lexical/react": "0.27.1", "@liveblocks/client": "2.9.1", "@liveblocks/react": "2.9.1", "@liveblocks/zustand": "2.9.1", "@mapbox/mapbox-gl-draw": "1.5.0", "@mastra/agui": "1.0.2", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@monaco-editor/react": "4.7.0", "@mui/icons-material": "6.3.1", "@mui/material": "6.3.1", "@mysten/sui": "1.16.0", "@newrelic/browser-agent": "^1.279.1", "@novnc/novnc": "1.6.0-beta", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@openzeppelin/contracts": "5.3.0", "@paypal/react-paypal-js": "8.8.3", "@pdfme/common": "5.3.3", "@pdfme/generator": "5.3.3", "@pdfme/schemas": "5.3.3", "@pdfme/ui": "5.3.3", "@play-ai/agent-web-sdk": "^0.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-google-maps/api": "2.20.3", "@react-pdf/renderer": "3.4.4", "@react-three/drei": "9.80.0", "@react-three/fiber": "8.13.1", "@react-three/xr": "5.7.0", "@remotion/player": "4.0.264", "@reown/appkit": "1.5.2", "@reown/appkit-adapter-solana": "1.5.3", "@reown/appkit-adapter-wagmi": "1.5.2", "@rjsf/core": "5.24.10", "@rjsf/mui": "5.24.10", "@rjsf/utils": "5.24.10", "@rjsf/validator-ajv8": "5.24.10", "@sanity/client": "6.28.0", "@shopify/app-bridge-react": "4.1.10", "@shopify/polaris": "13.9.5", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-react": "0.15.35", "@solana/wallet-adapter-react-ui": "0.9.38", "@solana/wallet-adapter-wallets": "0.19.32", "@solana/web3.js": "1", "@splinetool/react-spline": "4.0.0", "@stackframe/react": "patch:@stackframe/react@npm%3A2.8.12#~/.yarn/patches/@stackframe-react-npm-2.8.12-81578128be.patch", "@storyblok/react": "4.6.1", "@stripe/firestore-stripe-payments": "0.0.6", "@stripe/react-stripe-js": "2.9.0", "@stripe/stripe-js": "5.0.0", "@suiet/wallet-kit": "0.3.3", "@supabase/auth-ui-react": "0.4.7", "@supabase/auth-ui-shared": "0.1.8", "@supabase/supabase-js": "2.47.3", "@tailwindcss/typography": "0.5.10", "@talkjs/react": "0.1.11", "@tanstack/react-query": "5.79.0", "@tanstack/react-table": "8.21.3", "@tanstack/react-virtual": "3.13.9", "@tiptap/core": "2.12.0", "@tiptap/extension-bullet-list": "2.9.1", "@tiptap/extension-character-count": "2.9.1", "@tiptap/extension-code-block": "2.9.1", "@tiptap/extension-code-block-lowlight": "2.9.1", "@tiptap/extension-collaboration": "2.9.1", "@tiptap/extension-collaboration-cursor": "2.9.1", "@tiptap/extension-color": "2.9.1", "@tiptap/extension-document": "2.9.1", "@tiptap/extension-dropcursor": "2.9.1", "@tiptap/extension-focus": "2.9.1", "@tiptap/extension-font-family": "2.9.1", "@tiptap/extension-heading": "2.9.1", "@tiptap/extension-highlight": "2.9.1", "@tiptap/extension-horizontal-rule": "2.9.1", "@tiptap/extension-image": "2.9.1", "@tiptap/extension-link": "2.9.1", "@tiptap/extension-ordered-list": "2.9.1", "@tiptap/extension-paragraph": "2.9.1", "@tiptap/extension-placeholder": "2.9.1", "@tiptap/extension-subscript": "2.9.1", "@tiptap/extension-superscript": "2.9.1", "@tiptap/extension-table": "2.9.1", "@tiptap/extension-table-cell": "2.11.5", "@tiptap/extension-table-header": "2.9.1", "@tiptap/extension-table-row": "2.9.1", "@tiptap/extension-task-item": "2.9.1", "@tiptap/extension-task-list": "2.9.1", "@tiptap/extension-text-align": "2.9.1", "@tiptap/extension-text-style": "2.9.1", "@tiptap/extension-typography": "2.9.1", "@tiptap/extension-underline": "2.9.1", "@tiptap/pm": "2.9.1", "@tiptap/react": "2.9.1", "@tiptap/starter-kit": "2.9.1", "@tomtom-international/web-sdk-maps": "6.25.0", "@tomtom-international/web-sdk-services": "6.25.0", "@turf/turf": "7.2.0", "@twilio/voice-sdk": "^2.12.4", "@uidotdev/usehooks": "2.4.1", "@vanilla-extract/css": "1.17.2", "@vanilla-extract/css-utils": "0.1.4", "@vanilla-extract/dynamic": "2.1.3", "@vanilla-extract/recipes": "0.5.5", "@vanilla-extract/sprinkles": "1.6.3", "@vanilla-extract/vite-plugin": "5.0.2", "@vanyapr/react-image-magnifiers": "1.4.2", "@vapi-ai/web": "2.1.3", "@vis.gl/react-google-maps": "^1.5.2", "@wavesurfer/react": "1.0.7", "@webcomponents/shadycss": "1.11.2", "@xyflow/react": "12.4.1", "@xzdarcy/react-timeline-editor": "0.1.9", "add-to-calendar-button": "2.9.1", "add-to-calendar-button-react": "2.9.1", "ag-grid-react": "32.1.0", "agora-rtc-react": "2.3.0", "agora-rtc-sdk-ng": "4.23.1", "algoliasearch": "^4.24.0", "amazon-cognito-identity-js": "^6.3.12", "amplitude-js": "8.21.9", "audio-decode": "2.2.2", "aws-sdk": "^2.1691.0", "blockly": "^11.2.2", "bpmn-js": "^18.5.0", "cdgplayer": "0.1.17", "chart.js": "4.4.7", "ckeditor5": "42.0.2", "ckeditor5-premium-features": "42.0.2", "class-variance-authority": "^0.7.0", "classnames": "2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "convex": "1.11.2", "daisyui": "4.12.23", "date-fns": "^3.6.0", "docx": "9.3.0", "embla-carousel-react": "^8.2.1", "epubjs": "0.3.93", "fabric": "6.5.3", "fflate": "0.8.2", "file-saver": "2.0.5", "file-type": "20.1.0", "firebase": "10.8.0", "framer-motion": "10.18.0", "grapejs": "^2.1.2", "grapesjs": "0.22.5", "grapesjs-react": "4.0.2", "heic2any": "0.0.4", "hls.js": "1.6.5", "html2canvas": "1.4.1", "html2pdf.js": "0.10.3", "html5-qrcode": "2.3.8", "i18next": "24.2.0", "i18next-browser-languagedetector": "8.0.2", "i18next-http-backend": "^3.0.2", "i18next-parser": "9.3.0", "i18next-scanner": "4.6.0", "idb": "8.0.2", "immer": "10.1.1", "input-otp": "^1.2.4", "jspdf": "2.5.1", "jszip": "3.10.1", "konva": "9.3.18", "leaflet": "1.9.4", "lexical": "0.27.1", "lightweight-charts": "4.2.1", "livekit-client": "2.7.3", "lodash": "4.17.21", "lucide-react": "^0.439.0", "mammoth": "1.8.0", "mapbox-gl": "3.1.2", "maplibre-gl": "^5.4.0", "mathjs": "14.2.1", "mermaid": "^10.9.0", "mixpanel-browser": "2.52.0", "next-themes": "^0.3.0", "openai-partial-stream": "0.3.9", "p5": "2.0.3", "pdfjs-dist": "^4.8.69", "plotly.js": "2.27.1", "posthog-js": "1.245.0", "prosemirror-model": "^1.25.1", "qrcode.react": "4.2.0", "react": "18.3.1", "react-alice-carousel": "^2.9.1", "react-beautiful-dnd": "13.1.1", "react-big-calendar": "1.17.1", "react-big-schedule": "4.4.5", "react-calendar-heatmap": "1.10.0", "react-calendar-timeline": "0.30.0-beta.3", "react-chessboard": "4.7.3", "react-circular-progressbar": "^2.1.0", "react-colorful": "5.6.1", "react-confetti": "6.1.0", "react-cookie-consent": "9.0.0", "react-datasheet-grid": "4.11.4", "react-day-picker": "8.10.1", "react-diff-viewer": "3.1.1", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-easy-crop": "5.2.0", "react-email-editor": "1.7.11", "react-error-boundary": "4.0.11", "react-firebase-hooks": "5.1.1", "react-firebaseui": "6.0.0", "react-force-graph": "1.47.6", "react-ga4": "2.1.0", "react-google-charts": "5.2.1", "react-google-recaptcha-v3": "1.11.0", "react-grid-layout": "1.5.0", "react-h5-audio-player": "3.10.0", "react-helmet": "6.1.0", "react-hook-form": "^7.53.0", "react-i18next": "15.4.0", "react-icons": "5.0.1", "react-image-crop": "11.0.10", "react-image-magnify": "2.7.4", "react-intersection-observer": "9.14.1", "react-konva": "18", "react-leaflet": "4.2.1", "react-loading-skeleton": "^3.5.0", "react-lottie-player": "1.5.6", "react-map-gl": "7.1.7", "react-markdown": "9.0.1", "react-mentions": "4.4.10", "react-onesignal": "^3.2.2", "react-phone-input-2": "2.15.1", "react-plaid-link": "^4.0.1", "react-player": "2.16.0", "react-plotly.js": "2.6.0", "react-pro-sidebar": "^1.1.0", "react-quill": "2.0.0", "react-reader": "2.0.12", "react-remark": "2.1.0", "react-resizable-panels": "^2.1.2", "react-rnd": "^10.5.2", "react-router-dom": "6.17.0", "react-select": "5.8.0", "react-shadow": "20.6.0", "react-signature-canvas": "1.1.0-alpha.2", "react-slick": "0.30.2", "react-social-media-embed": "2.5.18", "react-speech-recognition": "4.0.1", "react-table": "7.8.0", "react-tabs": "^4.3.0", "react-to-pdf": "2.0.0", "react-to-print": "3.0.5", "react-use-websocket": "4.9.0", "react-virtualized-auto-sizer": "1.0.25", "react-webcam": "7.2.0", "react-wheel-of-prizes": "^1.1.0", "react-window": "1.8.11", "react-zoom-pan-pinch": "^3.7.0", "react-zxing": "2.0.2", "recharts": "^2.12.7", "recordrtc": "5.6.2", "rehype-raw": "7.0.0", "remark-gfm": "4.0.0", "remotion": "4.0.264", "reveal.js": "^5.2.1", "rhino3dm": "8.17.0", "shepherd.js": "14.1.0", "sonner": "^1.5.0", "stream-chat": "8.43.0", "stream-chat-react": "12.5.1", "style-dictionary": "4.4.0", "sudoku-gen": "1.0.2", "survey-core": "2.1.1", "survey-creator-react": "2.1.1", "survey-react-ui": "2.1.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "talkjs": "0.31.0", "tesseract.js": "5.1.1", "three": "0.153.0", "tone": "15.0.4", "trading-vue-js": "1.0.2", "ts-morph": "^25.0.1", "tsx": "^4.19.3", "turndown": "7.2.0", "twilio-video": "2.31.0", "userflow.js": "2.12.1", "vaul": "^0.9.2", "viem": "2.21.51", "vinyl-fs": "3.0.3", "vite-plugin-html-inject": "1.1.2", "wagmi": "2.13.0", "wavesurfer.js": "7.8.11", "web-vitals": "4.2.4", "xlsx": "0.18.5", "xterm-addon-fit": "0.8.0", "xterm-for-react": "1.0.4", "zod": "^3.23.8", "zustand": "4.5.5"}, "devDependencies": {"@biomejs/biome": "1.8.3", "@sentry/react": "7.101.1", "@sentry/types": "7.101.1", "@types/amplitude-js": "^8", "@types/file-saver": "^2", "@types/firebase": "^3.2.1", "@types/leaflet": "1.9.14", "@types/lodash": "^4", "@types/mapbox-gl": "2.7.20", "@types/mapbox__mapbox-gl-draw": "^1", "@types/mixpanel-browser": "^2", "@types/node": "20.6.2", "@types/novnc__novnc": "^1", "@types/p5": "1.7.6", "@types/react": "18.2.32", "@types/react-beautiful-dnd": "^13", "@types/react-big-calendar": "^1", "@types/react-calendar-heatmap": "^1", "@types/react-dom": "18.2.7", "@types/react-grid-layout": "^1", "@types/react-helmet": "^6", "@types/react-image-magnify": "^2", "@types/react-mentions": "^4", "@types/react-slick": "^0", "@types/react-speech-recognition": "3.9.6", "@types/react-table": "^7", "@types/react-window": "^1", "@types/recordrtc": "^5", "@types/reveal.js": "^5", "@types/three": "^0", "@types/vinyl-fs": "^3", "@vitejs/plugin-react-swc": "3.3.2", "autoprefixer": "^10.4.20", "dotenv": "16.4.5", "eslint": "8.45.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.3", "openapi-types": "12.1.3", "postcss": "^8.4.45", "raw-body": "2.5.2", "tailwindcss": "^3.4.10", "ts-prune": "0.10.3", "type-fest": "4.6.0", "typescript": "5.2.2", "typescript-language-server": "4.3.2", "vite": "4.4.5", "vite-tsconfig-paths": "4.2.2", "@vitejs/plugin-react": "^4.0.0", "@firebase/app": "^0.11.1"}, "packageManager": "yarn@4.0.2", "nodeLinker": "pnpm"}