import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { firebaseAuth } from 'app';
import { createUserWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

export default function Register() {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleRegister = async () => {
    // Basic frontend validation
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      toast.error('Passwords do not match');
      return;
    }
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      toast.error('Password must be at least 6 characters long');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Attempt to create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(firebaseAuth, email, password);
      const user = userCredential.user;
      
      // Immediately show success toast for account creation
      toast.success('Account created successfully!');

      // Attempt to create the user profile in Firestore (non-critical)
      if (user) {
        try {
          // Dynamically import store to avoid potential hook rule issues if called conditionally later
          const { useUserProfileStore } = await import('utils/userProfileStore');
          await useUserProfileStore.getState().createProfileIfNotExists(
            user.uid,
            user.email === undefined ? null : user.email,
            user.displayName === undefined ? null : user.displayName, // Usually null on email creation
            user.photoURL === undefined ? null : user.photoURL // Usually null on email creation
          );
          console.log(`Profile creation/check initiated for user ${user.uid}`);
        } catch (profileError) {
          // Log profile creation error but don't block user flow
          console.warn('Non-critical error during profile creation/check:', profileError);
          toast.warning('Account created, but failed to initialize profile. You can update it later.');
        }
      } else {
         console.warn('User object not available immediately after creation.');
         toast.warning('Account created, but failed to retrieve user details for profile initialization.');
      }

      // Navigate to the home page after successful registration
      // No need for setTimeout if we use the user object from the credential
      navigate('/');

    } catch (error: any) {
      // Handle Firebase specific errors
      console.error('Registration failed:', error);
      let errorMessage = 'Registration failed. Please try again.';
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'This email address is already in use.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'The password is too weak. Please use at least 6 characters.';
      } else if (error.code === 'auth/invalid-email') {
          errorMessage = 'The email address is not valid.';
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      // Ensure loading state is turned off
      setIsLoading(false);
    }
  };

  const handleGoogleSignup = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const provider = new GoogleAuthProvider();
      // Add scopes for additional user information
      provider.addScope('profile');
      provider.addScope('email');
      
      await signInWithPopup(firebaseAuth, provider);
      
      // Small delay to ensure auth state updates before redirecting
      setTimeout(async () => {
        try {
          // Get the current user after auth state is updated
          const currentUser = firebaseAuth.currentUser;
          if (currentUser) {
            // Import and use profile store directly to avoid hook rules issues
            const { useUserProfileStore } = await import('utils/userProfileStore');
            await useUserProfileStore.getState().createProfileIfNotExists(
              currentUser.uid,
              currentUser.email === undefined ? null : currentUser.email,
              currentUser.displayName === undefined ? null : currentUser.displayName,
              currentUser.photoURL === undefined ? null : currentUser.photoURL
            );
            toast.success('Account created successfully!');
          }
        } catch (profileError) {
          console.warn('Non-critical error updating profile after Google sign-in:', profileError);
          // Continue with navigation even if profile update fails
        }
        
        // Navigate to the home page
        navigate('/');
      }, 500); // Small delay for auth state update
    } catch (error) {
      console.error('Google authentication failed:', error);
      setError('Google authentication failed');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 flex items-center justify-center mb-2">
            <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
            <span className="relative z-10 font-mono font-bold">GIS</span>
          </div>
          <CardTitle className="text-2xl font-mono tracking-wide">Create an Account</CardTitle>
          <CardDescription>
            Join CodeScribe GIS to start creating documentation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="<EMAIL>" 
                value={email} 
                onChange={(e) => setEmail(e.target.value)}
                className="font-mono"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password" 
                type="password" 
                placeholder="••••••••" 
                value={password} 
                onChange={(e) => setPassword(e.target.value)}
                className="font-mono"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input 
                id="confirmPassword" 
                type="password" 
                placeholder="••••••••" 
                value={confirmPassword} 
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="font-mono"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && email && password && confirmPassword) {
                    handleRegister();
                  }
                }}
              />
            </div>
            
            {error && (
              <div className="text-sm font-medium text-destructive p-2 border border-destructive/20 bg-destructive/5 rounded-md mb-2">{error}</div>
            )}
            
            <Button 
              type="button"
              className="w-full justify-center" 
              disabled={isLoading || !email || !password || !confirmPassword}
              onClick={handleRegister}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Creating Account...</span>
                </div>
              ) : 'Create Account'}
            </Button>
            
            <div className="text-center">
              <Button 
                variant="link" 
                className="font-mono text-xs"
                asChild
              >
                <Link to="/login">Already have an account? Sign in</Link>
              </Button>
            </div>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-border/40"></span>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
              </div>
            </div>
            
            <Button 
              variant="outline"
              className="w-full justify-center space-x-2" 
              onClick={handleGoogleSignup}
              disabled={isLoading}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="h-5 w-5">
                <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"/>
                <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"/>
                <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"/>
                <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"/>
              </svg>
              <span>Sign up with Google</span>
            </Button>

            <div className="mt-6 text-center">
              <Button 
                variant="ghost" 
                size="sm"
                className="font-mono text-xs"
                asChild
              >
                <Link to="/">Back to Home</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
