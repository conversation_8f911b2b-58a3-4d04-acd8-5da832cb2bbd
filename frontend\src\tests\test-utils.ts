/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Minimale Test-Utilities ohne externe Dependencies
 */

// Einfache Test-Assertion-Funktionen
export class TestAssertions {
  static assertEqual<T>(actual: T, expected: T, message?: string): void {
    if (actual !== expected) {
      throw new Error(
        `Assertion failed: ${message || 'Values are not equal'}\n` +
        `Expected: ${JSON.stringify(expected)}\n` +
        `Actual: ${JSON.stringify(actual)}`
      );
    }
  }

  static assertTrue(condition: boolean, message?: string): void {
    if (!condition) {
      throw new Error(`Assertion failed: ${message || 'Condition is not true'}`);
    }
  }

  static assertFalse(condition: boolean, message?: string): void {
    if (condition) {
      throw new Error(`Assertion failed: ${message || 'Condition is not false'}`);
    }
  }

  static assertThrows(fn: () => void, expectedError?: string, message?: string): void {
    try {
      fn();
      throw new Error(`Assertion failed: ${message || 'Function did not throw an error'}`);
    } catch (error) {
      if (expectedError && !error.message.includes(expectedError)) {
        throw new Error(
          `Assertion failed: ${message || 'Wrong error thrown'}\n` +
          `Expected error containing: ${expectedError}\n` +
          `Actual error: ${error.message}`
        );
      }
    }
  }

  static assertNotNull<T>(value: T | null | undefined, message?: string): asserts value is T {
    if (value === null || value === undefined) {
      throw new Error(`Assertion failed: ${message || 'Value is null or undefined'}`);
    }
  }

  static assertArrayLength<T>(array: T[], expectedLength: number, message?: string): void {
    if (array.length !== expectedLength) {
      throw new Error(
        `Assertion failed: ${message || 'Array length mismatch'}\n` +
        `Expected length: ${expectedLength}\n` +
        `Actual length: ${array.length}`
      );
    }
  }

  static assertContains<T>(array: T[], item: T, message?: string): void {
    if (!array.includes(item)) {
      throw new Error(
        `Assertion failed: ${message || 'Array does not contain item'}\n` +
        `Array: ${JSON.stringify(array)}\n` +
        `Item: ${JSON.stringify(item)}`
      );
    }
  }
}

// Test-Runner-Klasse
export class TestRunner {
  private tests: Array<{ name: string; fn: () => void | Promise<void> }> = [];
  private results: Array<{ name: string; passed: boolean; error?: string; duration: number }> = [];

  addTest(name: string, testFn: () => void | Promise<void>): void {
    this.tests.push({ name, fn: testFn });
  }

  async runAll(): Promise<void> {
    console.log(`🧪 Starte ${this.tests.length} Tests...\n`);

    for (const test of this.tests) {
      const startTime = performance.now();
      try {
        await test.fn();
        const duration = performance.now() - startTime;
        this.results.push({ name: test.name, passed: true, duration });
        console.log(`✅ ${test.name} (${duration.toFixed(2)}ms)`);
      } catch (error) {
        const duration = performance.now() - startTime;
        this.results.push({ 
          name: test.name, 
          passed: false, 
          error: error.message, 
          duration 
        });
        console.log(`❌ ${test.name} (${duration.toFixed(2)}ms)`);
        console.log(`   Fehler: ${error.message}`);
      }
    }

    this.printSummary();
  }

  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`\n📊 Test-Zusammenfassung:`);
    console.log(`   Bestanden: ${passed}`);
    console.log(`   Fehlgeschlagen: ${failed}`);
    console.log(`   Gesamt: ${this.results.length}`);
    console.log(`   Dauer: ${totalDuration.toFixed(2)}ms`);
    console.log(`   Erfolgsrate: ${((passed / this.results.length) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log(`\n❌ Fehlgeschlagene Tests:`);
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
    }
  }

  getResults() {
    return this.results;
  }

  getCoverage(): number {
    const passed = this.results.filter(r => r.passed).length;
    return (passed / this.results.length) * 100;
  }
}

// Mock-Funktionen für Tests
export class MockUtils {
  static createMockFunction<T extends (...args: any[]) => any>(
    returnValue?: ReturnType<T>
  ): T & { calls: Parameters<T>[]; callCount: number } {
    const mockFn = ((...args: Parameters<T>) => {
      mockFn.calls.push(args);
      mockFn.callCount++;
      return returnValue;
    }) as T & { calls: Parameters<T>[]; callCount: number };

    mockFn.calls = [];
    mockFn.callCount = 0;
    return mockFn;
  }

  static createMockObject<T extends Record<string, any>>(
    properties: Partial<T> = {}
  ): T {
    return { ...properties } as T;
  }
}

// DOM-Test-Utilities (falls DOM-Tests benötigt werden)
export class DOMTestUtils {
  static createElement(tag: string, attributes: Record<string, string> = {}): HTMLElement {
    const element = document.createElement(tag);
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
    return element;
  }

  static createMockEvent(type: string, properties: Record<string, any> = {}): Event {
    const event = new Event(type);
    Object.assign(event, properties);
    return event;
  }

  static waitForElement(selector: string, timeout = 1000): Promise<Element> {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }
}
