import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { doc, collection, getDoc, getDocs, getFirestore, deleteDoc, query, where } from 'firebase/firestore';
// Navigation import is now in the combined import above
import { useUserGuardContext, firebaseApp } from "app";
import { useDocumentStore, DocumentMeta, Document, DocumentExportCache } from "utils/documentStore";
import { AccountPlan, PLAN_FEATURES, COLLECTIONS } from "utils/firebaseConfig";
import { useUserProfileStore } from "utils/userProfileStore";
import { exportAsMarkdown, exportAsHTML, exportAsText, generateDocx, generatePdf, downloadFile, downloadBlob } from "utils/exportUtils";
import { softDeleteDocument } from "utils/softDeleteDocument";
import { fixAllDocumentPermissions } from "utils/documentPermissions";
import { updateDoc } from 'firebase/firestore';
import brain from "brain";
import { API_URL } from "app";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "components/Badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { ExportDialog } from "components/ExportDialog";
import { DeleteDocumentDialog } from "components/DeleteDocumentDialog";
import { toast } from "sonner";
import { FileCode, Edit2, Trash2, Search, Plus, FolderPlus, Tag, Calendar, FileText, Eye, Download, Lock, Unlock, ArrowLeft } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuSubContent } from "@/components/ui/dropdown-menu";

export default function Documents() {
  const navigate = useNavigate();
  const { user } = useUserGuardContext();
  const { profile } = useUserProfileStore();
  const { documents, fetchUserDocuments, deleteDocument, error: storeError } = useDocumentStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTag, setActiveTag] = useState<string | null>(null);
  const [filteredDocs, setFilteredDocs] = useState<DocumentMeta[]>([]);

  // UI state
  const [deleteTargetId, setDeleteTargetId] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  // New state to store the full document with content for export
  const [exportDocument, setExportDocument] = useState<Document | null>(null);

  // State for document viewing/editing
  const [viewingDocument, setViewingDocument] = useState<Document | null>(null);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);

  // Dialog state for delete confirmation
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  // Fetch the user's documents when the component mounts and check for view/edit parameters
  useEffect(() => {
    if (user?.uid) {
      setIsLoading(true);
      fetchUserDocuments(user.uid)
        .finally(() => setIsLoading(false));

      // The fetchUserDocuments already filters out deleted documents

      // Check for URL parameters
      const params = new URLSearchParams(window.location.search);
      const docId = params.get('id');
      const viewMode = params.get('view') === 'true';
      const editMode = params.get('edit') === 'true';

      if (docId) {
        const loadDocument = async () => {
          try {
            toast.loading('Loading document...');

            // First check if user is logged in
            if (!user?.uid) {
              toast.error('You must be logged in to view this document');
              return;
            }

            try {
              // Fetch the document directly
              const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);
              const docSnapshot = await getDoc(docRef);

              if (!docSnapshot.exists()) {
                toast.error('Document not found');
                return;
              }

              const metaData = docSnapshot.data();
              console.log('[Firestore Read Debug] Document MetaData:', metaData);
              console.log('[Firestore Read Debug] Current User UID:', user?.uid);
              console.log('[Firestore Read Debug] Document UserID Field from MetaData:', metaData?.userId);
              console.log('[Firestore Read Debug] Checking permission block. metaData.isPublic:', metaData?.isPublic); // Added log
              console.log('[VIEW MODE DEBUG] Raw metaData from Firestore:', metaData); // LOGGING METADATA

              // Check if user has permission to access this document
              if (metaData.userId !== user.uid && !metaData.isPublic) {
                console.log('[Firestore Read Debug] Entered permission fix block.'); // Added log
                // Try to fix permissions first
                toast.loading('Fixing document permissions...');
                await fixAllDocumentPermissions(user.uid);

                // Try again after fixing permissions
                const updatedSnapshot = await getDoc(docRef);
                if (!updatedSnapshot.exists() || 
                    (updatedSnapshot.data().userId !== user.uid && !updatedSnapshot.data().isPublic)) {
                  toast.error('Missing or insufficient permissions');
                  return;
                }
              }

              console.log('[Firestore Read Debug] Attempting to fetch content subcollection...'); // Added log
              // Get content subcollection
              const contentSnap = await getDocs(collection(docRef, 'content'));

              if (contentSnap.empty) {
                toast.error('Document content not found');
                return;
              }

              const contentData = contentSnap.docs[0].data();

              console.log('[VIEW MODE DEBUG] Value of metaData.project_context JUST BEFORE assignment:', metaData.project_context); // NEUER LOG

              const document: Document = {
                id: docId,
                title: metaData.title,
                description: metaData.description,
                createdAt: metaData.createdAt?.toDate(),
                updatedAt: metaData.updatedAt?.toDate(),
                userId: metaData.userId,
                language: metaData.language, // MYA-17: Restored
                codeSize: metaData.codeSize,
                isPublic: metaData.isPublic,
                tags: metaData.tags || [],
                project_context: metaData.project_context, // MYA-17: Restored
                content: {
                  originalCode: contentData.originalCode,
                  generatedDocumentation: contentData.generatedDocumentation,
                  parameters: contentData.parameters,
                  toolsUsed: contentData.toolsUsed,
                  workflow: contentData.workflow,
                  manualEdits: contentData.manualEdits?.map((edit: any) => ({
                    ...edit,
                    timestamp: edit.timestamp?.toDate()
                  })),
                  exportCache: contentData.exportCache || []
                },
                author: metaData.author, // MYA-17: Restored
                version: metaData.version, // MYA-17: Restored
                changeLog: metaData.changeLog, // MYA-17: Restored (though not directly used by Preview, good to have for consistency)
              };

              // NEUER LOG HIER:
              console.log('[VIEW MODE DEBUG] Constructed "document" object before setViewingDocument:', document);
              console.log('[VIEW MODE DEBUG] project_context from constructed "document":', document.project_context);

              if (viewMode) {
                setViewingDocument(document);
                toast.success(`Viewing document: ${document.title}`);
              } else if (editMode) {
                // Keep original behavior for editMode for now
                setEditingDocument(document);
                toast.success(`Editing document: ${document.title}`);
              }

            } catch (err) {
              console.error('Error accessing document:', err);
              if (err instanceof Error && err.message.includes('permissions')) {
                toast.error('Permission error: Try using the Fix Permissions button first');
              } else {
                toast.error('Failed to access document');
              }
            } finally {
              toast.dismiss();
            }
          } catch (err) {
            console.error('Error loading document:', err);
            toast.error('Failed to load document');
          }
        };

        loadDocument();
      }
    }
  }, [user?.uid, fetchUserDocuments]);

  // Set error from store
  useEffect(() => {
    if (storeError) {
      setError(storeError);
    }
  }, [storeError]);

  // Filter documents when search term or active tag changes
  useEffect(() => {
    if (!documents) return;

    // Apply search filter
    let filtered = documents;

    if (searchTerm.trim()) {
      const lowerSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        doc => 
          doc.title.toLowerCase().includes(lowerSearch) ||
          (doc.description?.toLowerCase().includes(lowerSearch)) ||
          doc.tags.some(tag => tag.toLowerCase().includes(lowerSearch))
      );
    }

    // Apply tag filter if active
    if (activeTag) {
      filtered = filtered.filter(doc => 
        doc.tags && doc.tags.includes(activeTag)
      );
    }

    setFilteredDocs(filtered);
  }, [searchTerm, activeTag, documents]);

  // Extract all unique tags from documents
  const allTags = Array.from(new Set(
    documents.flatMap(doc => doc.tags || [])
  )).sort();

  // Get the user's plan
  const userPlan = profile?.accountPlan || AccountPlan.FREE;
  const usedDocuments = documents.length;
  const maxDocuments = PLAN_FEATURES[userPlan].maxDocuments;
  const canCreateNew = usedDocuments < maxDocuments;

  // Create a new document
  const handleCreateNew = () => {
    if (!canCreateNew) {
      toast.error(`You've reached your plan limit of ${maxDocuments} documents. Upgrade to create more.`);
      return;
    }

    if (!user?.uid) {
      toast.error('You must be logged in to create a document');
      return;
    }

    try {
      toast.success("Creating a new document...");
      // Use direct window.location for reliable navigation
      const basePath = window.location.pathname.endsWith('/') ? 
        window.location.pathname : 
        window.location.pathname + '/';
      window.location.href = './editor?new=true';
    } catch (error) {
      console.error('Error navigating to editor:', error);
      toast.error('Could not navigate to editor. Please try again.');
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('default', {
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle document selection
  const handleSelectDocument = (docId: string) => {
    // Use direct window.location for reliable navigation
    const basePath = window.location.pathname.endsWith('/') ? 
      window.location.pathname : 
      window.location.pathname + '/';
    window.location.href = `./editor?id=${docId}`;
  };

  // Handle document deletion with confirmation
  const handleDeleteDocument = async (docId: string) => {
    console.log('Delete document triggered for ID:', docId);
    if (window.confirm('Sind Sie sicher, dass Sie dieses Dokument löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.')) {
      try {
        setDeleteTargetId(docId); // Set target ID for animation
        const toastId = toast.loading('Dokument wird gelöscht...');
        console.log('Delete confirmation accepted, toast ID:', toastId);

        // First, verify that the user is logged in
        if (!user?.uid) {
          toast.error('Sie müssen angemeldet sein, um Dokumente zu löschen');
          setDeleteTargetId(null);
          return;
        }

        try {
          console.log('Referencing document in Firestore:', docId);
          // Reference directly from Firestore
          const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);

          // Check if document exists and belongs to current user
          console.log('Fetching document snapshot');
          const docSnap = await getDoc(docRef);
          if (!docSnap.exists()) {
            toast.error('Dokument nicht gefunden');
            setDeleteTargetId(null);
            return;
          }

          const docData = docSnap.data();
          console.log('Document data retrieved:', docData);
          if (docData.userId !== user.uid) {
            toast.error('Sie haben keine Berechtigung, dieses Dokument zu löschen');
            setDeleteTargetId(null);
            return;
          }

          // Try to repair permissions first if needed
          if (docData.needsPermissionFix) {
            console.log('Fixing permissions before deletion');
            await fixAllDocumentPermissions(user.uid);
          }

          console.log('Performing soft delete operation');
          // Implement soft-delete function using direct Firestore update
          await updateDoc(docRef, {
            isDeleted: true,
            deletedAt: new Date()
          });
          console.log('Soft delete completed successfully');

          // Remove from local state
          setFilteredDocs(prevDocs => prevDocs.filter(doc => doc.id !== docId));
          console.log('Removed document from local state');

          // Refresh document list
          if (user?.uid) {
            console.log('Refreshing document list');
            await fetchUserDocuments(user.uid);
          }

          toast.success('Dokument erfolgreich gelöscht');
          console.log('Delete operation completed successfully');
        } catch (deleteErr) {
          console.error('Error in document deletion:', deleteErr);
          toast.error('Berechtigungsfehler: Versuchen Sie zuerst die Schaltfläche "Fix Permissions" und versuchen Sie es dann erneut');
        }

        toast.dismiss();
        setTimeout(() => setDeleteTargetId(null), 300); // Clear after animation
      } catch (err) {
        console.error('Error deleting document:', err);
        toast.error('Fehler beim Löschen des Dokuments: ' + (err instanceof Error ? err.message : 'Unbekannter Fehler'));
        setDeleteTargetId(null); // Clear on error
      }
    } else {
      console.log('Delete confirmation rejected');
    }
  };

  // Handle document export dialog opening
  const handleExportDialog = async (docId: string) => {
    console.log('Export dialog triggered for document ID:', docId);
    try {
      toast.loading('Preparing document for export...');
      // Fetch the full document first
      const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);
      console.log('Fetching document from Firestore:', docRef.path);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        console.log('Document exists, fetching metadata');
        // Get document metadata
        const metaData = docSnap.data();

        // Get content subcollection
        console.log('Fetching content subcollection');
        const contentSnap = await getDocs(collection(docRef, 'content'));

        if (contentSnap.empty) {
          console.error('Content subcollection is empty');
          toast.error('Document content not found');
          return;
        }

        const contentData = contentSnap.docs[0].data();
        console.log('Content data retrieved successfully');

        // Create full document object with default values for missing fields
        const fullDocument: Document = {
          id: docId,
          title: metaData.title || 'Untitled Document',
          description: metaData.description || '',
          createdAt: metaData.createdAt?.toDate() || new Date(),
          updatedAt: metaData.updatedAt?.toDate() || new Date(),
          userId: metaData.userId,
          language: metaData.language || 'python',
          codeSize: metaData.codeSize || 0,
          isPublic: metaData.isPublic || false,
          tags: metaData.tags || [],
          content: {
            originalCode: contentData.originalCode || '',
            generatedDocumentation: contentData.generatedDocumentation || null,
            parameters: contentData.parameters || [],
            toolsUsed: contentData.toolsUsed || [],
            workflow: contentData.workflow || null,
            manualEdits: contentData.manualEdits?.map((edit: any) => ({
              ...edit,
              timestamp: edit.timestamp?.toDate() || new Date()
            })) || [],
            exportCache: contentData.exportCache || []
          }
        };

        console.log('Document object created, setting state and opening export dialog');
        console.log('Document has content:', !!fullDocument.content, 'original code length:', fullDocument.content.originalCode?.length || 0);

        // Before setting state, record current UI state
        console.log('UI state before export dialog opens:', {
          exportDialogCurrentlyOpen: exportDialogOpen,
          currentExportDocument: exportDocument ? exportDocument.id : 'none',
          viewingDocument: viewingDocument ? viewingDocument.id : 'none'
        });

        // Store the document in state and open dialog
        setExportDocument(fullDocument);
        setExportDialogOpen(true);

        // Verify state was updated
        console.log('UI state updated for export dialog:', {
          exportDialogOpen: true, 
          documentId: fullDocument.id
        });

        toast.dismiss();
      } else {
        console.error('Document not found');
        toast.error('Document not found');
      }
    } catch (err) {
      console.error('Error fetching document for export:', err);
      toast.error('Failed to prepare document for export');
      toast.dismiss();
    }
  };

  // Handle document export
  const handleExportDocument = async (document: Document, formats: string[]) => {
    try {
      setIsLoading(true);
      console.log('Starting export process for document:', document.id, 'formats:', formats);
      toast.loading(`Preparing export...`);

      // First check if user is logged in
      if (!user?.uid) {
        console.error('User not logged in');
        toast.error('You must be logged in to export documents');
        return;
      }

      // Create a deep copy of the document to avoid modifying the original
      const documentCopy = JSON.parse(JSON.stringify(document));

      // Verify document content exists
      if (!documentCopy || !documentCopy.content) {
        console.error('Document content missing:', documentCopy);
        toast.error('Document content is missing or invalid');
        return;
      }

      console.log('Document validated, proceeding with export');

      // Create a map to track which formats we've successfully exported
      const exportedFormats: Record<string, boolean> = {};

      // Check if we already have cached versions of any formats
      const cachedFormats = document.content.exportCache || [];
      console.log('Cached formats:', cachedFormats.map(cf => `${cf.format} (${new Date(cf.updatedAt).toLocaleString()})` ));

      const exportPromises = formats.map(async (format) => {
        try {
          console.log(`Processing format: ${format}`);
          // Check if we have a recent cached version (less than 24 hours old)
          const cachedFormat = cachedFormats.find(
            cache => cache && cache.format === format && 
              (new Date().getTime() - new Date(cache.updatedAt).getTime() < 86400000)
          );

          if (cachedFormat) {
            console.log(`Found cached version for ${format}:`, cachedFormat);
            // Use the cached version
            if (format === 'markdown' || format === 'html' || format === 'text') {
              // For text-based formats, we can download directly
              const extension = format === 'markdown' ? '.md' : format === 'html' ? '.html' : '.txt';
              const mimeType = format === 'markdown' ? 'text/markdown' : format === 'html' ? 'text/html' : 'text/plain';
              const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + extension;

              console.log(`Using cached ${format} data, downloading as ${filename}`);
              toast.info(`Using cached ${format.toUpperCase()} file`);
              downloadFile(cachedFormat.data, filename, mimeType);
              exportedFormats[format] = true;
            } else if (cachedFormat.data && typeof cachedFormat.data === 'string' && 
                (cachedFormat.data.startsWith('exports_'))) {
              // For binary formats stored in db.storage, we need to download from the backend
              console.log(`Downloading cached binary format ${format} from backend storage`);
              toast.info(`Downloading cached ${format.toUpperCase()} file`);

              // Create download URL from the storage key
              const downloadUrl = `${API_URL}/export/download/${encodeURIComponent(cachedFormat.data)}`;
              console.log(`Download URL: ${downloadUrl}`);
              console.log('Download URL for cached format:', downloadUrl);

              try {
                // Get the current user's auth token for authenticated requests
                const { auth } = await import('app');
                const authToken = await auth.getAuthToken();

                console.log('Got auth token for cached download request:', authToken ? 'Token available' : 'No token');

                // Set up headers with explicit Authorization header
                const headers: HeadersInit = {};
                if (authToken) {
                  headers['Authorization'] = `Bearer ${authToken}`;
                }

                console.log('Sending cached download request with auth headers');
                const response = await fetch(downloadUrl, {
                  credentials: 'include', // Include cookies
                  headers: headers // Add explicit Authorization header
                });

                if (!response.ok) {
                  console.error(`HTTP error when downloading ${format}:`, response.status, response.statusText);
                  throw new Error(`HTTP error! status: ${response.status}`);
                }

                console.log(`Successfully fetched ${format} file, creating download`);
                const blob = await response.blob();
                const extension = format === 'docx' ? '.docx' : '.pdf';
                const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + extension;

                downloadBlob(blob, filename);
                exportedFormats[format] = true;
              } catch (err) {
                console.error(`Error downloading cached ${format} file:`, err);
                toast.error(`Failed to download cached ${format.toUpperCase()} file. Generating new one...`);
                // Continue to generate a new version
              }
            } else {
              console.warn(`Invalid cache data for ${format}, regenerating`);
            }
          }

          // If we haven't exported this format yet, generate a new version
          if (!exportedFormats[format]) {
            console.log(`No valid cache found for ${format}, generating new version`);
            // We need to generate the format
            if (format === 'markdown') {
              const content = exportAsMarkdown(document);
              const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.md';
              downloadFile(content, filename, 'text/markdown');

              // Cache the result
              await updateExportCache(document.id, format, content);
              exportedFormats[format] = true;
            } 
            else if (format === 'html') {
              const content = exportAsHTML(document);
              const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.html';
              downloadFile(content, filename, 'text/html');

              // Cache the result
              await updateExportCache(document.id, format, content);
              exportedFormats[format] = true;
            }
            else if (format === 'text') {
              const content = exportAsText(document);
              const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.txt';
              downloadFile(content, filename, 'text/plain');

              // Cache the result
              await updateExportCache(document.id, format, content);
              exportedFormats[format] = true;
            }
            else if (format === 'docx' || format === 'pdf') {
              // For DOCX and PDF, we need to use the backend API
              console.log(`Generating ${format} file via backend API`);
              toast.loading(`Generating ${format.toUpperCase()} file...`);

              try {
                // First prepare the document for server-side processing
                if (format === 'docx') {
                  // For DOCX we need to wrap the HTML in a JSON structure
                  documentCopy.content.html_wrapper = JSON.stringify({
                    html: exportAsHTML(documentCopy),
                    title: documentCopy.title
                  });
                } else {
                  // For PDF we also use HTML as the source
                  documentCopy.content.html_wrapper = JSON.stringify({
                    html: exportAsHTML(documentCopy),
                    title: documentCopy.title
                  });
                }

                // Call the backend API to generate the document
                console.log('Calling export_document API with:', {
                  document_id: document.id,
                  format: format
                });

                const response = await brain.export_document({
                  document_id: documentCopy.id,
                  document_data: documentCopy,
                  format: format
                });

                // Get the export task ID from the response
                const result = await response.json();
                console.log('Export API response:', result);

                const export_id = result.export_id;

                if (!export_id) {
                  console.error('No export_id returned from API');
                  throw new Error(`Failed to get export_id from response`);
                }

                // Check for any status that indicates the export is in progress or pending
                if (result.status === 'queued' || result.status === 'processing' || result.status === 'pending') {
                  // Poll for completion
                  console.log(`Polling export status for export_id: ${export_id}`);
                  let exportStatus = await pollExportStatus(export_id);
                  console.log('Final export status:', exportStatus);

                  if (exportStatus.status === 'completed' && exportStatus.download_url) {
                    // Download the file
                    const downloadUrl = exportStatus.download_url;
                    console.log(`Export successful, downloading with URL: ${downloadUrl}`);

                    // Make the URL an absolute URL if it's a relative path
                    // The API returns a path like "/api/export/document/{export_id}/download"
                    // or "/api/export/download/{storage_key}"
                    // But we need to construct the full URL with the API_URL and ensure paths are correct
                    let absoluteDownloadUrl: string;

                    // Log more debugging information
                    console.log('Download URL parts analysis:');
                    console.log('- API_URL:', API_URL);
                    console.log('- Download path from API:', downloadUrl);

                    // If it's already an absolute URL, use it as is
                    if (downloadUrl.startsWith('http')) {
                      absoluteDownloadUrl = downloadUrl;
                      console.log('Using provided absolute URL');
                    } else {
                      // First remove trailing slash from API_URL if present
                      const baseUrl = API_URL.replace(/\/+$/, '');

                      // Determine if using storage download or document download endpoint
                      const isStorageDownload = downloadUrl.includes('/export/download/');
                      console.log('Is storage download endpoint:', isStorageDownload);

                      // Extract just the path we need, removing any API prefix that might be duplicated
                      let cleanPath;
                      if (downloadUrl.startsWith('/api/')) {
                        cleanPath = downloadUrl.substring(4);
                      } else if (downloadUrl.startsWith('api/')) {
                        cleanPath = downloadUrl.substring(3); 
                      } else {
                        cleanPath = downloadUrl;
                      }

                      // Now construct the full URL ensuring we have exactly one '/' between parts
                      absoluteDownloadUrl = `${baseUrl}/${cleanPath.replace(/^\//, '')}`;  
                      console.log('Constructed URL from parts:', absoluteDownloadUrl);
                    }

                    console.log('Absolute download URL:', absoluteDownloadUrl);

                    try {
                      // Get the current user's auth token for authenticated requests
                      const { auth } = await import('app');
                      const authToken = await auth.getAuthToken();

                      console.log('Got auth token for download request:', authToken ? 'Token available' : 'No token');

                      // Set up headers with explicit Authorization header
                      const headers: HeadersInit = {};
                      if (authToken) {
                        headers['Authorization'] = `Bearer ${authToken}`;
                      }

                      console.log('Sending download request with auth headers');
                      const downloadResponse = await fetch(absoluteDownloadUrl, {
                        credentials: 'include', // Include cookies
                        headers: headers // Add explicit Authorization header
                      });

                      if (!downloadResponse.ok) {
                        console.error(`HTTP error when downloading ${format}:`, downloadResponse.status, downloadResponse.statusText);
                        console.error('Download URL used:', absoluteDownloadUrl);
                        try {
                          // Try to get response details
                          const errorText = await downloadResponse.text();
                          console.error('Error response text:', errorText.substring(0, 500));
                        } catch (e) {
                          console.error('Could not read error response text');
                        }
                        throw new Error(`HTTP error! status: ${downloadResponse.status}`);
                      }

                      const blob = await downloadResponse.blob();
                      const extension = format === 'docx' ? '.docx' : '.pdf';
                      const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + extension;

                      console.log(`Successfully downloaded ${format}, initiating browser download`);
                      downloadBlob(blob, filename);

                      // Cache the result (storing the download URL)
                      await updateExportCache(document.id, format, downloadUrl);
                      exportedFormats[format] = true;
                    } catch (downloadErr) {
                      console.error(`Error downloading ${format} file:`, downloadErr);
                      toast.error(`Failed to download ${format.toUpperCase()} file`);
                      throw downloadErr;
                    }
                  } else {
                    console.error(`Export failed with status: ${exportStatus.status}`);
                    // Enhanced error message with more details if available
                    const errorDetails = exportStatus.error || `status: ${exportStatus.status}`;
                    throw new Error(`Export failed: ${errorDetails}`);
                  }
                } else {
                  console.error(`Unexpected export status: ${result.status}`);
                  throw new Error(`Unexpected export status: ${result.status}`);
                }
              } catch (formatErr) {
                console.error(`Error generating ${format} format:`, formatErr);
                toast.error(`Failed to generate ${format.toUpperCase()} format`);
              }
            }
          }
        } catch (formatErr) {
          console.error(`Error processing format ${format}:`, formatErr);
          // Continue with other formats instead of failing completely
        }
      });

      // Wait for all exports to complete
      await Promise.all(exportPromises);

      // Summarize results
      const successCount = Object.values(exportedFormats).filter(Boolean).length;
      console.log(`Export completed with ${successCount} successful formats out of ${formats.length}`);

      if (successCount === formats.length) {
        toast.success(`All formats exported successfully`);
      } else if (successCount > 0) {
        toast.success(`Exported ${successCount} of ${formats.length} formats`);
      } else {
        toast.error('Failed to export any formats');
      }
    } catch (err) {
      console.error(`Error during export:`, err);
      toast.error('Failed to export document');
    } finally {
      setIsLoading(false);
      toast.dismiss();
    }
  };

  // Helper function to poll for export status
  const pollExportStatus = async (exportId: string) => {
    let attempts = 0;
    const maxAttempts = 30; // Max 30 attempts (15 seconds)

    while (attempts < maxAttempts) {
      attempts++;

      try {
        console.log(`Poll attempt ${attempts} for export_id: ${exportId}`);
        // Make sure exportId is defined and valid
        if (!exportId || exportId === 'undefined') {
          throw new Error('Export ID is undefined or empty');
        }

        // Sanitize exportId to ensure it's a valid UUID
        const sanitizedExportId = exportId.replace(/[^a-zA-Z0-9-]/g, '');
        if (sanitizedExportId !== exportId) {
          console.log(`Sanitized export ID from ${exportId} to ${sanitizedExportId}`);
        }

        const response = await brain.get_export_status({ exportId: sanitizedExportId });
        const statusData = await response.json();

        console.log(`Poll attempt ${attempts} response:`, statusData);

        if (statusData.status === 'completed') {
          console.log('Export completed successfully!', statusData);
          return statusData;
        } else if (statusData.status === 'failed') {
          console.error('Export failed with status:', statusData);
          const errorMessage = statusData.error ? 
            `Export failed: ${statusData.error.split('\n')[0]}` : 
            'Export failed: Unknown error';
          throw new Error(errorMessage);
        } else if (statusData.status === 'processing' || statusData.status === 'pending') {
          console.log(`Export still processing (attempt ${attempts})`);
        } else {
          console.warn(`Unexpected status: ${statusData.status}`);
        }
      } catch (error) {
        // Log the error with more detail
        console.error(`Error polling export status (attempt ${attempts}):`, error);
        console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');

        // If this is the last attempt, throw a user-friendly error
        if (attempts >= maxAttempts) {
          let errorMessage = 'Export process timed out.';
          if (error instanceof Error) {
            // Add the specific error message for debugging
            if (error.message.includes('fetch') || error.message.includes('network')) {
              errorMessage = 'Network error while checking export status. Please check your connection and try again.';
            } else if (error.message.includes('JSON')) {
              errorMessage = 'Error processing server response. Please try again later.';
            } else {
              errorMessage = `Export failed: ${error.message}`;
            }
          }
          throw new Error(errorMessage);
        }
      }

      // Wait 500ms before trying again
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    throw new Error('Export timed out after maximum polling attempts');
  };

  // Helper function to update the export cache in Firestore
  const updateExportCache = async (documentId: string, format: string, data: string) => {
    try {
      console.log('Updating export cache for document:', documentId, 'format:', format);
      // Ensure documentId is valid
      if (!documentId) {
        console.error('Invalid document ID for cache update');
        throw new Error('Invalid document ID for cache update');
      }

      // Sanitize storage key to only contain allowed characters (alphanumeric and ._-)
      // This is critical for compatibility with backend storage
      const sanitizedDocId = documentId.replace(/[^a-zA-Z0-9._-]/g, '');
      if (sanitizedDocId !== documentId) {
        console.log('Sanitized document ID from', documentId, 'to', sanitizedDocId);
      }

      // If data looks like a storage key, ensure it's also sanitized
      let sanitizedData = data;
      if (typeof data === 'string' && (data.startsWith('exports_') || data.startsWith('exports/'))) {
        sanitizedData = data.replace(/[^a-zA-Z0-9._-]/g, '');
        if (sanitizedData !== data) {
          console.log('Sanitized storage key data from', data, 'to', sanitizedData);
        }
      }

      // Get the document reference
      const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, documentId);

      // Get the content subcollection reference
      const contentQuery = query(collection(docRef, 'content'));
      const contentSnap = await getDocs(contentQuery);

      if (!contentSnap.empty) {
        const contentRef = contentSnap.docs[0].ref;

        // Get current export cache
        const currentContent = contentSnap.docs[0].data();
        let exportCache = Array.isArray(currentContent.exportCache) ? 
          currentContent.exportCache : [];

        console.log('Current export cache:', exportCache?.length || 0);

        // Find if this format already exists in the cache
        const formatIndex = exportCache.findIndex(
          (item: any) => item && item.format === format
        );

        // Prepare the new cache item
        const cacheItem = {
          format,
          data: sanitizedData,
          updatedAt: new Date()
        };

        // Update the cache array
        if (formatIndex >= 0) {
          console.log(`Updating existing cache for ${format} at index ${formatIndex}`);
          exportCache[formatIndex] = cacheItem;
        } else {
          console.log(`Adding new cache for ${format}`);
          exportCache.push(cacheItem);
        }

        console.log('Updated export cache size:', exportCache.length);

        // Update the doc
        try {
          await updateDoc(contentRef, {
            exportCache
          });
          console.log(`Export cache successfully updated for ${format}`);
        } catch (updateErr) {
          console.error('Error updating document with cache:', updateErr);
          throw new Error(`Failed to update export cache: ${updateErr instanceof Error ? updateErr.message : String(updateErr)}`);
        }
      } else {
        console.error('No content document found for document:', documentId);
        throw new Error('No content document found for document');
      }
    } catch (err) {
      console.error('Error updating export cache:', err);
      throw err; // Re-throw so calling code can catch and handle it
    }
  };

  // Helper function for component state
  const set = (state: Partial<{ isLoading: boolean }>) => {
    if (state.isLoading !== undefined) {
      setIsLoading(state.isLoading);
    }
  };

  // Calculate remaining quota percentage
  const quotaPercentage = (usedDocuments / maxDocuments) * 100;

  // Render document viewer or editor if a document is being viewed or edited
  if (viewingDocument) {
    return (
      <div className="container mx-auto p-6 bg-gradient-to-br from-transparent to-background/50 min-h-screen relative">
      {/* Navigation back to home */}
      <div className="mb-4">
        <Button 
          variant="ghost" 
          onClick={() => {
            // Navigate directly to home
            navigate('/');
          }} 
          className="font-mono tracking-tight -ml-2"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </div>
        <div className="mb-4 flex justify-between items-center">
          <Button variant="outline" onClick={() => {
            setViewingDocument(null);
            // Navigate directly to documents page
            navigate('/documents');
          }}>
            ← Back to Documents
          </Button>
          <Badge variant="outline" className="font-mono">
            VIEW MODE
          </Badge>
        </div>

        <Card className="mb-8 backdrop-blur-sm">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="font-mono text-2xl">{viewingDocument.title}</CardTitle>
                <CardDescription>{viewingDocument.description}</CardDescription>
              </div>
              <div className="flex space-x-2">
                <Badge variant="outline">{viewingDocument.language}</Badge>
                <Badge variant="outline">{(viewingDocument.codeSize / 1024).toFixed(1)} KB</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6">
              {/* Document content preview */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                    Description
                    {/* Angled decorative element */}
                    <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                  </h3>
                  <div className="bg-muted/30 p-6 backdrop-blur-sm rounded-md border border-muted relative overflow-hidden w-full">
                    {/* Decorative corner cut */}
                    <div className="absolute top-0 right-0 border-t-[24px] border-r-0 border-b-0 border-l-[24px] border-t-primary/20 border-l-transparent"></div>

                    {viewingDocument.content.generatedDocumentation ? (
                      (() => {
                        try {
                          // Parse the stored JSON string into an object
                          const docData = typeof viewingDocument.content.generatedDocumentation === 'string' ?
                            JSON.parse(viewingDocument.content.generatedDocumentation) :
                            viewingDocument.content.generatedDocumentation;

                          return (
                            <div className="font-mono text-sm leading-relaxed tracking-wide">
                              {docData.description || docData.textualDescription || 'No description available'}
                            </div>
                          );
                        } catch (err) {
                          console.error('Error parsing document data:', err);
                          return <p className="text-muted-foreground">Error parsing document data</p>;
                        }
                      })()
                    ) : (
                      <p className="text-muted-foreground">No documentation available</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                    Input & Output Parameters
                    {/* Angled decorative element */}
                    <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                  </h3>
                  <div className="bg-muted/20 backdrop-blur-sm p-6 rounded-md border border-muted relative overflow-hidden w-full before:absolute before:bottom-0 before:left-0 before:w-full before:h-px before:bg-gradient-to-r before:from-transparent before:via-primary/20 before:to-transparent">
                    {/* Decorative geometric accent */}
                    <div className="absolute -top-4 -right-4 w-16 h-16 bg-primary/5 rotate-45 transform origin-bottom-left"></div>
                    {viewingDocument.content.parameters || (viewingDocument.content.generatedDocumentation && JSON.parse(viewingDocument.content.generatedDocumentation).parameters) ? (
                      <div className="overflow-x-auto w-full">
                        <table className="w-full border-collapse table-fixed sm:table-auto min-w-[640px] max-w-full">
                          <thead>
                            <tr>
                              {/* Angled headers for visual interest */}
                              <th className="text-left p-3 font-mono tracking-wide text-xs uppercase relative overflow-hidden">
                                <div className="relative z-10">Name</div>
                                <div className="absolute inset-0 bg-primary/10 skew-x-[-5deg] transform origin-left z-0"></div>
                              </th>
                              <th className="text-left p-3 font-mono tracking-wide text-xs uppercase relative overflow-hidden">
                                <div className="relative z-10">Type</div>
                                <div className="absolute inset-0 bg-primary/10 skew-x-[-5deg] transform origin-left z-0"></div>
                              </th>
                              <th className="text-left p-3 font-mono tracking-wide text-xs uppercase relative overflow-hidden">
                                <div className="relative z-10">Description</div>
                                <div className="absolute inset-0 bg-primary/10 skew-x-[-5deg] transform origin-left z-0"></div>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {Array.isArray(viewingDocument.content.parameters) ? 
                              // Handle array format
                              viewingDocument.content.parameters.map((param: any, index: number) => (
                                <tr key={index} className={`border-b border-muted relative ${index % 2 === 0 ? "bg-muted/5" : ""}`}>
                                  <td className="p-3 font-medium font-mono">{param.name}</td>
                                  <td className="p-3">{param.type}</td>
                                  <td className="p-3">{param.description}</td>
                                </tr>
                              )) : 
                              // Handle object format
                              Object.entries(viewingDocument.content.parameters).map(([name, param]: [string, any], index: number) => (
                                <tr key={name} className={`border-b border-muted relative ${index % 2 === 0 ? "bg-muted/5" : ""}`}>
                                  <td className="p-3 font-medium font-mono">{name}</td>
                                  <td className="p-3">{param.type || 'Unknown'}</td>
                                  <td className="p-3">{param.description || 'No description'}</td>
                                </tr>
                              ))
                            }
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No parameters available</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                    Tools & Functions Used
                    {/* Angled decorative element */}
                    <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                  </h3>
                  <div className="bg-muted/20 backdrop-blur-sm p-6 rounded-md border border-muted relative overflow-hidden w-full before:absolute before:top-0 before:left-0 before:w-1 before:h-full before:bg-gradient-to-b before:from-primary/30 before:to-transparent">
                    {/* Decorative angular cut at bottom-left */}
                    <div className="absolute bottom-0 left-0 border-b-[24px] border-l-0 border-t-0 border-r-[24px] border-b-primary/10 border-r-transparent"></div>
                    {(viewingDocument.content.toolsUsed && viewingDocument.content.toolsUsed.length > 0) || (viewingDocument.content.generatedDocumentation && JSON.parse(viewingDocument.content.generatedDocumentation).toolsUsed?.length > 0) ? (
                      <div className="flex flex-wrap gap-3 pb-4">
                        {(viewingDocument.content.toolsUsed || 
                         (viewingDocument.content.generatedDocumentation && 
                          JSON.parse(viewingDocument.content.generatedDocumentation).toolsUsed)).map((tool: string, index: number) => (
                          <Badge 
                            key={index} 
                            className="px-4 py-2 bg-muted/50 hover:bg-primary/10 hover:text-primary transition-colors border border-muted font-mono text-xs tracking-wider transform hover:-skew-x-3 hover:scale-105 duration-150 relative overflow-hidden group"
                            variant="outline"
                          >
                            {/* Glassmorphic hover effect */}
                            <span className="absolute inset-0 w-full h-full bg-primary/0 backdrop-blur-[1px] opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                            {/* Function name with decorative underscore */}
                            <span className="relative z-10">{tool}</span>
                            <span className="absolute bottom-1.5 left-0 w-0 h-[2px] bg-primary group-hover:w-full transition-all duration-200"></span>
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No tools or functions listed</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Workflow section */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                  Workflow
                  {/* Angled decorative element */}
                  <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                </h3>
                <div className="p-4 bg-muted/30 rounded border border-muted overflow-auto">
                  {viewingDocument.content.workflow || (viewingDocument.content.generatedDocumentation && JSON.parse(viewingDocument.content.generatedDocumentation).workflow) ? (
                    <div className="relative py-4">
                      {((viewingDocument.content.workflow && viewingDocument.content.workflow.steps) || 
                       (viewingDocument.content.generatedDocumentation && 
                        JSON.parse(viewingDocument.content.generatedDocumentation).workflow?.steps)) && 
                       Array.isArray((viewingDocument.content.workflow && viewingDocument.content.workflow.steps) || 
                        (viewingDocument.content.generatedDocumentation && 
                         JSON.parse(viewingDocument.content.generatedDocumentation).workflow?.steps)) ? (
                        ((viewingDocument.content.workflow && viewingDocument.content.workflow.steps) || 
                         (viewingDocument.content.generatedDocumentation && 
                          JSON.parse(viewingDocument.content.generatedDocumentation).workflow?.steps)).map((step: any, index: number) => (
                          <div key={index} className="mb-16 relative">
                            {/* Connector line with animation */}
                            {index < ((viewingDocument.content.workflow && viewingDocument.content.workflow.steps) || 
                             (viewingDocument.content.generatedDocumentation && 
                              JSON.parse(viewingDocument.content.generatedDocumentation).workflow?.steps)).length - 1 && (
                              <div className="absolute left-6 top-12 bottom-0 w-1 z-0 overflow-hidden h-[calc(100%-16px)]">
                                <div className="absolute inset-0 bg-gradient-to-b from-primary/60 to-primary/20 animate-pulse"></div>
                              </div>
                            )}

                            {/* Step card with brutalist design */}
                            <div className="relative z-10 flex items-start">
                              {/* Hexagonal step indicator */}
                              <div className="flex-shrink-0 relative">
                                <div className="flex items-center justify-center w-14 h-14 bg-primary/10 backdrop-blur-md text-primary border-2 border-primary font-bold text-lg" style={{
                                  clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)'
                                }}>
                                  {step.id}
                                </div>
                              </div>

                              {/* Step content with glassmorphic card */}
                              <div className="ml-6 flex-grow">
                                <div className="bg-background/60 backdrop-blur-sm p-4 border border-muted rounded-md relative overflow-hidden">
                                  <h4 className="font-bold text-lg mb-1">{step.name}</h4>
                                  <p className="mb-3 text-muted-foreground">{step.description}</p>

                                  {/* Input/Output badges */}
                                  {step.input && (
                                    <div className="mb-2">
                                      <span className="text-xs font-semibold block mb-1">Input:</span>
                                      <div className="flex flex-wrap gap-2">
                                        {typeof step.input === 'string' ? (
                                          <Badge variant="outline" className="bg-blue-500/10 text-blue-600 border-blue-200">{step.input}</Badge>
                                        ) : Array.isArray(step.input) ? (
                                          step.input.map((input: string, idx: number) => (
                                            <Badge key={idx} variant="outline" className="bg-blue-500/10 text-blue-600 border-blue-200">{input}</Badge>
                                          ))
                                        ) : null}
                                      </div>
                                    </div>
                                  )}

                                  {step.output && (
                                    <div>
                                      <span className="text-xs font-semibold block mb-1">Output:</span>
                                      <div className="flex flex-wrap gap-2">
                                        {typeof step.output === 'string' ? (
                                          <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-200">{step.output}</Badge>
                                        ) : Array.isArray(step.output) ? (
                                          step.output.map((output: string, idx: number) => (
                                            <Badge key={idx} variant="outline" className="bg-green-500/10 text-green-600 border-green-200">{output}</Badge>
                                          ))
                                        ) : null}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-muted-foreground">No workflow steps available</p>
                      )}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No workflow information available</p>
                  )}
                </div>
              </div>

              {/* Metadata section */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                  Metadata
                  {/* Angled decorative element */}
                  <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                </h3>
                <div className="bg-muted/20 backdrop-blur-sm p-6 rounded-md border border-muted relative overflow-hidden w-full">
                  <div className="space-y-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-semibold">Author:</span>
                      <span className="px-2 py-1 rounded bg-primary/10 font-mono text-sm inline-block">{viewingDocument.author || "Not specified"}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-semibold">Version:</span>
                      <span className="px-2 py-1 rounded bg-primary/10 font-mono text-sm inline-block">{viewingDocument.version || "1.0.0"}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-semibold">Created at:</span>
                      <span className="px-2 py-1 rounded bg-primary/10 font-mono text-sm inline-block">
                        {viewingDocument.createdAt ? new Date(viewingDocument.createdAt.seconds * 1000).toLocaleString() : "Unknown"}
                      </span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-semibold">Last updated:</span>
                      <span className="px-2 py-1 rounded bg-primary/10 font-mono text-sm inline-block">
                        {viewingDocument.updatedAt ? new Date(viewingDocument.updatedAt.seconds * 1000).toLocaleString() : "Unknown"}
                      </span>
                    </div>
                    {viewingDocument.changeLog && viewingDocument.changeLog.length > 0 && (
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-semibold">Change Log:</span>
                        <ul className="list-disc pl-6 space-y-1">
                          {viewingDocument.changeLog.map((entry: any, index: number) => (
                            <li key={index} className="text-sm">
                              {entry.description} - {new Date(entry.timestamp.seconds * 1000).toLocaleString()}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Project Context Section */}
              {viewingDocument.project_context && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                    Project Context
                    <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                  </h3>
                  <div className="bg-muted/30 p-6 backdrop-blur-sm rounded-md border border-muted relative overflow-hidden w-full">
                    {/* Optional: Hexagonal grid pattern background for context section */}
                    {console.log('[VIEW MODE DEBUG] viewingDocument.project_context:', viewingDocument.project_context)} {/* LOGGING PROJECT CONTEXT */}
                    {/* <div className="absolute inset-0 opacity-5 pointer-events-none bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iY3VycmVudENvbG9yIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')]"></div> */}
                    <div className="font-mono text-sm leading-relaxed tracking-wide whitespace-pre-wrap p-4">
                      {viewingDocument.project_context}
                    </div>
                  </div>
                </div>
              )}

              {/* Code preview at the end */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                  Original Code
                  {/* Angled decorative element */}
                  <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                </h3>
                <div className="p-4 bg-muted/30 rounded border border-muted overflow-auto relative">
                  {/* Hexagonal grid pattern background for code sections */}
                  <div className="absolute inset-0 opacity-5 pointer-events-none bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iY3VycmVudENvbG9yIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')]"></div>

                  <pre className="font-mono text-sm whitespace-pre overflow-x-auto p-4 max-h-[500px] scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">{viewingDocument.content.originalCode}</pre>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between border-t border-muted/30 pt-4">
            <div className="text-sm text-muted-foreground">
              Last updated: {formatDate(viewingDocument.updatedAt)}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => {
                console.log('Clicked export button for document:', viewingDocument.id);
                // Open export dialog with current document
                setExportDocument(viewingDocument);

                // Log export attempt
                console.log('Opening export dialog for document in view mode:', {
                  id: viewingDocument.id,
                  title: viewingDocument.title,
                  hasContent: !!viewingDocument.content,
                  contentSize: viewingDocument.content ? JSON.stringify(viewingDocument.content).length : 0
                });

                // Open the export dialog
                setExportDialogOpen(true);
              }}>
                <Download size={16} className="mr-2" />
                Export Document
              </Button>
              <Button variant="outline" onClick={() => {
                // Navigate to editor
                console.log('Edit button clicked, navigating to editor with document ID:', viewingDocument.id);
                // Use direct window.location for reliable navigation
                const basePath = window.location.pathname.endsWith('/') ? 
                  window.location.pathname : 
                  window.location.pathname + '/';
                window.location.href = `./editor?id=${viewingDocument.id}`;
              }}>
                <Edit2 size={16} className="mr-2" />
                Edit Document
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render document editor if a document is being edited
  if (editingDocument) {
    return (
      <div className="container mx-auto p-6 bg-gradient-to-br from-transparent to-background/50 min-h-screen relative">
        <div className="mb-4 flex justify-between items-center">
          <Button variant="outline" onClick={() => {
            setEditingDocument(null);
            // Use direct window.location for reliable navigation
            const basePath = window.location.pathname.endsWith('/') ? 
              window.location.pathname : 
              window.location.pathname + '/';
            window.location.href = './documents';
          }}>
            ← Back to Documents
          </Button>
          <Badge variant="outline" className="font-mono">
            EDIT MODE
          </Badge>
        </div>

        <Card className="mb-8 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-mono text-2xl">Editing: {editingDocument.title}</CardTitle>
            <CardDescription>
              Document editing will be implemented in a future update. Currently in view-only mode.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-6 bg-muted/30 rounded border border-muted text-center">
              <p className="mb-4">The document editing functionality is currently under development.</p>
              <p>You can view the document content below.</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
              {/* Document content preview */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2 font-mono">Description</h3>
                  <div className="p-4 bg-muted/30 rounded border border-muted">
                    {editingDocument.content.generatedDocumentation ? (
                      <pre className="whitespace-pre-wrap font-mono text-sm">
                        {JSON.parse(editingDocument.content.generatedDocumentation).description}
                      </pre>
                    ) : (
                      <p className="text-muted-foreground">No documentation available</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Code preview */}
              <div>
                <h3 className="text-lg font-medium mb-4 font-mono tracking-wider border-l-4 border-primary pl-3 py-1 inline-block relative">
                  Original Code
                  {/* Angled decorative element */}
                  <div className="absolute -right-2 top-0 h-full w-6 bg-primary/10 -skew-x-12 -z-10"></div>
                </h3>
                <div className="p-4 bg-muted/30 rounded border border-muted overflow-auto">
                  <pre className="font-mono text-sm">{editingDocument.content.originalCode}</pre>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => {
              setEditingDocument(null);
              // Use direct window.location for reliable navigation
              const basePath = window.location.pathname.endsWith('/') ? 
                window.location.pathname : 
                window.location.pathname + '/';
              window.location.href = basePath;
            }}>
              Cancel
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 bg-gradient-to-br from-transparent to-background/50 min-h-screen relative">
      {/* Export Dialog */}
      {exportDialogOpen && exportDocument && (
        <ExportDialog
          document={exportDocument}
          isOpen={exportDialogOpen}
          onClose={() => {
            setExportDialogOpen(false);
            setExportDocument(null);
          }}
          onExport={handleExportDocument}
        />
      )}
      {/* Navigation back to home */}
      <div className="mb-4">
        <Button 
          variant="ghost" 
          onClick={() => {
            // Use React Router's navigate for reliable navigation
            navigate('/');
          }} 
          className="font-mono tracking-tight -ml-2 p-0 h-auto inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
            <path d="m15 18-6-6 6-6"/>
          </svg>
          Back to Home
        </Button>
      </div>
      {/* Glassmorphic background elements */}
      <div className="fixed top-20 right-20 w-96 h-96 rounded-full bg-primary/5 blur-3xl pointer-events-none opacity-30 -z-10" />
      <div className="fixed bottom-40 left-40 w-80 h-80 rounded-full bg-primary/10 blur-3xl pointer-events-none opacity-20 -z-10" />

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold font-mono tracking-wider relative">
            Your Documents
            <div className="absolute -bottom-1 left-0 w-1/2 h-[3px] bg-primary/30"></div>
          </h1>
          <p className="text-muted-foreground mt-1 pl-1 font-mono tracking-tight">
            Manage and access your documentation projects
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline"
            onClick={() => user && fixAllDocumentPermissions(user.uid)}
            className="font-mono tracking-tight"
            title="Fix document permissions"
          >
            <Lock size={16} className="mr-2" /> Fix Permissions
          </Button>
          <Button 
            onClick={handleCreateNew} 
            disabled={!canCreateNew}
            className="font-mono tracking-wide"
          >
            <Plus size={18} className="mr-2" /> New Document
          </Button>
        </div>
      </div>

      {/* Usage quota */}
      <Card className="mb-8 relative overflow-hidden backdrop-blur-sm after:absolute after:inset-0 after:border-2 after:border-primary/10 after:rounded-[inherit] after:pointer-events-none">
        <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-primary/5 transform rotate-12 z-0"></div>

        <CardHeader className="relative z-10 pb-3">
          <CardTitle className="font-mono tracking-wider flex items-center gap-2 relative inline-block">
            Storage Usage
            <div className="h-1 w-1 rounded-full bg-primary mx-1"></div>
            <Badge variant={quotaPercentage > 80 ? "destructive" : "outline"} className="ml-2 text-xs font-mono">
              {Math.round(quotaPercentage)}%
            </Badge>
            <div className="absolute -bottom-1 left-0 w-1/3 h-[3px] bg-primary/30 transform -skew-x-12"></div>
          </CardTitle>
          <CardDescription>
            You're using <span className="font-medium">{usedDocuments}</span> of <span className="font-medium">{maxDocuments}</span> documents
          </CardDescription>
        </CardHeader>

        <CardContent className="relative z-10">
          {/* Brutalist progress bar with angled cuts */}
          <div className="w-full h-4 bg-muted/50 backdrop-blur-sm relative overflow-hidden border border-muted" 
               style={{
                 clipPath: 'polygon(0 0, 100% 0, 95% 100%, 5% 100%)'
               }}>
            <div 
              className={`h-full ${quotaPercentage > 80 ? 'bg-destructive/80' : 'bg-primary/80'}`}
              style={{ 
                width: `${quotaPercentage}%`,
                clipPath: 'polygon(0 0, 100% 0, 100% 100%, 0 100%)'
              }}
            ></div>
          </div>

          {/* Document breakdown */}
          <div className="mt-4 flex flex-col space-y-2 font-mono text-sm">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 mr-2" style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)', background: 'rgba(var(--primary-rgb), 0.2)' }}></div>
                <span>FREE TIER</span>
              </div>
              <div className="bg-muted/30 px-2 py-0.5 rounded-sm">{PLAN_FEATURES[AccountPlan.FREE].maxDocuments} DOCS</div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 mr-2" style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)', background: 'rgba(var(--primary-rgb), 0.4)' }}></div>
                <span>PRO TIER</span>
              </div>
              <div className="bg-muted/30 px-2 py-0.5 rounded-sm">{PLAN_FEATURES[AccountPlan.PRO].maxDocuments} DOCS</div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 mr-2" style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)', background: 'rgba(var(--primary-rgb), 0.6)' }}></div>
                <span>ENTERPRISE TIER</span>
              </div>
              <div className="bg-muted/30 px-2 py-0.5 rounded-sm">{PLAN_FEATURES[AccountPlan.ENTERPRISE].maxDocuments} DOCS</div>
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between items-center relative z-10 pt-3 border-t border-muted/30">
          <p className="text-sm text-muted-foreground font-mono tracking-tight">
            {userPlan !== AccountPlan.ENTERPRISE && (
              <>
                Need more storage? <Button 
                  variant="link" 
                  className="text-primary p-0 h-auto font-normal hover:underline relative inline-block group"
                  onClick={async () => {
                    if (user?.uid) {
                      const { upgradeToEnterpriseTier } = useUserProfileStore.getState();
                      const success = await upgradeToEnterpriseTier(user.uid);
                      if (success) {
                        toast.success('Upgraded to Enterprise tier successfully!');
                      } else {
                        toast.error('Failed to upgrade to Enterprise tier');
                      }
                    } else {
                      toast.error('You must be logged in to upgrade');
                    }
                  }}
                >
                  Upgrade your plan
                  <span className="absolute bottom-0 left-0 w-full h-[1px] bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                </Button>
              </>
            )}
          </p>
          <Badge 
            variant="outline" 
            className="font-mono tracking-wider px-3 py-1 bg-muted/30 relative overflow-hidden"
            style={{
              clipPath: userPlan === AccountPlan.FREE ? 'polygon(0 0, 100% 0, 95% 100%, 5% 100%)' : 
                      userPlan === AccountPlan.PRO ? 'polygon(5% 0, 95% 0, 100% 100%, 0% 100%)' : 
                      'polygon(0 0, 100% 0, 95% 100%, 5% 100%)'
            }}
          >
            <span className="relative z-10">{userPlan} PLAN</span>
            <div className="absolute inset-0 bg-primary/5"></div>
          </Badge>
        </CardFooter>
      </Card>

      {/* Search and filters */}
      <div className="mb-6 flex gap-4 items-center">
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
          <Input
            type="text"
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {activeTag && (
          <Badge 
            variant="secondary"
            className="cursor-pointer"
            onClick={() => setActiveTag(null)}
          >
            {activeTag} ✕
          </Badge>
        )}
      </div>

      {/* Tag filters */}
      {allTags.length > 0 && (
        <div className="mb-6 overflow-x-auto whitespace-nowrap pb-2">
          <div className="inline-flex space-x-2">
            {allTags.map((tag) => (
              <Badge 
                key={tag}
                variant={activeTag === tag ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setActiveTag(activeTag === tag ? null : tag)}
              >
                <Tag size={14} className="mr-1.5 inline-block" />
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Documents grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map(i => (
            <Card key={i} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="h-6 bg-muted rounded animate-pulse mb-2 w-3/4"></div>
                <div className="h-4 bg-muted rounded animate-pulse w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded animate-pulse mb-2"></div>
                <div className="h-4 bg-muted rounded animate-pulse mb-2 w-5/6"></div>
                <div className="h-4 bg-muted rounded animate-pulse w-4/6"></div>
              </CardContent>
              <CardFooter>
                <div className="h-4 bg-muted rounded animate-pulse w-1/3"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Card className="p-6 bg-destructive/10 border-destructive/50 backdrop-blur-sm">
          <div className="flex flex-col items-center text-center py-4">
            <div style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }} 
                className="w-16 h-16 bg-destructive/10 mb-6 flex items-center justify-center">
              <span className="text-2xl">!</span>
            </div>
            <h3 className="font-mono tracking-tight text-xl mb-2">Error Loading Documents</h3>
            <p className="text-muted-foreground mb-6">{error.message}</p>
            <Button 
              variant="outline" 
              className="font-mono tracking-wide" 
              onClick={() => user?.uid && fetchUserDocuments(user.uid)}
            >
              Try Again
            </Button>
          </div>
        </Card>
      ) : filteredDocs.length === 0 ? (
        <div className="flex flex-col items-center justify-center min-h-[300px] text-center bg-muted/20 backdrop-blur-sm rounded-md border border-muted p-8 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary/5 transform -translate-x-1/2 -translate-y-1/2 rotate-45"></div>
          <div className="absolute bottom-0 left-0 w-40 h-40 bg-primary/5 transform translate-x-1/2 translate-y-1/2 rotate-45"></div>

          <div style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }} 
               className="w-24 h-24 bg-primary/10 mb-6 flex items-center justify-center relative">
            <FolderPlus size={40} className="text-primary/50" />
          </div>

          <h2 className="text-xl font-bold mb-2 font-mono tracking-wider">{searchTerm || activeTag ? 'No matching documents' : 'No documents yet'}</h2>

          <p className="text-muted-foreground max-w-md mb-6">
            {searchTerm || activeTag ? 
              `Try adjusting your search or filters to find what you're looking for.` : 
              `Get started by creating your first document. Upload your code and generate comprehensive documentation in seconds.`
            }
          </p>

          {!(searchTerm || activeTag) && (
            <Button 
              onClick={handleCreateNew}
              className="font-mono tracking-wide"
              disabled={!canCreateNew}
            >
              <Plus size={18} className="mr-2" /> Create New Document
            </Button>
          )}

          {(searchTerm || activeTag) && (
            <Button 
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setActiveTag(null);
              }}
              className="font-mono tracking-wide"
            >
              <Search size={18} className="mr-2" /> Reset Filters
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDocs.map((document) => (
            <Card 
              key={document.id} 
              className={`group hover:shadow-md transition-all duration-300 bg-background/80 backdrop-blur-sm relative overflow-hidden after:absolute after:inset-0 after:border-2 after:border-primary/5 after:rounded-[inherit] after:pointer-events-none ${deleteTargetId === document.id ? 'scale-95 opacity-50' : 'scale-100 opacity-100'}`}
            >
              {/* Brutalist geometric accent */}
              <div className="absolute top-0 right-0 w-16 h-16 bg-primary/5 transform -translate-x-1/2 -translate-y-1/2 rotate-45 z-0"></div>
              <div className="absolute bottom-0 left-0 w-12 h-12 bg-primary/5 transform translate-x-1/2 translate-y-1/2 rotate-45 z-0"></div>

              {/* Hexagonal GIS accent elements */}
              <div className="absolute top-5 left-5 h-6 w-6 opacity-20" 
                   style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)', background: 'rgba(var(--primary-rgb), 0.2)' }}></div>
              <div className="absolute bottom-5 right-5 h-6 w-6 opacity-20" 
                   style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)', background: 'rgba(var(--primary-rgb), 0.2)' }}></div>

              <CardHeader className="relative z-10">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle 
                      className="font-mono tracking-wide hover:text-primary transition-colors cursor-pointer relative inline-block"
                      onClick={() => navigate(`/documents?view=true&id=${document.id}`)}
                    >
                      {document.title}
                      <span className="absolute bottom-0 left-0 w-full h-[2px] bg-primary/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {document.description || "No description provided"}
                    </CardDescription>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="1"/>
                          <circle cx="12" cy="5" r="1"/>
                          <circle cx="12" cy="19" r="1"/>
                        </svg>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => {
                        // Navigate directly to the editor with the document ID
                        toast.success(`Opening document for editing...`);
                        // Use direct window.location for reliable navigation
                        const basePath = window.location.pathname.endsWith('/') ? 
                          window.location.pathname : 
                          window.location.pathname + '/';
                        window.location.href = `./editor?id=${document.id}`;
                      }}>
                        <Edit2 size={16} className="mr-2" />
                        <span>Edit Document</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Direct navigation to view mode - simpler approach to avoid potential confusion
                        toast.success(`Loading document for viewing...`);
                        // Use direct window.location for reliable navigation
                        const basePath = window.location.pathname.endsWith('/') ? 
                          window.location.pathname : 
                          window.location.pathname + '/';
                        // Navigate to the documents page with view=true parameter instead
                        window.location.href = `./documents?view=true&id=${document.id}`;
                      }}>
                        <Eye size={16} className="mr-2" />
                        <span>View Document</span>
                      </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleExportDialog(document.id)}>
                        <Download size={16} className="mr-2" />
                        <span>Export Document</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Delete dropdown clicked for document:', document.id);
                          handleDeleteDocument(document.id);
                        }}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 size={16} className="mr-2" />
                        <span>Delete Document</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent className="relative z-10">
                {/* Document metadata with brutalist aesthetic */}
                <div className="flex flex-col space-y-3 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <FileCode size={16} className="mr-2 text-primary/70" />
                    <span>{document.language}</span>
                    <span className="mx-2 text-muted">·</span>
                    <span>{(document.codeSize / 1024).toFixed(1)} KB</span>
                  </div>

                  {/* Tag pills with brutalist design */}
                  {document.tags && document.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 pt-2">
                      {document.tags.map((tag, i) => (
                        <Badge 
                          key={i} 
                          variant="outline" 
                          className="px-2 py-0.5 text-xs bg-muted/40 hover:bg-primary/10 hover:text-primary transition-colors cursor-pointer transform hover:-skew-x-3 hover:scale-105 duration-150"
                          onClick={(e) => {
                            e.stopPropagation();
                            setActiveTag(tag);
                          }}
                        >
                          <div className="relative">
                            <span>{tag}</span>
                            <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-primary group-hover:w-full transition-all duration-200"></span>
                          </div>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>

              <CardFooter className="flex justify-between items-center pt-2 pb-3 relative z-10 text-xs text-muted-foreground">
                <div className="flex items-center bg-muted/30 px-2 py-1 rounded-sm backdrop-blur-sm">
                  <Calendar size={14} className="mr-1.5" />
                  {formatDate(document.updatedAt)}
                </div>

                {document.isPublic ? (
                  <div className="flex items-center bg-primary/10 px-2 py-1 rounded-sm backdrop-blur-sm">
                    <Unlock size={14} className="mr-1.5" />
                    <span className="font-mono">PUBLIC</span>
                  </div>
                ) : (
                  <div className="flex items-center bg-muted/40 px-2 py-1 rounded-sm backdrop-blur-sm">
                    <Lock size={14} className="mr-1.5" />
                    <span className="font-mono">PRIVATE</span>
                  </div>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}


