/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Hauptseite für Dokumentenverwaltung - Refactored für bessere Wartbarkeit
 */

import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

// App Context und Stores
import { useUserGuardContext } from "app";
import { useDocumentStore, DocumentMeta } from "utils/documentStore";
import { AccountPlan } from "utils/firebaseConfig";
import { useUserProfileStore } from "utils/userProfileStore";

// Custom Hooks (Refactored Logic)
import { useDocumentLoader } from "hooks/useDocumentLoader";
import { useDocumentManager } from "hooks/useDocumentManager";
import { useDocumentExporter } from "hooks/useDocumentExporter";
import { useDocumentFilter } from "hooks/useDocumentFilter";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "components/Badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { ExportDialog } from "components/ExportDialog";
import { toast } from "sonner";
import { FileCode, Edit2, Trash2, Search, Plus, FolderPlus, Tag, Calendar, FileText, Eye, Download, Lock, Unlock, ArrowLeft } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuSubContent } from "@/components/ui/dropdown-menu";

export default function Documents() {
  const navigate = useNavigate();
  const { user } = useUserGuardContext();
  const { profile } = useUserProfileStore();
  const { documents, fetchUserDocuments, error: storeError } = useDocumentStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // URL-Parameter extrahieren
  const params = new URLSearchParams(window.location.search);
  const docId = params.get('id');
  const viewMode = params.get('view') === 'true';
  const editMode = params.get('edit') === 'true';

  // Custom Hooks für refactorierte Logik
  const documentLoader = useDocumentLoader({
    user,
    docId: docId || undefined,
    viewMode,
    editMode
  });

  const documentFilter = useDocumentFilter({
    documents: documents || []
  });

  const documentManager = useDocumentManager({
    user,
    userPlan: profile?.accountPlan || AccountPlan.FREE,
    documentsCount: documents?.length || 0,
    onDocumentDeleted: (docId: string) => {
      // Lokalen Zustand aktualisieren
    },
    onRefreshDocuments: () => fetchUserDocuments(user?.uid || '')
  });

  const documentExporter = useDocumentExporter({
    user
  });

  // Dokumente beim Mount laden
  useEffect(() => {
    if (user?.uid) {
      setIsLoading(true);
      fetchUserDocuments(user.uid)
        .finally(() => setIsLoading(false));
    }
  }, [user?.uid, fetchUserDocuments]);

  // Fehler vom Store setzen
  useEffect(() => {
    if (storeError) {
      setError(storeError);
    }
  }, [storeError]);

  // Wenn ein Dokument angezeigt wird, zeige die Dokumentenansicht
  if (documentLoader.viewingDocument) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-4">
          <Button 
            variant="outline" 
            onClick={() => documentLoader.setViewingDocument(null)}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Zurück zur Dokumentenliste
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>{documentLoader.viewingDocument.title}</CardTitle>
            <CardDescription>{documentLoader.viewingDocument.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Original Code:</h3>
                <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                  {documentLoader.viewingDocument.content.originalCode}
                </pre>
              </div>
              
              {documentLoader.viewingDocument.content.generatedDocumentation && (
                <div>
                  <h3 className="font-semibold mb-2">Generierte Dokumentation:</h3>
                  <div className="prose max-w-none">
                    {documentLoader.viewingDocument.content.generatedDocumentation}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Hauptansicht der Dokumentenliste
  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Meine Dokumente</h1>
          <p className="text-gray-600 mt-2">
            {documents?.length || 0} Dokumente • Plan: {profile?.accountPlan || AccountPlan.FREE}
          </p>
        </div>
        
        <Button 
          onClick={documentManager.handleCreateNew}
          disabled={!documentManager.canCreateNew}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Neues Dokument
        </Button>
      </div>

      {/* Suchleiste und Filter */}
      <div className="flex gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Dokumente durchsuchen..."
            value={documentFilter.searchTerm}
            onChange={(e) => documentFilter.setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {documentFilter.allTags.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Tag className="w-4 h-4" />
                Tags {documentFilter.activeTag && `(${documentFilter.activeTag})`}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => documentFilter.setActiveTag(null)}>
                Alle Tags
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {documentFilter.allTags.map(tag => (
                <DropdownMenuItem 
                  key={tag} 
                  onClick={() => documentFilter.setActiveTag(tag)}
                >
                  {tag}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        
        {documentFilter.hasActiveFilters && (
          <Button 
            variant="ghost" 
            onClick={documentFilter.clearFilters}
            className="text-gray-500"
          >
            Filter löschen
          </Button>
        )}
      </div>

      {/* Dokumentenliste */}
      {isLoading ? (
        <div className="text-center py-8">
          <p>Dokumente werden geladen...</p>
        </div>
      ) : documentFilter.filteredDocs.length === 0 ? (
        <div className="text-center py-8">
          <FileCode className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Keine Dokumente gefunden</h3>
          <p className="text-gray-600 mb-4">
            {documentFilter.hasActiveFilters 
              ? "Keine Dokumente entsprechen Ihren Filterkriterien."
              : "Erstellen Sie Ihr erstes Dokument, um zu beginnen."
            }
          </p>
          {!documentFilter.hasActiveFilters && (
            <Button onClick={documentManager.handleCreateNew}>
              <Plus className="w-4 h-4 mr-2" />
              Erstes Dokument erstellen
            </Button>
          )}
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {documentFilter.filteredDocs.map((doc) => (
            <Card 
              key={doc.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                documentManager.deleteTargetId === doc.id ? 'opacity-50' : ''
              }`}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2">{doc.title}</CardTitle>
                    <CardDescription className="line-clamp-2 mt-1">
                      {doc.description || 'Keine Beschreibung'}
                    </CardDescription>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        •••
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => documentManager.handleSelectDocument(doc.id)}>
                        <Edit2 className="w-4 h-4 mr-2" />
                        Bearbeiten
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => documentLoader.loadDocument(doc.id, 'view')}>
                        <Eye className="w-4 h-4 mr-2" />
                        Anzeigen
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => documentExporter.handleExportDialog(doc.id)}>
                        <Download className="w-4 h-4 mr-2" />
                        Exportieren
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => documentManager.handleDeleteDocument(doc.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Löschen
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-3">
                  <Calendar className="w-4 h-4" />
                  {documentManager.formatDate(doc.updatedAt || doc.createdAt)}
                </div>
                
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-3">
                  <FileText className="w-4 h-4" />
                  {doc.language} • {doc.codeSize} Zeichen
                </div>
                
                {doc.tags && doc.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {doc.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {doc.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{doc.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
              
              <CardFooter className="pt-0">
                <div className="flex items-center gap-2 w-full">
                  {doc.isPublic ? (
                    <Unlock className="w-4 h-4 text-green-600" />
                  ) : (
                    <Lock className="w-4 h-4 text-gray-400" />
                  )}
                  <span className="text-xs text-gray-500 flex-1">
                    {doc.isPublic ? 'Öffentlich' : 'Privat'}
                  </span>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Export Dialog */}
      <ExportDialog
        open={documentExporter.exportDialogOpen}
        onOpenChange={documentExporter.setExportDialogOpen}
        document={documentExporter.exportDocument}
        onExport={documentExporter.handleExportDocument}
      />
    </div>
  );
}
