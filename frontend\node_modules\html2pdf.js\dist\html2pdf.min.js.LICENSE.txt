/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */

/*!
 * html2pdf.js v0.10.3
 * Copyright (c) 2025 <PERSON>
 * Released under the MIT License.
 */

/*! ../internals/a-function */

/*! ../internals/a-possible-prototype */

/*! ../internals/add-to-unscopables */

/*! ../internals/an-object */

/*! ../internals/array-for-each */

/*! ../internals/array-includes */

/*! ../internals/array-iteration */

/*! ../internals/array-method-has-species-support */

/*! ../internals/array-method-is-strict */

/*! ../internals/array-species-constructor */

/*! ../internals/array-species-create */

/*! ../internals/classof */

/*! ../internals/classof-raw */

/*! ../internals/copy-constructor-properties */

/*! ../internals/correct-prototype-getter */

/*! ../internals/create-html */

/*! ../internals/create-iterator-constructor */

/*! ../internals/create-non-enumerable-property */

/*! ../internals/create-property */

/*! ../internals/create-property-descriptor */

/*! ../internals/define-iterator */

/*! ../internals/define-well-known-symbol */

/*! ../internals/descriptors */

/*! ../internals/document-create-element */

/*! ../internals/dom-iterables */

/*! ../internals/engine-user-agent */

/*! ../internals/engine-v8-version */

/*! ../internals/enum-bug-keys */

/*! ../internals/export */

/*! ../internals/fails */

/*! ../internals/function-bind-context */

/*! ../internals/get-built-in */

/*! ../internals/global */

/*! ../internals/has */

/*! ../internals/hidden-keys */

/*! ../internals/html */

/*! ../internals/ie8-dom-define */

/*! ../internals/indexed-object */

/*! ../internals/inherit-if-required */

/*! ../internals/inspect-source */

/*! ../internals/internal-state */

/*! ../internals/is-array */

/*! ../internals/is-forced */

/*! ../internals/is-object */

/*! ../internals/is-pure */

/*! ../internals/is-symbol */

/*! ../internals/iterators */

/*! ../internals/iterators-core */

/*! ../internals/native-symbol */

/*! ../internals/native-weak-map */

/*! ../internals/object-assign */

/*! ../internals/object-create */

/*! ../internals/object-define-properties */

/*! ../internals/object-define-property */

/*! ../internals/object-get-own-property-descriptor */

/*! ../internals/object-get-own-property-names */

/*! ../internals/object-get-own-property-names-external */

/*! ../internals/object-get-own-property-symbols */

/*! ../internals/object-get-prototype-of */

/*! ../internals/object-keys */

/*! ../internals/object-keys-internal */

/*! ../internals/object-property-is-enumerable */

/*! ../internals/object-set-prototype-of */

/*! ../internals/object-to-string */

/*! ../internals/ordinary-to-primitive */

/*! ../internals/own-keys */

/*! ../internals/path */

/*! ../internals/redefine */

/*! ../internals/regexp-flags */

/*! ../internals/require-object-coercible */

/*! ../internals/set-global */

/*! ../internals/set-to-string-tag */

/*! ../internals/shared */

/*! ../internals/shared-key */

/*! ../internals/shared-store */

/*! ../internals/string-html-forced */

/*! ../internals/string-multibyte */

/*! ../internals/string-trim */

/*! ../internals/to-absolute-index */

/*! ../internals/to-indexed-object */

/*! ../internals/to-integer */

/*! ../internals/to-length */

/*! ../internals/to-object */

/*! ../internals/to-primitive */

/*! ../internals/to-property-key */

/*! ../internals/to-string */

/*! ../internals/to-string-tag-support */

/*! ../internals/uid */

/*! ../internals/use-symbol-as-uid */

/*! ../internals/well-known-symbol */

/*! ../internals/well-known-symbol-wrapped */

/*! ../internals/whitespaces */

/*! ../modules/es.array.iterator */

/*! ../utils.js */

/*! ../worker.js */

/*! ./plugin/hyperlinks.js */

/*! ./plugin/jspdf-plugin.js */

/*! ./plugin/pagebreaks.js */

/*! ./utils.js */

/*! ./worker.js */

/*! core-js/modules/es.array.concat.js */

/*! core-js/modules/es.array.iterator.js */

/*! core-js/modules/es.array.join.js */

/*! core-js/modules/es.array.map.js */

/*! core-js/modules/es.array.slice.js */

/*! core-js/modules/es.function.name.js */

/*! core-js/modules/es.number.constructor.js */

/*! core-js/modules/es.object.assign.js */

/*! core-js/modules/es.object.keys.js */

/*! core-js/modules/es.object.to-string.js */

/*! core-js/modules/es.regexp.to-string.js */

/*! core-js/modules/es.string.iterator.js */

/*! core-js/modules/es.string.link.js */

/*! core-js/modules/es.symbol.description.js */

/*! core-js/modules/es.symbol.iterator.js */

/*! core-js/modules/es.symbol.js */

/*! core-js/modules/web.dom-collections.for-each.js */

/*! core-js/modules/web.dom-collections.iterator.js */

/*! es6-promise */

/*! html2canvas */

/*! jspdf */

/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/

/*!**********************!*\
  !*** ./src/utils.js ***!
  \**********************/

/*!***********************!*\
  !*** ./src/worker.js ***!
  \***********************/

/*!************************!*\
  !*** external "jspdf" ***!
  \************************/

/*!******************************!*\
  !*** external "html2canvas" ***!
  \******************************/

/*!**********************************!*\
  !*** ./src/plugin/hyperlinks.js ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/plugin/pagebreaks.js ***!
  \**********************************/

/*!************************************!*\
  !*** ./src/plugin/jspdf-plugin.js ***!
  \************************************/

/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/has.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/uid.js ***!
  \***********************************************/

/*!************************************************!*\
  !*** ./node_modules/core-js/internals/html.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/core-js/internals/path.js ***!
  \************************************************/

/*!*************************************************!*\
  !*** ./node_modules/core-js/internals/fails.js ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/export.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/global.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/shared.js ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/classof.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/is-pure.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.js ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/is-array.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/own-keys.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/redefine.js ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/an-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-forced.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-symbol.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/iterators.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-length.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-string.js ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/a-function.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/set-global.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/shared-key.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/to-integer.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.map.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/es6-promise/dist/es6-promise.js ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/classof-raw.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/create-html.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/descriptors.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/hidden-keys.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/string-trim.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/whitespaces.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.join.js ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/get-built-in.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/regexp-flags.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/shared-store.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/to-primitive.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.slice.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.keys.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.link.js ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/dom-iterables.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/enum-bug-keys.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/native-symbol.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-assign.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-create.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.concat.js ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-for-each.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-includes.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/ie8-dom-define.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/indexed-object.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/inspect-source.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/internal-state.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/iterators-core.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.function.name.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.assign.js ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/array-iteration.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/create-property.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/define-iterator.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/native-weak-map.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/to-property-key.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.iterator.js ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/object-to-string.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/string-multibyte.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.iterator.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.iterator.js ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-user-agent.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-v8-version.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/set-to-string-tag.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-absolute-index.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-indexed-object.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/use-symbol-as-uid.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.to-string.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.regexp.to-string.js ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/add-to-unscopables.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/string-html-forced.js ***!
  \**************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/internals/inherit-if-required.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.number.constructor.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.description.js ***!
  \***************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/a-possible-prototype.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-create.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys-internal.js ***!
  \****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/function-bind-context.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/ordinary-to-primitive.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/to-string-tag-support.js ***!
  \*****************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-is-strict.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-property.js ***!
  \******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/document-create-element.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-prototype-of.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-set-prototype-of.js ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/correct-prototype-getter.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/define-well-known-symbol.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-properties.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/require-object-coercible.js ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-constructor.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol-wrapped.js ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-property-descriptor.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.for-each.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.iterator.js ***!
  \**********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/copy-constructor-properties.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-iterator-constructor.js ***!
  \***********************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-property-is-enumerable.js ***!
  \*************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js/internals/create-non-enumerable-property.js ***!
  \**************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-symbols.js ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-has-species-support.js ***!
  \****************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-descriptor.js ***!
  \******************************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names-external.js ***!
  \**********************************************************************************/
