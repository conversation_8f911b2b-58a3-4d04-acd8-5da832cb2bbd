  <h2>Scale &amp; Basic Styles</h2>

  <div class="demo-preview-block demo-page--typography">
    <table>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Display 4</th>
        <td class="mdl-typography--display-4">Light 112px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Display 3</th>
        <td class="mdl-typography--display-3">Regular 56px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Display 2</th>
        <td class="mdl-typography--display-2">Regular 45px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Display 1</th>
        <td class="mdl-typography--display-1">Regular 34px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Headline</th>
        <td class="mdl-typography--headline">Regular 24px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Title</th>
        <td class="mdl-typography--title">Medium 20px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Subhead</th>
        <td class="mdl-typography--subhead">Regular 16px (Device), Regular 15px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Body 2</th>
        <td class="mdl-typography--body-2">Medium 14px (Device), Medium 13px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Body 1</th>
        <td class="mdl-typography--body-1">Regular 14px (Device), Regular 13px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Body 2 (force preferred font)</th>
        <td class="mdl-typography--body-2-force-preferred-font">Medium 14px (Device), Medium 13px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Body 1 (force preferred font)</th>
        <td class="mdl-typography--body-1-force-preferred-font">Regular 14px (Device), Regular 13px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Caption</th>
        <td class="mdl-typography--caption">Regular 12px</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Menu</th>
        <td class="mdl-typography--menu">Medium 14px (Device), Medium 13px (Desktop)</td>
      </tr>
      <tr>
        <th class="mdl-typography--caption-color-contrast">Button</th>
        <td class="mdl-typography--button">Medium (All Caps) 14px</td>
      </tr>
    </table>
  </div>

  <h2>HTML Elements</h2>

  <div class="demo-preview-block">

    <h3>Headings</h3>

    <h1>&lt;h1&gt;</h1>
    <h2>&lt;h2&gt;</h2>
    <h3>&lt;h3&gt;</h3>
    <h4>&lt;h4&gt;</h4>
    <h5>&lt;h5&gt;</h5>
    <h6>&lt;h6&gt;</h6>

    <h3>Formatting</h3>

    <p><u>&lt;u&gt;Underlined&lt;u&gt;</u></p>

    <p><b>&lt;b&gt;Bold&lt;b&gt;</b></p>

    <p><strong>&lt;strong&gt;Strong&lt;strong&gt;</strong></p>

    <p><i>&lt;italic&gt;Italic&lt;italic&gt;</i></p>

    <p><em>&lt;em&gt;Em&lt;em&gt;</em></p>

    <p><s>&lt;s&gt;Strikethrough&lt;s&gt;</s></p>

    <p><small>&lt;small&gt;Small&lt;small&gt;</small></p>

    <p><mark>&lt;mark&gt;Mark&lt;mark&gt;</mark></p>

    <h3>Body Text</h3>

    <p>&lt;p&gt;</p>

    <p class="mdl-typography--body-2">&lt;p class="mdl-typography-body-2"&gt;</p>

    <p class="mdl-typography--caption">&lt;p class="mdl-typography-caption"&gt;</p>

    <p class="mdl-typography--menu">&lt;p class="mdl-typography-menu"&gt;</p>

    <p class="mdl-typography--button">&lt;p class="mdl-typography-button"&gt;</p>

    <h3>Subtitles</h3>

    <h1>&lt;h1&gt; <small>Subtitle</small></h1>
    <h2>&lt;h2&gt; <small>Subtitle</small></h2>
    <h3>&lt;h3&gt; <small>Subtitle</small></h3>
    <h4>&lt;h4&gt; <small>Subtitle</small></h4>
    <h5>&lt;h5&gt; <small>Subtitle</small></h5>
    <h6>&lt;h6&gt; <small>Subtitle</small></h6>

    <h3>Description</h3>

    <dl>
      <dt>Description lists</dt>
      <dd>A description list is perfect for defining terms.</dd>
      <dt>Euismod</dt>
      <dd>Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit.</dd>
      <dd>Donec id elit non mi porta gravida at eget metus.</dd>
      <dt>Malesuada porta</dt>
      <dd>Etiam porta sem malesuada magna mollis euismod.</dd>
    </dl>
  </div>

  <h2>Quotes</h2>

  <div class="demo-preview-block">
    <blockquote>&lt;blockquote&gt;</blockquote>
  </div>

  <h2>Alignment</h2>

  <p class="mdl-typography--text-left">Left aligned text.</p>
  <p class="mdl-typography--text-center">Center aligned text.</p>
  <p class="mdl-typography--text-right">Right aligned text.</p>
  <p class="mdl-typography--text-justify">Justified text.</p>
  <p class="mdl-typography--text-nowrap">No wrap text.</p>

  <h2>Transformations</h2>
  <p class="mdl-typography--text-lowercase">Lowercased text.</p>
  <p class="mdl-typography--text-uppercase">Uppercased text.</p>
  <p class="mdl-typography--text-capitalize">Capitalized text.</p>

  <h2>Addresses</h2>

  <address>
    <strong>Googleplex</strong><br>
    1600 Amphitheatre Pkwy<br>
    Mountain View, CA 94043<br>
    <abbr title="Phone">P:</abbr> (*************
  </address>

  <h2>Code</h2>

  <h3>Multi-line code blocks</h3>
  <p>
    Use &lt;pre&gt; for multi-line code blocks.
    <pre>
&lt;p&gt;This is the first line of code&lt;/p&gt;
&lt;p&gt;This is the second line of code&lt;/p&gt;
  </pre>
  </p>

  <h3>Inline code blocks</h3>
  <p>Code blocks like <code>&lt;main&gt;</code> could be displayed inline.</p>


  <h2>Color Contrasts</h2>

  <div class="demo-preview-block">
    <div class="demo-typography--white">
      <p class="mdl-typography--caption-color-contrast">Caption</p>

      <p class="mdl-typography--body-2-color-contrast">Body</p>

      <p class="mdl-typography--subhead-color-contrast">Subhead</p>

      <p class="mdl-typography--title-color-contrast">Title</p>

      <p class="mdl-typography--display-1-color-contrast">Display</p>
    </div>
  </div>

  <div class="demo-preview-block">
    <div class="demo-typography--black">
        <p class="mdl-typography--caption-color-contrast">Caption</p>

        <p class="mdl-typography--body-2-color-contrast">Body</p>

        <p class="mdl-typography--subhead-color-contrast">Subhead</p>

        <p class="mdl-typography--title-color-contrast">Title</p>

        <p class="mdl-typography--display-1-color-contrast">Display</p>
    </div>
  </div>

  <div class="demo-preview-block">
    <div class="demo-typography--img-1">
        <p class="mdl-typography--caption-color-contrast">Caption</p>

        <p class="mdl-typography--body-2-color-contrast">Body</p>

        <p class="mdl-typography--subhead-color-contrast">Subhead</p>

        <p class="mdl-typography--title-color-contrast">Title</p>

        <p class="mdl-typography--display-1-color-contrast">Display</p>
    </div>
  </div>

  <div class="demo-preview-block">
    <div class="demo-typography--img-2">
      <p class="mdl-typography--caption-color-contrast">Caption</p>

      <p class="mdl-typography--body-2-color-contrast">Body</p>

      <p class="mdl-typography--subhead-color-contrast">Subhead</p>

      <p class="mdl-typography--title-color-contrast">Title</p>

      <p class="mdl-typography--display-1-color-contrast">Display</p>
    </div>
  </div>
