# Documentation models package
from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any

class DocumentationParameter(BaseModel):
    """Model for a parameter in the documentation"""
    name: str = ""
    type: str = "Unknown"
    description: str = ""
    required: bool = True
    constraints: str = ""

class DocumentationWorkflowStep(BaseModel):
    """Model for a workflow step in the documentation"""
    id: int
    name: str
    description: str

class DocumentationWorkflow(BaseModel):
    """Model for a workflow in the documentation"""
    steps: List[DocumentationWorkflowStep] = []

class DocumentationFunction(BaseModel):
    """Model for a function in the documentation"""
    name: str
    description: str = ""
    parameters: List[str] = []
    returns: str = ""

class DocumentationResponse(BaseModel):
    """Model for the documentation response"""
    textualDescription: str = ""
    parameters: List[DocumentationParameter] = []
    workflow: DocumentationWorkflow = Field(default_factory=DocumentationWorkflow)
    toolsUsed: List[str] = []
    originalCode: str = ""

# API request/response models
class DocumentationRequest(BaseModel):
    """Model for the documentation request"""
    code: str
    language: str = "Python"  # Programming language of the code
    provider: str = "auto"
    format: str = "markdown"
    documentationLanguage: str = "en"  # Language for the generated documentation (en, de, fr, it, es)
    documentation_id: Optional[str] = None # ID of the documentation entry for context fetching
    project_context: Optional[str] = None # Optional project context to guide documentation generation

class DocumentationGenerationResponse(BaseModel):
    """Model for the documentation generation response"""
    success: bool
    error_message: Optional[str] = None
    provider_used: Optional[str] = None
    language_used: Optional[str] = None  # Added field for language
    documentation: Optional[DocumentationResponse] = None

# Additional models for frontend compatibility
class DocumentationSection(BaseModel):
    """Model for a section in the documentation"""
    title: str
    content: str

class DocumentationData(BaseModel):
    """Model for the documentation data"""
    description: str = ""
    parameters: List[Dict[str, Any]] = []
    workflow: List[Dict[str, Any]] = []
    tools: List[str] = []
    code: str = ""

# Create empty router for compatibility
router = APIRouter()