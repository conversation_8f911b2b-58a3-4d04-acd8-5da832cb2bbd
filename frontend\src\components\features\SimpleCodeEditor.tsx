import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';

// Map of languages to their CSS classes for syntax highlighting
const LANGUAGE_CSS_MAP: Record<string, string> = {
  'Python': 'language-python',
  'JavaScript': 'language-javascript',
  'TypeScript': 'language-typescript',
  'Java': 'language-java',
  'C++': 'language-cpp',
  'C#': 'language-csharp',
  'R': 'language-r',
  'SQL': 'language-sql',
  'plaintext': 'language-plaintext'
};

// List of GIS-related keywords for enhanced syntax highlighting
const GIS_KEYWORDS = [
  // Common GIS libraries and modules
  'arcpy', 'arcgis', 'qgis', 'gdal', 'ogr', 'geopandas', 'shapely', 'fiona',
  'pyproj', 'rasterio', 'earthpy', 'leaflet', 'mapbox', 'openlayers',
  // Common GIS functions and concepts
  'spatial', 'buffer', 'intersect', 'projection', 'feature', 'geometry',
  'raster', 'vector', 'crs', 'epsg', 'wgs84', 'utm', 'overlay', 'clip',
  'dissolve', 'union', 'erase', 'merge', 'spatial.join', 'geotransform',
  // GIS data types
  'multipolygon', 'multipoint', 'multilinestring', 'featurelayer', 'pointcloud',
  'dem', 'tin', 'raster.dataset', 'feature.class', 'shapefile', 'geojson',
  'geopackage', 'gdb', 'geodatabase'
];

interface SimpleCodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  height?: string;
  placeholder?: string;
  highlightedLines?: number[];
}

// A simplified code editor with enhanced syntax highlighting for GIS code
export const SimpleCodeEditor = forwardRef<any, SimpleCodeEditorProps>(({
  value, onChange, language, height = '400px', placeholder = '', highlightedLines = []
},
  ref
) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [lineCount, setLineCount] = useState<number>(1);
  
  // Calculate line numbers whenever the value changes
  useEffect(() => {
    if (value) {
      const lines = value.split('\n').length;
      setLineCount(lines);
    } else {
      setLineCount(1);
    }
  }, [value]);
  
  // Expose methods to parent components
  useImperativeHandle(ref, () => ({
    editor: textareaRef.current,
    focus: () => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    },
    setCursor: (row: number, column: number) => {
      if (textareaRef.current) {
        const lines = value.split('\n');
        let position = 0;
        
        // Calculate position based on rows and columns
        for (let i = 0; i < row - 1 && i < lines.length; i++) {
          position += lines[i].length + 1; // +1 for newline character
        }
        position += Math.min(column, lines[row - 1]?.length || 0);
        
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(position, position);
      }
    },
    highlightLines: () => {
      // Simplified version just focuses the editor
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  }));
  
  // Handle tab key inside textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = e.currentTarget.selectionStart;
      const end = e.currentTarget.selectionEnd;
      
      // Insert tab at cursor position
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // Move cursor after the inserted tab
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;
        }
      }, 0);
    }
  };
  
  // Generate line numbers
  const lineNumbers = Array.from({ length: lineCount }, (_, i) => i + 1).join('\n');
  
  // Determine if we should use dark mode
  const [isDarkMode, setIsDarkMode] = useState(false);
  // Track if the code might be GIS-related
  const [isGisCode, setIsGisCode] = useState(false);
  
  // Check for GIS-related code by scanning for keywords
  useEffect(() => {
    if (value) {
      const lowerValue = value.toLowerCase();
      const containsGisKeywords = GIS_KEYWORDS.some(keyword => 
        lowerValue.includes(keyword.toLowerCase())
      );
      setIsGisCode(containsGisKeywords);
    } else {
      setIsGisCode(false);
    }
  }, [value]);
  
  useEffect(() => {
    // Check for system dark mode preference
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setIsDarkMode(darkModeQuery.matches);
    
    // Listen for changes
    const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
    darkModeQuery.addEventListener('change', handleChange);
    return () => darkModeQuery.removeEventListener('change', handleChange);
  }, []);
  
  // Define language-specific styling
  const languageClass = LANGUAGE_CSS_MAP[language] || 'language-plaintext';
  
  return (
    <div className="code-editor relative w-full border rounded-md overflow-hidden" style={{ height }}>
      {/* GIS-inspired hexagonal pattern background when GIS code is detected */}
      {isGisCode && (
        <div className="absolute inset-0 pointer-events-none opacity-5 z-0">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iY3VycmVudENvbG9yIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')]" />
        </div>
      )}
      
      {/* Glassmorphic accents */}
      <div className="absolute top-3 right-8 w-24 h-24 bg-primary/5 rounded-full blur-xl pointer-events-none opacity-20 z-0" />
      <div className="absolute bottom-5 left-8 w-16 h-16 bg-primary/10 blur-xl pointer-events-none opacity-10 z-0" />
      
      <div className="editor-container flex h-full relative z-10">
        {/* Line numbers with enhanced styling */}
        <div 
          className={`line-numbers py-2 px-2 text-right select-none border-r ${isDarkMode ? 'bg-neutral-800/90 text-neutral-400 border-neutral-700' : 'bg-neutral-100/90 text-neutral-500 border-neutral-200'}`}
          style={{ 
            width: '3.5rem', 
            overflowY: 'hidden',
            backdropFilter: 'blur(4px)'
          }}
        >
          {lineNumbers}
        </div>
        
        {/* Enhanced editor area with layered approach for better UX */}
        <div className="relative flex-1 overflow-auto">
          {/* Background with custom styling */}
          <div 
            className={`absolute inset-0 ${isDarkMode ? 'bg-neutral-900/80' : 'bg-white/80'}`}
            style={{ backdropFilter: 'blur(4px)' }}
          ></div>
          
          {/* Textarea for editing (actual input) */}
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className={`relative z-10 w-full h-full py-2 px-3 outline-none resize-none font-mono text-sm ${isDarkMode ? 'text-neutral-200' : 'text-neutral-900'}`}
            style={{ 
              lineHeight: '1.5',
              fontFamily: '"Fira Code", monospace',
              background: 'transparent',
              caretColor: isGisCode ? 'var(--primary)' : (isDarkMode ? '#e0e0e0' : '#333'),
            }}
            placeholder={placeholder}
            spellCheck="false"
            autoCapitalize="none"
            autoComplete="off"
            autoCorrect="off"
          />
        </div>
      </div>
      
      {/* Enhanced language indicator with GIS-inspired hexagonal shape */}
      <div className="absolute bottom-3 right-3 z-20">
        <div 
          className={`px-3 py-1.5 font-mono text-xs tracking-wider backdrop-blur-sm ${isGisCode ? 'bg-primary/20 text-primary' : (isDarkMode ? 'bg-neutral-800/80 text-neutral-300' : 'bg-muted/70 text-primary-foreground')}`}
          style={{ 
            clipPath: 'polygon(0% 0%, 100% 0%, 85% 50%, 100% 100%, 0% 100%, 15% 50%)', 
            boxShadow: '0 0 10px rgba(var(--primary-rgb), 0.1)'
          }}
        >
          {language.toUpperCase()}
          {isGisCode && <span className="ml-1 text-[0.65rem]">• GIS</span>}
        </div>
      </div>
      
      {/* Add highlighted lines markers */}
      {highlightedLines.length > 0 && (
        <div className="absolute top-0 left-3.5 right-0 bottom-0 pointer-events-none">
          {highlightedLines.map((lineNum) => (
            <div 
              key={lineNum} 
              className="absolute left-0 right-0 bg-yellow-500/10 border-l-2 border-yellow-500"
              style={{ 
                top: `calc(${lineNum - 1} * 1.5rem + 0.5rem)`,
                height: '1.5rem'
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
});

SimpleCodeEditor.displayName = 'SimpleCodeEditor';