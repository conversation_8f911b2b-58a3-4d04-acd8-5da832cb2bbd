var Q=Object.defineProperty;var M=(o,e,t)=>e in o?Q(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var l=(o,e,t)=>(M(o,typeof e!="symbol"?e+"":e,t),t);import{j as a,H as K,E as Y,o as I,c as w,s as g,p as X,i as Z,g as ee,a as z,b as te,G as re,d as oe,e as se,u as ne,f as ae,h as ie,k as le,l as ce,r as ue,m as c,n as de,N as fe,q as pe,t as he,v as x,w as $,x as me,y as ge,O as ye,R as be,W as T,z as Pe}from"./vendor-8eb8bd34.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const n of r)if(n.type==="childList")for(const i of n.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function t(r){const n={};return r.integrity&&(n.integrity=r.integrity),r.referrerPolicy&&(n.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?n.credentials="include":r.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(r){if(r.ep)return;r.ep=!0;const n=t(r);fetch(r.href,n)}})();const Ee=["https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap"],_e=()=>a.jsx(K,{children:Ee.map(o=>a.jsx("link",{href:o,rel:"stylesheet"},o))}),we=({children:o})=>a.jsx(Y,{fallback:null,onError:e=>{console.error("Caught error in AppWrapper",e.message,e.stack)},children:o}),xe="modulepreload",ve=function(o){return"/"+o},F={},m=function(e,t,s){if(!t||t.length===0)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map(n=>{if(n=ve(n),n in F)return;F[n]=!0;const i=n.endsWith(".css"),u=i?'[rel="stylesheet"]':"";if(!!s)for(let y=r.length-1;y>=0;y--){const p=r[y];if(p.href===n&&(!i||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${n}"]${u}`))return;const f=document.createElement("link");if(f.rel=i?"stylesheet":xe,i||(f.as="script",f.crossOrigin=""),f.href=n,document.head.appendChild(f),i)return new Promise((y,p)=>{f.addEventListener("load",y),f.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${n}`)))})})).then(()=>e()).catch(n=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=n,window.dispatchEvent(i),!i.defaultPrevented)throw n})};var Se=(o=>(o.DEV="development",o.PROD="production",o))(Se||{});const ht="production",mt="c94deb09-9445-402c-be40-329d6b202dc9",N="",gt="http://localhost:8000",yt="ws://localhost:8000",bt="/",Ae=I({signInOptions:I({google:w.boolean({description:"Enable Google sign-in"}),github:w.boolean({description:"Enable GitHub sign-in"}),facebook:w.boolean({description:"Enable Facebook sign-in"}),twitter:w.boolean({description:"Enable Twitter sign-in"}),emailAndPassword:w.boolean({description:"Enable email and password sign-in"}),magicLink:w.boolean({description:"Enable magic link sign-in"})}),siteName:g({description:"The name of the site"}),signInSuccessUrl:X(o=>o||"/",g({description:"The URL to redirect to after a successful sign-in"})),tosLink:g({description:"Link to the terms of service"}).optional(),privacyPolicyLink:g({description:"Link to the privacy policy"}).optional(),firebaseConfig:I({apiKey:g().default(""),authDomain:g().default(""),projectId:g().default(""),storageBucket:g().default(""),messagingSenderId:g().default(""),appId:g().default("")},{description:"Firebase config as as describe in https://firebase.google.com/docs/web/learn-more#config-object"})}),je=Ae.parse(JSON.parse('{"firebaseConfig":{"apiKey":"AIzaSyATpy8Z1sXczJYavH_Tbj_jXLd4YHz3HzI","authDomain":"codescribe-gis.firebaseapp.com","projectId":"codescribe-gis","storageBucket":"codescribe-gis.firebasestorage.app","messagingSenderId":"233634784812","appId":"1:233634784812:web:4381a748e379e6887f5389"},"signInOptions":{"google":true,"email":true,"facebook":false,"twitter":false,"emailAndPassword":false,"magicLink":false},"siteName":"CodeScribe GIS","signInSuccessUrl":"/","tosLink":"/terms-of-service","privacyPolicyLink":"/privacy-policy"}')),D=Z(je.firebaseConfig),A=ee(D),Le=z(D),Pt=Le,Et=te(D),Te=()=>{console.log("Firebase auth extension enabled")},Ce=async()=>{const o=new re;return o.addScope("https://www.googleapis.com/auth/userinfo.profile"),oe(A,o)},Re=async()=>se(A),Oe=()=>A.currentUser,De=async(o,e)=>ne(o,e),Ue=async(o,e)=>ae(o,e),Ie=async o=>ie(o),$e=async(o,e)=>le(o,e),ke=async o=>ce(A,o),qe=async(o,e)=>ue(o,e),W=async()=>{var o;return((o=A.currentUser)==null?void 0:o.getIdToken())??null},Fe=async()=>`Bearer ${await W()??""}`,Ne={getAuthHeaderValue:Fe,getAuthToken:W,getCurrentUser:Oe,reauthenticateUser:qe,sendEmailVerification:Ie,sendPasswordResetEmail:ke,signInWithGoogle:Ce,signOut:Re,updateCurrentUser:De,updateCurrentUserEmail:Ue,updateCurrentUserPassword:$e,validateConfig:Te},G=()=>{const[o,e]=c.useState(null),[t,s]=c.useState(!0);return c.useEffect(()=>{const r=A.onAuthStateChanged(n=>{e(n),s(!1)});return()=>{r()}},[]),{user:o,loading:t}},V=c.createContext(void 0),_t=()=>{const o=c.useContext(V);if(o===void 0)throw new Error("useUserGuardContext must be used within a <UserGuard>");return o},v=o=>{const{user:e,loading:t}=G(),{pathname:s}=de();if(t)return a.jsx(c.Fragment,{});if(!e){const r=new URLSearchParams(window.location.search);s!=="/logout"&&s!=="/sign-out"&&r.set("next",s);const n=r.toString();return a.jsx(fe,{to:`/login?${n}`,replace:!0})}return a.jsx(V.Provider,{value:{user:e},children:o.children})};var E=(o=>(o.Json="application/json",o.FormData="multipart/form-data",o.UrlEncoded="application/x-www-form-urlencoded",o.Text="text/plain",o))(E||{});class Be{constructor(e={}){l(this,"baseUrl","");l(this,"securityData",null);l(this,"securityWorker");l(this,"abortControllers",new Map);l(this,"customFetch",(...e)=>fetch(...e));l(this,"baseApiParams",{credentials:"same-origin",headers:{},redirect:"follow",referrerPolicy:"no-referrer"});l(this,"setSecurityData",e=>{this.securityData=e});l(this,"contentFormatters",{"application/json":e=>e!==null&&(typeof e=="object"||typeof e=="string")?JSON.stringify(e):e,"text/plain":e=>e!==null&&typeof e!="string"?JSON.stringify(e):e,"multipart/form-data":e=>Object.keys(e||{}).reduce((t,s)=>{const r=e[s];if(Array.isArray(r))for(const n of r)t.append(s,n instanceof Blob?n:typeof n=="object"&&n!==null?JSON.stringify(n):`${n}`);else t.append(s,r instanceof Blob?r:typeof r=="object"&&r!==null?JSON.stringify(r):`${r}`);return t},new FormData),"application/x-www-form-urlencoded":e=>this.toQueryString(e)});l(this,"createAbortSignal",e=>{if(this.abortControllers.has(e)){const s=this.abortControllers.get(e);return s?s.signal:void 0}const t=new AbortController;return this.abortControllers.set(e,t),t.signal});l(this,"abortRequest",e=>{const t=this.abortControllers.get(e);t&&(t.abort(),this.abortControllers.delete(e))});l(this,"request",async({body:e,secure:t,path:s,type:r,query:n,format:i,baseUrl:u,cancelToken:d,...f})=>{const y=(typeof t=="boolean"?t:this.baseApiParams.secure)&&this.securityWorker&&await this.securityWorker(this.securityData)||{},p=this.mergeRequestParams(f,y),j=n&&this.toQueryString(n),U=this.contentFormatters[r||"application/json"],b=i||p.format;return this.customFetch(`${u||this.baseUrl||""}${s}${j?`?${j}`:""}`,{...p,headers:{...p.headers||{},...r&&r!=="multipart/form-data"?{"Content-Type":r}:{}},signal:(d?this.createAbortSignal(d):p.signal)||null,body:typeof e>"u"||e===null?null:U(e)}).then(async P=>{const h=P;h.data=null,h.error=null;const L=b?await P[b]().then(_=>(h.ok?h.data=_:h.error=_,h)).catch(_=>(h.error=_,h)):h;if(d&&this.abortControllers.delete(d),!P.ok)throw L;return L})});Object.assign(this,e)}encodeQueryParam(e,t){return`${encodeURIComponent(e)}=${encodeURIComponent(typeof t=="number"?t:`${t}`)}`}addQueryParam(e,t){return this.encodeQueryParam(t,e[t])}addArrayQueryParam(e,t){return e[t].map(r=>this.encodeQueryParam(t,r)).join("&")}toQueryString(e){const t=e||{};return Object.keys(t).filter(r=>typeof t[r]<"u").map(r=>Array.isArray(t[r])?this.addArrayQueryParam(t,r):this.addQueryParam(t,r)).join("&")}addQueryParams(e){const t=this.toQueryString(e);return t?`?${t}`:""}mergeRequestParams(e,t){return{...this.baseApiParams,...e,...t||{},headers:{...this.baseApiParams.headers||{},...e.headers||{},...t&&t.headers||{}}}}async*requestStream({body:e,secure:t,path:s,type:r,query:n,format:i,baseUrl:u,cancelToken:d,...f}){const y=(typeof t=="boolean"?t:this.baseApiParams.secure)&&this.securityWorker&&await this.securityWorker(this.securityData)||{},p=this.mergeRequestParams(f,y),j=n&&this.toQueryString(n),U=this.contentFormatters[r||"application/json"];let b;try{if(b=await this.customFetch(`${u||this.baseUrl||""}${s}${j?`?${j}`:""}`,{...p,headers:{...p.headers||{},...r&&r!=="multipart/form-data"?{"Content-Type":r}:{}},signal:(d?this.createAbortSignal(d):p.signal)||null,body:typeof e>"u"||e===null?null:U(e)}),!b.ok)throw new Error("Response not OK");const P=b.body.getReader(),h=new TextDecoder,L=b.headers.get("Content-Type");for(;;){const{done:_,value:C}=await P.read();if(_)break;let dt=h.decode(C,{stream:!0}),R;if(L==="application/json"){let q=h.decode(C,{stream:!0});try{R=JSON.parse(q)}catch(J){throw new Error(J)}}else L==="application/octet-stream"?R=new Uint8Array(C):R=h.decode(C,{stream:!0});yield R}}catch(P){throw new Error(P)}finally{d&&this.abortControllers.delete(d)}}}class ze extends Be{constructor(){super(...arguments);l(this,"check_health",(t={})=>this.request({path:"/_healthz",method:"GET",...t}));l(this,"run_test_endpoint",(t={})=>this.request({path:"/routes/test",method:"GET",...t}));l(this,"testing_documentation_language",(t,s={})=>this.request({path:"/routes/test_language",method:"POST",body:t,type:E.Json,...s}));l(this,"validate_code_endpoint2",(t,s={})=>this.request({path:"/routes/test-documentation",method:"GET",query:t,...s}));l(this,"download_from_storage",({storageKey:t,...s},r={})=>this.request({path:`/routes/export/download/${t}`,method:"GET",...r}));l(this,"export_document",(t,s={})=>this.request({path:"/routes/export/document",method:"POST",body:t,type:E.Json,...s}));l(this,"get_export_status",({exportId:t,...s},r={})=>this.request({path:`/routes/export/document/${t}/status`,method:"GET",...r}));l(this,"download_document",({exportId:t,...s},r={})=>this.request({path:`/routes/export/document/${t}/download`,method:"GET",...r}));l(this,"delete_export",({exportId:t,...s},r={})=>this.request({path:`/routes/export/document/${t}`,method:"DELETE",...r}));l(this,"summarize_uploaded_files",(t,s={})=>this.request({path:"/routes/context-processor/summarize-uploaded-files",method:"POST",body:t,type:E.FormData,...s}));l(this,"llm_validate_code_syntax_endpoint",(t,s={})=>this.request({path:"/routes/llm_validate_code_syntax",method:"POST",body:t,type:E.Json,...s}));l(this,"generate_documentation",(t,s={})=>this.request({path:"/routes/documentation/generate",method:"POST",body:t,type:E.Json,...s}));l(this,"validate_code_endpoint",(t,s={})=>this.request({path:"/routes/validate",method:"POST",body:t,type:E.Json,...s}))}}const We=/localhost:\d{4}/i.test(window.location.origin),Ge=()=>We?`${window.location.origin}${N}`:`https://api.databutton.com${N}`,Ve=()=>({credentials:"include",secure:!0}),He=()=>{const o=Ge(),e=Ve();return new ze({baseUrl:o,baseApiParams:e,customFetch:(t,s)=>fetch(t,s),securityWorker:async()=>({headers:{Authorization:await Ne.getAuthHeaderValue()}})})},wt=He(),Je=c.lazy(()=>m(()=>import("./App-3a107145.js"),["assets/App-3a107145.js","assets/vendor-8eb8bd34.js","assets/index-fcfcda09.js","assets/card-b935221e.js","assets/index-ae1e7bbf.js","assets/dropdown-menu-752f7312.js","assets/index-89be2a26.js","assets/react-icons.esm-600f1a11.js","assets/createLucideIcon-6c39ff51.js"])),Qe=c.lazy(()=>m(()=>import("./Documents-d9635d4c.js"),["assets/Documents-d9635d4c.js","assets/vendor-8eb8bd34.js","assets/documentStore-4a8258c1.js","assets/documentPermissions-f0c02e2b.js","assets/createLucideIcon-6c39ff51.js","assets/index-fcfcda09.js","assets/card-b935221e.js","assets/label-ef207e6f.js","assets/index-89be2a26.js","assets/react-icons.esm-600f1a11.js","assets/index-a23265aa.js","assets/dropdown-menu-752f7312.js"])),Me=c.lazy(()=>m(()=>import("./Editor-4a0adc3c.js"),["assets/Editor-4a0adc3c.js","assets/vendor-8eb8bd34.js","assets/index-fcfcda09.js","assets/card-b935221e.js","assets/documentPermissions-f0c02e2b.js","assets/createLucideIcon-6c39ff51.js","assets/index-ae1e7bbf.js","assets/index-89be2a26.js","assets/index-ee3cbf23.js"])),B=c.lazy(()=>m(()=>import("./EditorSimplified-41d1f71e.js"),["assets/EditorSimplified-41d1f71e.js","assets/vendor-8eb8bd34.js"])),Ke=c.lazy(()=>m(()=>import("./Login-ad631f2b.js"),["assets/Login-ad631f2b.js","assets/vendor-8eb8bd34.js","assets/card-b935221e.js","assets/label-ef207e6f.js","assets/index-fcfcda09.js"])),Ye=c.lazy(()=>m(()=>import("./Logout-b7844489.js"),["assets/Logout-b7844489.js","assets/vendor-8eb8bd34.js","assets/card-b935221e.js"])),Xe=c.lazy(()=>m(()=>import("./Profile-f4c3fa26.js"),["assets/Profile-f4c3fa26.js","assets/vendor-8eb8bd34.js","assets/card-b935221e.js","assets/label-ef207e6f.js","assets/index-fcfcda09.js","assets/index-89be2a26.js","assets/react-icons.esm-600f1a11.js","assets/index-a23265aa.js","assets/index-ee3cbf23.js"])),Ze=c.lazy(()=>m(()=>import("./Register-8f86aec2.js"),["assets/Register-8f86aec2.js","assets/vendor-8eb8bd34.js","assets/card-b935221e.js","assets/label-ef207e6f.js","assets/index-fcfcda09.js"])),et=[{path:"/",element:a.jsx(Je,{})},{path:"/documents",element:a.jsx(v,{children:a.jsx(Qe,{})})},{path:"/editor",element:a.jsx(v,{children:a.jsx(Me,{})})},{path:"/editor-simplified",element:a.jsx(v,{children:a.jsx(B,{})})},{path:"/editorsimplified",element:a.jsx(v,{children:a.jsx(B,{})})},{path:"/login",element:a.jsx(Ke,{})},{path:"/logout",element:a.jsx(v,{children:a.jsx(Ye,{})})},{path:"/profile",element:a.jsx(v,{children:a.jsx(Xe,{})})},{path:"/register",element:a.jsx(Ze,{})}],xt={USERS:"users",USER_PROFILES:"userProfiles",DOCUMENTS:"documentations",PAYMENTS:"payments"};var O=(o=>(o.FREE="free",o.PRO="pro",o.ENTERPRISE="enterprise",o))(O||{});const vt={free:{maxDocuments:5,maxStorageBytes:5*1024*1024,allowAdvancedAnalytics:!1,allowBatchProcessing:!1,price:0},pro:{maxDocuments:50,maxStorageBytes:100*1024*1024,allowAdvancedAnalytics:!0,allowBatchProcessing:!0,price:9.99},enterprise:{maxDocuments:500,maxStorageBytes:1024*1024*1024,allowAdvancedAnalytics:!0,allowBatchProcessing:!0,allowCustomBranding:!0,allowTeamCollaboration:!0,price:49.99}},S=z(D),H=pe((o,e)=>({profile:null,isLoading:!0,error:null,subscribeToProfile:t=>{if(!t)return o({profile:null,isLoading:!1}),()=>{};o({isLoading:!0});try{return he(x(S,"userProfiles",t),r=>{r.exists()?o({profile:r.data(),isLoading:!1}):o({profile:null,isLoading:!1})},r=>{console.error("Error fetching user profile:",r),o({error:r,isLoading:!1,profile:null})})}catch(s){return console.error("Error setting up profile subscription:",s),o({error:s,isLoading:!1,profile:null}),()=>{}}},updateProfile:async t=>{const{profile:s}=e();if(!s){console.error("Cannot update profile: No profile loaded");return}try{const r={...s,...t,updatedAt:Date.now()};o({profile:r}),await $(x(S,"userProfiles",s.userId),r,{merge:!0})}catch(r){console.error("Error updating profile:",r),o({error:r})}},createProfileIfNotExists:async(t,s,r,n)=>{try{const i={userId:t,email:s===void 0?null:s,displayName:r===void 0?null:r,photoURL:n===void 0?null:n,createdAt:Date.now(),updatedAt:Date.now(),accountPlan:O.FREE,preferences:{theme:"system",emailNotifications:!1}};try{if(!(await me(x(S,"userProfiles",t))).exists()){const d={userId:t,email:s===void 0?null:s,displayName:r===void 0?null:r,photoURL:n===void 0?null:n,createdAt:Date.now(),updatedAt:Date.now()},f={...d,accountPlan:O.FREE,preferences:{theme:"system",language:"en",emailNotifications:!1}};await $(x(S,"userProfiles",t),f),o({profile:d,isLoading:!1})}}catch(u){console.warn("Error accessing Firestore, using fallback profile:",u),o({profile:i,isLoading:!1})}}catch(i){console.error("Error creating profile:",i)}},upgradeToEnterpriseTier:async t=>{try{if(!t)return console.error("Cannot upgrade profile: No user ID provided"),!1;const s=x(S,"userProfiles",t);return await $(s,{accountPlan:O.ENTERPRISE,updatedAt:Date.now()},{merge:!0}),console.log(`User ${t} upgraded to Enterprise tier`),!0}catch(s){return console.error("Error upgrading to Enterprise tier:",s),!1}},deleteUserProfile:async t=>{try{if(!t){console.error("Cannot delete profile: No user ID provided");return}const{deleteDoc:s}=await m(()=>import("./vendor-8eb8bd34.js").then(n=>n.a6),[]),r=x(S,"userProfiles",t);await s(r),console.log(`User profile ${t} deleted successfully`),o({profile:null,isLoading:!1})}catch(s){console.error("Error deleting user profile:",s)}}})),St=Object.freeze(Object.defineProperty({__proto__:null,useUserProfileStore:H},Symbol.toStringTag,{value:"Module"})),tt=({children:o})=>{const{user:e,loading:t}=G(),{subscribeToProfile:s,createProfileIfNotExists:r}=H();return c.useEffect(()=>{let n=()=>{};if(e&&!t)try{r(e.uid,e.email===void 0?null:e.email,e.displayName===void 0?null:e.displayName,e.photoURL===void 0?null:e.photoURL).catch(i=>{console.warn("Could not create profile. This may be due to Firestore permissions.",i)}),n=s(e.uid)}catch(i){console.warn("Error in UserProfileProvider:",i)}return()=>{n()}},[e,t,s,r]),a.jsx(a.Fragment,{children:o})},rt=({children:o})=>a.jsx(tt,{children:o}),k=({children:o})=>a.jsx(c.Suspense,{children:o}),ot=c.lazy(()=>m(()=>import("./NotFoundPage-35624358.js"),["assets/NotFoundPage-35624358.js","assets/vendor-8eb8bd34.js","assets/ProdErrorPage-2d98144e.js"])),st=c.lazy(()=>m(()=>import("./SomethingWentWrongPage-20214032.js"),["assets/SomethingWentWrongPage-20214032.js","assets/vendor-8eb8bd34.js","assets/ProdErrorPage-2d98144e.js"])),nt=ge([{element:a.jsx(rt,{children:a.jsx(k,{children:a.jsx(ye,{})})}),children:et},{path:"*",element:a.jsx(k,{children:a.jsx(ot,{})}),errorElement:a.jsx(k,{children:a.jsx(st,{})})}]),at={theme:"system",setTheme:()=>null},it=c.createContext(at);function lt({children:o,defaultTheme:e="system",storageKey:t="databutton-c94deb09-9445-402c-be40-329d6b202dc9-ui-theme",...s}){const[r,n]=c.useState(()=>localStorage.getItem(t)||e);c.useEffect(()=>{const u=window.document.documentElement;if(u.classList.remove("light","dark"),r==="system"){const d=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";u.classList.add(d);return}u.classList.add(r)},[r]);const i={theme:r,setTheme:u=>{localStorage.setItem(t,u),n(u)}};return a.jsx(it.Provider,{...s,value:i,children:o})}const ct="system",ut=()=>a.jsx(we,{children:a.jsxs(lt,{defaultTheme:ct,children:[a.jsx(be,{router:nt}),a.jsx(_e,{})]})});"use"in T||(T.use=function(e){if(typeof e=="object"&&e!==null&&typeof e.then=="function"){let t=e.status;if(t===void 0){let s=e;throw s.status="pending",s.then(r=>{s.status==="pending"&&(s.status="fulfilled",s.value=r)},r=>{s.status==="pending"&&(s.status="rejected",s.reason=r)}),s}else{if(t==="fulfilled")return e.value;throw t==="rejected"?e.reason:e}}else return typeof e=="object"&&e!==null&&typeof e._context<"u"?T.useContext(e._context||e):typeof e=="object"&&e!==null&&typeof e.$$typeof<"u"?T.useContext(e):T.useContext(e)});Pe(document.getElementById("root")).render(a.jsx(c.StrictMode,{children:a.jsx(ut,{})}));export{O as A,xt as C,Se as M,vt as P,tt as U,yt as W,m as _,_t as a,H as b,wt as c,A as d,Ne as e,D as f,je as g,gt as h,bt as i,mt as j,Le as k,Pt as l,ht as m,Et as n,v as o,St as p,G as u};
