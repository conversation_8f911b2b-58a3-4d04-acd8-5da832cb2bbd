import { create } from 'zustand';
import { getFirestore, doc, setDoc, getDoc, onSnapshot } from 'firebase/firestore';
import { firebaseApp } from 'app';

// Initialize Firestore
const db = getFirestore(firebaseApp);

import { AccountPlan } from '../utils/firebaseConfig';

// Define the user profile interface
export interface UserProfile {
  userId: string;
  displayName?: string;
  email?: string;
  photoURL?: string;
  createdAt: number;
  updatedAt: number;
  accountPlan?: AccountPlan;
  accountCreationDate?: number;
  accountExpiryDate?: number;
  stripeCustomerId?: string;
  hasActiveSubscription?: boolean;
  preferences?: {
    theme?: string;
    language?: string;
    emailNotifications?: boolean;
  }
}

// Define the store interface
interface UserProfileStore {
  profile: UserProfile | null;
  isLoading: boolean;
  error: Error | null;
  
  // Initialize the subscription to Firestore
  subscribeToProfile: (userId: string) => () => void;
  
  // Update user profile
  updateProfile: (data: Partial<Omit<UserProfile, 'userId' | 'createdAt'>>) => Promise<void>;
  
  // Create new profile if it doesn't exist
  createProfileIfNotExists: (userId: string, email?: string, displayName?: string, photoURL?: string) => Promise<void>;

  // Delete user profile
  deleteUserProfile: (userId: string) => Promise<void>;

  // Upgrade user to Enterprise tier
  upgradeToEnterpriseTier: (userId: string) => Promise<boolean>;
}

export const useUserProfileStore = create<UserProfileStore>((set, get) => ({
  profile: null,
  isLoading: true,
  error: null,
  
  // Subscribe to the user profile in Firestore
  subscribeToProfile: (userId: string) => {
    // If no userId, clear the profile
    if (!userId) {
      set({ profile: null, isLoading: false });
      return () => {};
    }
    
    // Set loading state while fetching
    set({ isLoading: true });
    
    try {
      // Subscribe to the user profile document
      const unsubscribe = onSnapshot(
        doc(db, 'userProfiles', userId),
        (docSnapshot) => {
          if (docSnapshot.exists()) {
            set({ profile: docSnapshot.data() as UserProfile, isLoading: false });
          } else {
            set({ profile: null, isLoading: false });
          }
        },
        (error) => {
          console.error('Error fetching user profile:', error);
          set({ error, isLoading: false, profile: null });
        }
      );
      
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up profile subscription:', error);
      set({ error: error as Error, isLoading: false, profile: null });
      return () => {};
    }
  },
  
  // Update the user profile
  updateProfile: async (data) => {
    const { profile } = get();
    if (!profile) {
      console.error('Cannot update profile: No profile loaded');
      return;
    }
    
    try {
      const updatedProfile = {
        ...profile,
        ...data,
        updatedAt: Date.now()
      };  
      // Optimistic update
      set({ profile: updatedProfile });
      
      // Update in Firestore
      await setDoc(doc(db, 'userProfiles', profile.userId), updatedProfile, { merge: true });
    } catch (error) {
      console.error('Error updating profile:', error);
      // Keep the optimistic update but log the error
      set({ error: error as Error });
    }
  },
  
  // Create a new profile if it doesn't exist
  createProfileIfNotExists: async (userId, email, displayName, photoURL) => {
    try {
      // Create fallback profile in memory if Firestore fails
      const fallbackProfile: UserProfile = {
        userId,
        email: email === undefined ? null : email,
        displayName: displayName === undefined ? null : displayName,
        photoURL: photoURL === undefined ? null : photoURL,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        accountPlan: AccountPlan.FREE, // Default to free plan
        preferences: {
          theme: 'system',
          emailNotifications: false
        }
      };

      try {
        // Check if profile exists
        const profileDoc = await getDoc(doc(db, 'userProfiles', userId));
        
        if (!profileDoc.exists()) {
          // Create new profile
          const newProfile: UserProfile = {
            userId,
            email: email === undefined ? null : email,
            displayName: displayName === undefined ? null : displayName,
            photoURL: photoURL === undefined ? null : photoURL,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };
          
          // Add default preferences when creating a new profile
          const newProfileWithPreferences: UserProfile = {
            ...newProfile,
            accountPlan: AccountPlan.FREE, // Default to free plan
            preferences: {
              theme: 'system',
              language: 'en',
              emailNotifications: false
            }
          };
          
          await setDoc(doc(db, 'userProfiles', userId), newProfileWithPreferences);
          set({ profile: newProfile, isLoading: false });
        }
      } catch (error) {
        console.warn('Error accessing Firestore, using fallback profile:', error);
        // Use fallback profile instead of showing an error
        set({ profile: fallbackProfile, isLoading: false });
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      // Don't update state with error, just log it
      // This allows the app to continue working even with Firestore permission issues
    }
  },

  // Upgrade user to Enterprise tier
  upgradeToEnterpriseTier: async (userId: string) => {
    try {
      if (!userId) {
        console.error('Cannot upgrade profile: No user ID provided');
        return false;
      }

      // Reference to the user profile document
      const userProfileRef = doc(db, 'userProfiles', userId);
      
      // Update to Enterprise tier
      await setDoc(userProfileRef, {
        accountPlan: AccountPlan.ENTERPRISE,
        updatedAt: Date.now()
      }, { merge: true });
      
      console.log(`User ${userId} upgraded to Enterprise tier`);
      return true;
    } catch (error) {
      console.error('Error upgrading to Enterprise tier:', error);
      return false;
    }
  },

  // Delete user profile from Firestore
  deleteUserProfile: async (userId: string) => {
    try {
      if (!userId) {
        console.error('Cannot delete profile: No user ID provided');
        return;
      }

      // Import deleteDoc function from firebase/firestore
      const { deleteDoc } = await import('firebase/firestore');
      
      // Reference to the user profile document
      const userProfileRef = doc(db, 'userProfiles', userId);
      
      // Delete the profile document
      await deleteDoc(userProfileRef);
      
      console.log(`User profile ${userId} deleted successfully`);
      set({ profile: null, isLoading: false });
    } catch (error) {
      console.error('Error deleting user profile:', error);
      // Don't update state with error, just log it
    }
  }
}));
