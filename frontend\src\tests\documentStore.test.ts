/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Tests für DocumentStore Service
 */

import { TestAssertions, TestRunner, MockUtils } from './test-utils';

// Mock für Firebase (da wir keine echte Firebase-Verbindung in Tests wollen)
const mockFirestore = {
  collection: MockUtils.createMockFunction(() => mockFirestore),
  doc: MockUtils.createMockFunction(() => mockFirestore),
  where: MockUtils.createMockFunction(() => mockFirestore),
  getDocs: MockUtils.createMockFunction(() => Promise.resolve({
    size: 0,
    docs: [],
    forEach: () => {}
  })),
  getDoc: MockUtils.createMockFunction(() => Promise.resolve({
    exists: () => false,
    data: () => ({})
  })),
  addDoc: MockUtils.createMockFunction(() => Promise.resolve({ id: 'test-doc-id' })),
  updateDoc: MockUtils.createMockFunction(() => Promise.resolve()),
  deleteDoc: MockUtils.createMockFunction(() => Promise.resolve())
};

// Mock für Firebase Auth
const mockAuth = {
  currentUser: {
    uid: 'test-user-id',
    email: '<EMAIL>'
  }
};

// DocumentStore Interface für Tests
interface DocumentMeta {
  id: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  language: string;
  codeSize: number;
  isPublic: boolean;
  tags: string[];
}

interface DocumentContent {
  originalCode: string;
  generatedDocumentation?: string;
  parameters?: Record<string, any>;
  toolsUsed?: string[];
  workflow?: any;
}

interface Document extends DocumentMeta {
  content: DocumentContent;
}

// Test-Implementierung der DocumentStore-Logik
class TestDocumentStore {
  private documents: DocumentMeta[] = [];
  private currentDocument: Document | null = null;
  private isLoading = false;
  private error: Error | null = null;

  // Simuliere Dokument-Erstellung
  async createDocument(doc: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    // Validierung der Eingabedaten
    if (!doc.title || doc.title.trim().length === 0) {
      throw new Error('Dokumenttitel ist erforderlich');
    }

    if (!doc.userId || doc.userId.trim().length === 0) {
      throw new Error('Benutzer-ID ist erforderlich');
    }

    if (!doc.content.originalCode || doc.content.originalCode.trim().length === 0) {
      throw new Error('Code-Inhalt ist erforderlich');
    }

    // Simuliere Dokument-Erstellung
    const newDoc: DocumentMeta = {
      id: `doc-${Date.now()}`,
      title: doc.title,
      description: doc.description || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: doc.userId,
      language: doc.language,
      codeSize: doc.content.originalCode.length,
      isPublic: doc.isPublic,
      tags: doc.tags || []
    };

    this.documents.push(newDoc);
    return newDoc.id;
  }

  // Simuliere Dokument-Abruf
  async getDocumentById(docId: string): Promise<Document | null> {
    const meta = this.documents.find(d => d.id === docId);
    if (!meta) {
      return null;
    }

    // Simuliere vollständiges Dokument
    const document: Document = {
      ...meta,
      content: {
        originalCode: 'print("Hello World")',
        generatedDocumentation: 'Test documentation',
        parameters: {},
        toolsUsed: ['python']
      }
    };

    this.currentDocument = document;
    return document;
  }

  // Simuliere Benutzer-Dokumente abrufen
  async fetchUserDocuments(userId: string): Promise<DocumentMeta[]> {
    const userDocs = this.documents.filter(d => d.userId === userId);
    return userDocs.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  // Simuliere Dokument-Update
  async updateDocument(docId: string, updates: Partial<Document>): Promise<void> {
    const docIndex = this.documents.findIndex(d => d.id === docId);
    if (docIndex === -1) {
      throw new Error('Dokument nicht gefunden');
    }

    // Update Metadaten
    if (updates.title) this.documents[docIndex].title = updates.title;
    if (updates.description !== undefined) this.documents[docIndex].description = updates.description;
    if (updates.isPublic !== undefined) this.documents[docIndex].isPublic = updates.isPublic;
    if (updates.tags) this.documents[docIndex].tags = updates.tags;
    
    this.documents[docIndex].updatedAt = new Date();
  }

  // Simuliere Dokument-Löschung
  async deleteDocument(docId: string): Promise<void> {
    const docIndex = this.documents.findIndex(d => d.id === docId);
    if (docIndex === -1) {
      throw new Error('Dokument nicht gefunden');
    }

    this.documents.splice(docIndex, 1);
    
    if (this.currentDocument?.id === docId) {
      this.currentDocument = null;
    }
  }

  // Getter für Tests
  getDocuments(): DocumentMeta[] {
    return this.documents;
  }

  getCurrentDocument(): Document | null {
    return this.currentDocument;
  }
}

// Test-Suite für DocumentStore
export function createDocumentStoreTests(): TestRunner {
  const runner = new TestRunner();
  
  runner.addTest('DocumentStore: Dokument erstellen - Erfolg', async () => {
    const store = new TestDocumentStore();
    
    const testDoc = {
      title: 'Test Dokument',
      description: 'Ein Test-Dokument',
      userId: 'user-123',
      language: 'Python',
      isPublic: false,
      tags: ['test', 'python'],
      content: {
        originalCode: 'print("Hello World")',
        generatedDocumentation: 'Test documentation'
      }
    };

    const docId = await store.createDocument(testDoc);
    
    TestAssertions.assertNotNull(docId, 'Dokument-ID sollte nicht null sein');
    TestAssertions.assertTrue(docId.startsWith('doc-'), 'Dokument-ID sollte mit "doc-" beginnen');
    
    const documents = store.getDocuments();
    TestAssertions.assertArrayLength(documents, 1, 'Ein Dokument sollte erstellt worden sein');
    TestAssertions.assertEqual(documents[0].title, testDoc.title, 'Dokumenttitel sollte übereinstimmen');
    TestAssertions.assertEqual(documents[0].userId, testDoc.userId, 'Benutzer-ID sollte übereinstimmen');
    TestAssertions.assertEqual(documents[0].codeSize, testDoc.content.originalCode.length, 'Code-Größe sollte korrekt berechnet werden');
  });

  runner.addTest('DocumentStore: Dokument erstellen - Fehlende Daten', async () => {
    const store = new TestDocumentStore();

    await TestAssertions.assertThrowsAsync(
      () => store.createDocument({
        title: '',
        userId: 'user-123',
        language: 'Python',
        isPublic: false,
        tags: [],
        content: { originalCode: 'print("test")' }
      }),
      'Dokumenttitel ist erforderlich',
      'Sollte Fehler bei leerem Titel werfen'
    );

    await TestAssertions.assertThrowsAsync(
      () => store.createDocument({
        title: 'Test',
        userId: '',
        language: 'Python',
        isPublic: false,
        tags: [],
        content: { originalCode: 'print("test")' }
      }),
      'Benutzer-ID ist erforderlich',
      'Sollte Fehler bei leerer Benutzer-ID werfen'
    );

    await TestAssertions.assertThrowsAsync(
      () => store.createDocument({
        title: 'Test',
        userId: 'user-123',
        language: 'Python',
        isPublic: false,
        tags: [],
        content: { originalCode: '' }
      }),
      'Code-Inhalt ist erforderlich',
      'Sollte Fehler bei leerem Code werfen'
    );
  });

  runner.addTest('DocumentStore: Dokument abrufen', async () => {
    const store = new TestDocumentStore();
    
    // Erst ein Dokument erstellen
    const docId = await store.createDocument({
      title: 'Test Dokument',
      userId: 'user-123',
      language: 'Python',
      isPublic: false,
      tags: ['test'],
      content: { originalCode: 'print("Hello")' }
    });

    // Dokument abrufen
    const document = await store.getDocumentById(docId);
    
    TestAssertions.assertNotNull(document, 'Dokument sollte gefunden werden');
    TestAssertions.assertEqual(document!.id, docId, 'Dokument-ID sollte übereinstimmen');
    TestAssertions.assertEqual(document!.title, 'Test Dokument', 'Dokumenttitel sollte übereinstimmen');
    TestAssertions.assertNotNull(document!.content, 'Dokument-Inhalt sollte vorhanden sein');
    
    // Nicht existierendes Dokument
    const nonExistentDoc = await store.getDocumentById('non-existent-id');
    TestAssertions.assertEqual(nonExistentDoc, null, 'Nicht existierendes Dokument sollte null zurückgeben');
  });

  runner.addTest('DocumentStore: Benutzer-Dokumente abrufen', async () => {
    const store = new TestDocumentStore();
    
    // Mehrere Dokumente für verschiedene Benutzer erstellen
    await store.createDocument({
      title: 'Dokument 1',
      userId: 'user-123',
      language: 'Python',
      isPublic: false,
      tags: [],
      content: { originalCode: 'print("1")' }
    });

    await store.createDocument({
      title: 'Dokument 2',
      userId: 'user-456',
      language: 'JavaScript',
      isPublic: false,
      tags: [],
      content: { originalCode: 'console.log("2")' }
    });

    await store.createDocument({
      title: 'Dokument 3',
      userId: 'user-123',
      language: 'Python',
      isPublic: false,
      tags: [],
      content: { originalCode: 'print("3")' }
    });

    // Dokumente für user-123 abrufen
    const user123Docs = await store.fetchUserDocuments('user-123');
    TestAssertions.assertArrayLength(user123Docs, 2, 'Benutzer 123 sollte 2 Dokumente haben');
    
    // Dokumente für user-456 abrufen
    const user456Docs = await store.fetchUserDocuments('user-456');
    TestAssertions.assertArrayLength(user456Docs, 1, 'Benutzer 456 sollte 1 Dokument haben');
    
    // Dokumente für nicht existierenden Benutzer
    const nonExistentUserDocs = await store.fetchUserDocuments('user-999');
    TestAssertions.assertArrayLength(nonExistentUserDocs, 0, 'Nicht existierender Benutzer sollte 0 Dokumente haben');
  });

  return runner;
}
