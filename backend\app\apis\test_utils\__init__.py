"""Test utilities for documentation generator"""

import asyncio
from typing import Dict, Any
from fastapi import APIRouter, Query

# Function to get language name from code
def get_language_name(language_code: str) -> str:
    """
    Get the full language name from the language code
    """
    language_map = {
        "en": "English",
        "de": "German (Deutsch)",
        "fr": "French (Français)",
        "it": "Italian (Italiano)",
        "es": "Spanish (Español)"
    }
    
    return language_map.get(language_code, "Unknown language")

from app.apis.documentation_models import DocumentationRequest
from app.apis.documentation_generator import generate_documentation

# Define the router
router = APIRouter()

# Simple sample scripts to avoid syntax issues
SAMPLE_GIS_SCRIPT = "import arcpy\nimport os\nfrom arcpy import env\n\n# Sample GIS script\nprint('Hello GIS world')\n"

SAMPLE_NON_GIS_SCRIPT = "import pandas as pd\nimport numpy as np\n\n# Sample data analysis script\nprint('Hello data world')\n"

async def test_documentation_generator(gis_script: bool = True, language: str = "en") -> Dict[str, Any]:
    """Test the documentation generator with a sample script
    
    Args:
        gis_script: Whether to use the GIS script (True) or non-GIS script (False)
        
    Returns:
        The documentation generation response as a dictionary
    """
    # Create a test request
    request = DocumentationRequest(
        code=SAMPLE_GIS_SCRIPT if gis_script else SAMPLE_NON_GIS_SCRIPT,
        language="python",
        provider="auto",
        format="markdown",
        documentationLanguage=language
    )
    
    # Generate documentation
    response = await generate_documentation(request)
    
    # Convert to dictionary for easier inspection
    response_dict = response.model_dump()
    
    return response_dict

@router.get("/test-documentation")
async def validate_code_endpoint2(language: str = Query("en", description="Documentation language code (en, de, fr, it, es)")):
    """Test endpoint for documentation generation"""
    result = await test_documentation_generator(gis_script=True, language=language)
    
    # Add language information to result
    result["language_requested"] = language
    result["language_name"] = get_language_name(language)
    
    return result
