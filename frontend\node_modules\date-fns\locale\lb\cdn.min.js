var z=function(K){return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},z(K)},N=function(K,J){var Q=Object.keys(K);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(K);J&&(T=T.filter(function(q){return Object.getOwnPropertyDescriptor(K,q).enumerable})),Q.push.apply(Q,T)}return Q},F=function(K){for(var J=1;J<arguments.length;J++){var Q=arguments[J]!=null?arguments[J]:{};J%2?N(Object(Q),!0).forEach(function(T){KB(K,T,Q[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(K,Object.getOwnPropertyDescriptors(Q)):N(Object(Q)).forEach(function(T){Object.defineProperty(K,T,Object.getOwnPropertyDescriptor(Q,T))})}return K},KB=function(K,J,Q){if(J=MB(J),J in K)Object.defineProperty(K,J,{value:Q,enumerable:!0,configurable:!0,writable:!0});else K[J]=Q;return K},MB=function(K){var J=OB(K,"string");return z(J)=="symbol"?J:String(J)},OB=function(K,J){if(z(K)!="object"||!K)return K;var Q=K[Symbol.toPrimitive];if(Q!==void 0){var T=Q.call(K,J||"default");if(z(T)!="object")return T;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(K)};(function(K){var J=Object.defineProperty,Q=function C(I,H){for(var B in H)J(I,B,{get:H[B],enumerable:!0,configurable:!0,set:function M(O){return H[B]=function(){return O}}})},T=function C(I){var H=I.charAt(0).toLowerCase();if(G.indexOf(H)!=-1||D.indexOf(H)!=-1)return!0;var B=I.split(" ")[0],M=parseInt(B);if(!isNaN(M)&&S.indexOf(M%10)!=-1&&_.indexOf(parseInt(B.substring(0,2)))==-1)return!0;return!1},q={lessThanXSeconds:{standalone:{one:"manner w\xE9i eng Sekonn",other:"manner w\xE9i {{count}} Sekonnen"},withPreposition:{one:"manner w\xE9i enger Sekonn",other:"manner w\xE9i {{count}} Sekonnen"}},xSeconds:{standalone:{one:"eng Sekonn",other:"{{count}} Sekonnen"},withPreposition:{one:"enger Sekonn",other:"{{count}} Sekonnen"}},halfAMinute:{standalone:"eng hallef Minutt",withPreposition:"enger hallwer Minutt"},lessThanXMinutes:{standalone:{one:"manner w\xE9i eng Minutt",other:"manner w\xE9i {{count}} Minutten"},withPreposition:{one:"manner w\xE9i enger Minutt",other:"manner w\xE9i {{count}} Minutten"}},xMinutes:{standalone:{one:"eng Minutt",other:"{{count}} Minutten"},withPreposition:{one:"enger Minutt",other:"{{count}} Minutten"}},aboutXHours:{standalone:{one:"ongef\xE9ier eng Stonn",other:"ongef\xE9ier {{count}} Stonnen"},withPreposition:{one:"ongef\xE9ier enger Stonn",other:"ongef\xE9ier {{count}} Stonnen"}},xHours:{standalone:{one:"eng Stonn",other:"{{count}} Stonnen"},withPreposition:{one:"enger Stonn",other:"{{count}} Stonnen"}},xDays:{standalone:{one:"een Dag",other:"{{count}} Deeg"},withPreposition:{one:"engem Dag",other:"{{count}} Deeg"}},aboutXWeeks:{standalone:{one:"ongef\xE9ier eng Woch",other:"ongef\xE9ier {{count}} Wochen"},withPreposition:{one:"ongef\xE9ier enger Woche",other:"ongef\xE9ier {{count}} Wochen"}},xWeeks:{standalone:{one:"eng Woch",other:"{{count}} Wochen"},withPreposition:{one:"enger Woch",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"ongef\xE9ier ee Mount",other:"ongef\xE9ier {{count}} M\xE9int"},withPreposition:{one:"ongef\xE9ier engem Mount",other:"ongef\xE9ier {{count}} M\xE9int"}},xMonths:{standalone:{one:"ee Mount",other:"{{count}} M\xE9int"},withPreposition:{one:"engem Mount",other:"{{count}} M\xE9int"}},aboutXYears:{standalone:{one:"ongef\xE9ier ee Joer",other:"ongef\xE9ier {{count}} Joer"},withPreposition:{one:"ongef\xE9ier engem Joer",other:"ongef\xE9ier {{count}} Joer"}},xYears:{standalone:{one:"ee Joer",other:"{{count}} Joer"},withPreposition:{one:"engem Joer",other:"{{count}} Joer"}},overXYears:{standalone:{one:"m\xE9i w\xE9i ee Joer",other:"m\xE9i w\xE9i {{count}} Joer"},withPreposition:{one:"m\xE9i w\xE9i engem Joer",other:"m\xE9i w\xE9i {{count}} Joer"}},almostXYears:{standalone:{one:"bal ee Joer",other:"bal {{count}} Joer"},withPreposition:{one:"bal engem Joer",other:"bal {{count}} Joer"}}},D=["d","h","n","t","z"],G=["a,","e","i","o","u"],S=[0,1,2,3,8,9],_=[40,50,60,70],V=function C(I,H,B){var M,O=q[I],U=B!==null&&B!==void 0&&B.addSuffix?O.withPreposition:O.standalone;if(typeof U==="string")M=U;else if(H===1)M=U.one;else M=U.other.replace("{{count}}",String(H));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"a"+(T(M)?"n":"")+" "+M;else return"viru"+(T(M)?"n":"")+" "+M;return M};function L(C){return function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=I.width?String(I.width):C.defaultWidth,B=C.formats[H]||C.formats[C.defaultWidth];return B}}var j={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.yy"},P={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},x={date:L({formats:j,defaultWidth:"full"}),time:L({formats:P,defaultWidth:"full"}),dateTime:L({formats:R,defaultWidth:"full"})},w={lastWeek:function C(I){var H=I.getDay(),B="'l\xE4schte";if(H===2||H===4)B+="n";return B+="' eeee 'um' p",B},yesterday:"'g\xEBschter um' p",today:"'haut um' p",tomorrow:"'moien um' p",nextWeek:"eeee 'um' p",other:"P"},b=function C(I,H,B,M){var O=w[I];if(typeof O==="function")return O(H);return O};function Z(C){return function(I,H){var B=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",M;if(B==="formatting"&&C.formattingValues){var O=C.defaultFormattingWidth||C.defaultWidth,U=H!==null&&H!==void 0&&H.width?String(H.width):O;M=C.formattingValues[U]||C.formattingValues[O]}else{var X=C.defaultWidth,E=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;M=C.values[E]||C.values[X]}var Y=C.argumentCallback?C.argumentCallback(I):I;return M[Y]}}var W={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["viru Christus","no Christus"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","M\xE4e","Abr","Mee","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","M\xE4erz","Abr\xEBll","Mee","Juni","Juli","August","September","Oktober","November","Dezember"]},k={narrow:["S","M","D","M","D","F","S"],short:["So","M\xE9","D\xEB","M\xEB","Do","Fr","Sa"],abbreviated:["So.","M\xE9.","D\xEB.","M\xEB.","Do.","Fr.","Sa."],wide:["Sonndeg","M\xE9indeg","D\xEBnschdeg","M\xEBttwoch","Donneschdeg","Freideg","Samschdeg"]},h={narrow:{am:"mo.",pm:"nom\xEB.",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"},abbreviated:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"},wide:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"M\xEBtteg",morning:"Moien",afternoon:"Nom\xEBtteg",evening:"Owend",night:"Nuecht"}},c={narrow:{am:"mo.",pm:"nom.",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"},abbreviated:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"},wide:{am:"moies",pm:"nom\xEBttes",midnight:"M\xEBtternuecht",noon:"m\xEBttes",morning:"moies",afternoon:"nom\xEBttes",evening:"owes",night:"nuets"}},m=function C(I,H){var B=Number(I);return B+"."},y={ordinalNumber:m,era:Z({values:W,defaultWidth:"wide"}),quarter:Z({values:f,defaultWidth:"wide",argumentCallback:function C(I){return I-1}}),month:Z({values:v,defaultWidth:"wide"}),day:Z({values:k,defaultWidth:"wide"}),dayPeriod:Z({values:h,defaultWidth:"wide",formattingValues:c,defaultFormattingWidth:"wide"})};function $(C){return function(I){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=H.width,M=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],O=I.match(M);if(!O)return null;var U=O[0],X=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],E=Array.isArray(X)?l(X,function(A){return A.test(U)}):d(X,function(A){return A.test(U)}),Y;Y=C.valueCallback?C.valueCallback(E):E,Y=H.valueCallback?H.valueCallback(Y):Y;var JB=I.slice(U.length);return{value:Y,rest:JB}}}var d=function C(I,H){for(var B in I)if(Object.prototype.hasOwnProperty.call(I,B)&&H(I[B]))return B;return},l=function C(I,H){for(var B=0;B<I.length;B++)if(H(I[B]))return B;return};function p(C){return function(I){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=I.match(C.matchPattern);if(!B)return null;var M=B[0],O=I.match(C.parsePattern);if(!O)return null;var U=C.valueCallback?C.valueCallback(O[0]):O[0];U=H.valueCallback?H.valueCallback(U):U;var X=I.slice(M.length);return{value:U,rest:X}}}var u=/^(\d+)(\.)?/i,g=/\d+/i,i={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(viru Christus|virun eiser Zäitrechnung|no Christus|eiser Zäitrechnung)/i},n={any:[/^v/i,/^n/i]},o={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},e={any:[/1/i,/2/i,/3/i,/4/i]},r={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mäe|abr|mee|jun|jul|aug|sep|okt|nov|dez)/i,wide:/^(januar|februar|mäerz|abrëll|mee|juni|juli|august|september|oktober|november|dezember)/i},a={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mä/i,/^ab/i,/^me/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[smdf]/i,short:/^(so|mé|dë|më|do|fr|sa)/i,abbreviated:/^(son?|méi?|dën?|mët?|don?|fre?|sam?)\.?/i,wide:/^(sonndeg|méindeg|dënschdeg|mëttwoch|donneschdeg|freideg|samschdeg)/i},t={any:[/^so/i,/^mé/i,/^dë/i,/^më/i,/^do/i,/^f/i,/^sa/i]},BB={narrow:/^(mo\.?|nomë\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,abbreviated:/^(moi\.?|nomët\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,wide:/^(moies|nomëttes|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i},CB={any:{am:/^m/i,pm:/^n/i,midnight:/^Mëtter/i,noon:/^mëttes/i,morning:/moies/i,afternoon:/nomëttes/i,evening:/owes/i,night:/nuets/i}},HB={ordinalNumber:p({matchPattern:u,parsePattern:g,valueCallback:function C(I){return parseInt(I,10)}}),era:$({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),quarter:$({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any",valueCallback:function C(I){return I+1}}),month:$({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),day:$({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),dayPeriod:$({matchPatterns:BB,defaultMatchWidth:"wide",parsePatterns:CB,defaultParseWidth:"any"})},IB={code:"lb",formatDistance:V,formatLong:x,formatRelative:b,localize:y,match:HB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=F(F({},window.dateFns),{},{locale:F(F({},(K=window.dateFns)===null||K===void 0?void 0:K.locale),{},{lb:IB})})})();

//# debugId=76B8E594F67B09F964756e2164756e21
