import{d as f,_ as w}from"./index-********.js";import{A as S,m as l,j as e,L as y,$ as A,G as k,d as E}from"./vendor-8eb8bd34.js";import{C as _,a as L,b as R,d as z,c as I,B as m}from"./card-b935221e.js";import{L as p,I as x}from"./label-ef207e6f.js";import{u as r}from"./index-fcfcda09.js";function D(){const v=S(),[c,N]=l.useState(""),[t,C]=l.useState(""),[n,b]=l.useState(""),[g,i]=l.useState(""),[u,d]=l.useState(!1),j=async()=>{if(t!==n){i("Passwords do not match"),r.error("Passwords do not match");return}if(t.length<6){i("Password must be at least 6 characters long"),r.error("Password must be at least 6 characters long");return}d(!0),i("");try{const s=(await A(f,c,t)).user;if(r.success("Account created successfully!"),s)try{const{useUserProfileStore:o}=await w(()=>import("./index-********.js").then(h=>h.p),["assets/index-********.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await o.getState().createProfileIfNotExists(s.uid,s.email===void 0?null:s.email,s.displayName===void 0?null:s.displayName,s.photoURL===void 0?null:s.photoURL),console.log(`Profile creation/check initiated for user ${s.uid}`)}catch(o){console.warn("Non-critical error during profile creation/check:",o),r.warning("Account created, but failed to initialize profile. You can update it later.")}else console.warn("User object not available immediately after creation."),r.warning("Account created, but failed to retrieve user details for profile initialization.");v("/")}catch(a){console.error("Registration failed:",a);let s="Registration failed. Please try again.";a.code==="auth/email-already-in-use"?s="This email address is already in use.":a.code==="auth/weak-password"?s="The password is too weak. Please use at least 6 characters.":a.code==="auth/invalid-email"&&(s="The email address is not valid."),i(s),r.error(s)}finally{d(!1)}},P=async()=>{try{d(!0),i("");const a=new k;a.addScope("profile"),a.addScope("email"),await E(f,a),setTimeout(async()=>{try{const s=f.currentUser;if(s){const{useUserProfileStore:o}=await w(()=>import("./index-********.js").then(h=>h.p),["assets/index-********.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await o.getState().createProfileIfNotExists(s.uid,s.email===void 0?null:s.email,s.displayName===void 0?null:s.displayName,s.photoURL===void 0?null:s.photoURL),r.success("Account created successfully!")}}catch(s){console.warn("Non-critical error updating profile after Google sign-in:",s)}v("/")},500)}catch(a){console.error("Google authentication failed:",a),i("Google authentication failed"),d(!1)}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4",children:e.jsxs(_,{className:"w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(L,{className:"text-center",children:[e.jsxs("div",{className:"mx-auto w-12 h-12 flex items-center justify-center mb-2",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:"GIS"})]}),e.jsx(R,{className:"text-2xl font-mono tracking-wide",children:"Create an Account"}),e.jsx(z,{children:"Join CodeScribe GIS to start creating documentation"})]}),e.jsx(I,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"email",children:"Email"}),e.jsx(x,{id:"email",type:"email",placeholder:"<EMAIL>",value:c,onChange:a=>N(a.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"password",children:"Password"}),e.jsx(x,{id:"password",type:"password",placeholder:"••••••••",value:t,onChange:a=>C(a.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"confirmPassword",children:"Confirm Password"}),e.jsx(x,{id:"confirmPassword",type:"password",placeholder:"••••••••",value:n,onChange:a=>b(a.target.value),className:"font-mono",onKeyDown:a=>{a.key==="Enter"&&c&&t&&n&&j()}})]}),g&&e.jsx("div",{className:"text-sm font-medium text-destructive p-2 border border-destructive/20 bg-destructive/5 rounded-md mb-2",children:g}),e.jsx(m,{type:"button",className:"w-full justify-center",disabled:u||!c||!t||!n,onClick:j,children:u?e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{children:"Creating Account..."})]}):"Create Account"}),e.jsx("div",{className:"text-center",children:e.jsx(m,{variant:"link",className:"font-mono text-xs",asChild:!0,children:e.jsx(y,{to:"/login",children:"Already have an account? Sign in"})})}),e.jsxs("div",{className:"relative my-6",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("span",{className:"w-full border-t border-border/40"})}),e.jsx("div",{className:"relative flex justify-center text-xs",children:e.jsx("span",{className:"bg-card px-2 text-muted-foreground",children:"Or continue with"})})]}),e.jsxs(m,{variant:"outline",className:"w-full justify-center space-x-2",onClick:P,disabled:u,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",className:"h-5 w-5",children:[e.jsx("path",{fill:"#FFC107",d:"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"}),e.jsx("path",{fill:"#FF3D00",d:"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"}),e.jsx("path",{fill:"#4CAF50",d:"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"}),e.jsx("path",{fill:"#1976D2",d:"M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"})]}),e.jsx("span",{children:"Sign up with Google"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx(m,{variant:"ghost",size:"sm",className:"font-mono text-xs",asChild:!0,children:e.jsx(y,{to:"/",children:"Back to Home"})})})]})})]})})}export{D as default};
