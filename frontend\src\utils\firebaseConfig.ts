// Firestore collection and document types
export const COLLECTIONS = {
  USERS: 'users',
  USER_PROFILES: 'userProfiles',
  DOCUMENTS: 'documentations',
  PAYMENTS: 'payments'
};

// Account plan types
export enum AccountPlan {
  FREE = 'free',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

// Plan features and limits
export const PLAN_FEATURES = {
  [AccountPlan.FREE]: {
    maxDocuments: 5,
    maxStorageBytes: 5 * 1024 * 1024, // 5MB
    allowAdvancedAnalytics: false,
    allowBatchProcessing: false,
    price: 0
  },
  [AccountPlan.PRO]: {
    maxDocuments: 50,
    maxStorageBytes: 100 * 1024 * 1024, // 100MB
    allowAdvancedAnalytics: true,
    allowBatchProcessing: true,
    price: 9.99
  },
  [AccountPlan.ENTERPRISE]: {
    maxDocuments: 500,
    maxStorageBytes: 1024 * 1024 * 1024, // 1GB
    allowAdvancedAnalytics: true,
    allowBatchProcessing: true,
    allowCustomBranding: true,
    allowTeamCollaboration: true,
    price: 49.99
  }
};
