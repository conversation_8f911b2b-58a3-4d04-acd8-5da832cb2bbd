import React, { useState, useEffect, useMemo } from 'react'; // Added useMemo
console.log("[Editor.tsx] Component rendering anfangen"); // DEBUG LOG 1
import brain from 'brain';
import { useNavigate, useLocation } from 'react-router-dom';
import { getFirestore, doc, getDoc, collection, getDocs, writeBatch, updateDoc, arrayUnion, setDoc } from 'firebase/firestore';
import { useUserGuardContext, firebaseApp } from 'app';
import { toast } from 'sonner';
import { Loader2, Save, ArrowLeft, Plus, FileCode2, Book } from 'lucide-react';

// Components
import { Button } from '@/components/ui/button';
import { LoadingOverlay } from 'components/LoadingOverlay';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MonacoEditor } from 'components/MonacoEditor';
import { CodeUploader } from 'components/CodeUploader';
import { FileTypeIndicator } from 'components/FileTypeIndicator';
import { DocumentationPreview } from 'components/DocumentationPreview';

// Types and utilities
import { COLLECTIONS } from 'utils/firebaseConfig';
import { Document } from 'utils/documentStore';

import { fixAllDocumentPermissions } from 'utils/documentPermissions';
import { useUserProfileStore } from 'utils/userProfileStore';

export default function Editor() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useUserGuardContext();
  const { profile } = useUserProfileStore();
  const [activeTab, setActiveTab] = useState('editor');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [document, setDocument] = useState<Document | null>(null);
  const [viewMode, setViewMode] = useState(false);
  
  // Editor state
  const [code, setCode] = useState('');
  const [documentTitle, setDocumentTitle] = useState('');
  const [documentDescription, setDocumentDescription] = useState('');
  const [projectContext, setProjectContext] = useState<string>(""); // MYA-15: Changed to string
  const [isUpdating, setIsUpdating] = useState(false);
  const [isGeneratingDoc, setIsGeneratingDoc] = useState(false);
  const [isSummarizing, setIsSummarizing] = useState(false); // MYA-15: State for summarization loading

  const [uploadedFile, setUploadedFile] = useState<{ name: string; extension: string; type?: string } | null>(null);
  const [contextFiles, setContextFiles] = useState<File[]>([]); // MYA-15: State for multiple context files

  useEffect(() => {
    console.log("[Editor.tsx] useEffect anfangen - location.search:", location.search); // DEBUG LOG 2
    // Add a helper function to handle document limit checks
    const handleDocumentLimitCheck = async (user, callback) => {
      if (!user?.uid) {
        toast.error('Sie müssen angemeldet sein, um ein Dokument zu erstellen');
        return false;
      }

      try {
        const { useDocumentStore } = await import('utils/documentStore');
        const { canCreate, currentCount, maxAllowed } = await useDocumentStore.getState().checkDocumentLimit(user.uid);
        
        if (!canCreate) {
          setError(`Sie haben das Limit von ${maxAllowed} Dokumenten für Ihren aktuellen Plan erreicht. Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente.`);
          toast.error(
            <div className="space-y-2">
              <p><strong>Dokumentenlimit erreicht!</strong></p>
              <p>Sie haben das Limit von {maxAllowed} Dokumenten für Ihren aktuellen Plan erreicht.</p>
              <p className="text-sm">Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente.</p>
            </div>,
            { duration: 8000 }
          );
          return false;
        }
        return true;
      } catch (err) {
        console.error('Error checking document limit:', err);
        // Always return true to allow document creation if the limit check fails
        // This ensures a better user experience without blocking document creation
        return true;
      }
    };
    // Get document ID and other parameters from URL parameters
    const params = new URLSearchParams(window.location.search); // MYA-19: Use window.location.search for immediate consistency
    const docId = params.get('id');
    const viewParam = params.get('view');
    const newDoc = params.get('new');
    const tabParam = params.get('tab');
    
    console.log('URL parameters:', params.toString(), 'viewParam:', viewParam);
    
    // Explicitly set view mode based on URL parameter
    // Check for multiple possible true values to ensure robust detection
    const isViewMode = viewParam === 'true' || viewParam === 'True' || viewParam === '1' || viewParam === 'yes';
    console.log('Setting view mode to:', isViewMode);
    setViewMode(isViewMode);
    
    // Set active tab from URL parameter if present
    if (tabParam && (tabParam === 'editor' || tabParam === 'documentation')) {
      setActiveTab(tabParam);
    }

    const loadDocument = async () => {
      // Handle new document creation
      if (newDoc === 'true' && !docId) {
        // Check document limit before creating a new one
        if (user?.uid) {
          const canProceed = await handleDocumentLimitCheck(user);
          if (!canProceed) {
            setIsLoading(false);
            return;
          }
          
          // Improved duplicate document prevention
          const lastCreationTime = sessionStorage.getItem('lastDocumentCreationTime');
          const now = Date.now();
          if (lastCreationTime && (now - parseInt(lastCreationTime)) < 10000) { // Increased to 10 seconds
            console.log('Preventing duplicate document creation, redirecting to documents page');
            toast.info('Document creation already in progress');
            window.location.href = './documents';
            return;
          }
          
          // Set a timestamp to prevent duplicate creation
          sessionStorage.setItem('lastDocumentCreationTime', now.toString());
        }
        
        setIsLoading(false);
        setDocument({
          id: 'new',
          title: 'New Document',
          description: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: user?.uid || '',
          language: 'python',
          codeSize: 0,
          isPublic: false,
          tags: [],
          content: {
            originalCode: '',
            generatedDocumentation: null,
            parameters: null,
            toolsUsed: null,
            workflow: null,
            manualEdits: []
          }
        });
        setCode('');
        setDocumentTitle('New Document');
        setDocumentDescription('');
        setProjectContext(""); // MYA-15: Initialize project context as empty string
        setUploadedFile(null); // Reset uploaded file when creating new document
        // setContextFile(null); // contextFile state is no longer used
        setContextFiles([]); // MYA-15: Reset multiple context files on new document
        return;
      }
      
      // Handle loading existing document
      if (!docId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        toast.loading('Loading document...');

        // Make sure user is logged in
        if (!user?.uid) {
          setError('You must be logged in to view this document');
          toast.error('You must be logged in to view this document');
          setIsLoading(false);
          return;
        }

        // Get document from Firestore
        const db = getFirestore(firebaseApp);
        const docRef = doc(db, COLLECTIONS.DOCUMENTS, docId);
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          setError('Document not found');
          toast.error('Document not found');
          setIsLoading(false);
          return;
        }

        const docData = docSnapshot.data();

        // Check if user has permission
        if (docData.userId !== user.uid && !docData.isPublic) {
          // Try to fix permissions
          toast.loading('Checking document permissions...');
          await fixAllDocumentPermissions(user.uid);

          // Try again
          const updatedSnapshot = await getDoc(docRef);
          if (!updatedSnapshot.exists() || 
              (updatedSnapshot.data().userId !== user.uid && !updatedSnapshot.data().isPublic)) {
            setError('You do not have permission to view this document');
            toast.error('You do not have permission to view this document');
            setIsLoading(false);
            return;
          }
        }

        // Get content subcollection
        const contentSnap = await getDocs(collection(docRef, 'content'));

        if (contentSnap.empty) {
          setError('Document content not found');
          toast.error('Document content not found');
          setIsLoading(false);
          return;
        }

        const contentData = contentSnap.docs[0].data();

        // Create document object
        const documentData: Document = {
          id: docId,
          title: docData.title,
          description: docData.description,
          createdAt: docData.createdAt?.toDate(),
          updatedAt: docData.updatedAt?.toDate(),
          userId: docData.userId,
          language: docData.language,
          codeSize: docData.codeSize,
          isPublic: docData.isPublic,
          tags: docData.tags || [],
          content: {
            originalCode: contentData.originalCode,
            generatedDocumentation: contentData.generatedDocumentation,
            parameters: contentData.parameters,
            toolsUsed: contentData.toolsUsed,
            workflow: contentData.workflow,
            manualEdits: contentData.manualEdits?.map((edit: any) => ({
              ...edit,
              timestamp: edit.timestamp?.toDate()
            })) || []
          }
        };

        setDocument(documentData);
        setCode(documentData.content.originalCode || '');
        setDocumentTitle(documentData.title);
        setDocumentDescription(documentData.description || '');
        
        // Logging the raw project_context from Firestore
        console.log("[Editor.tsx] Raw docData.project_context from Firestore:", docData.project_context);

        if (docData.project_context) {
          if (typeof docData.project_context === 'string') {
            setProjectContext(docData.project_context);
            console.log("[Editor.tsx] Set projectContext from string:", docData.project_context);
          } else if (typeof docData.project_context === 'object' && docData.project_context !== null) {
            console.log("[Editor.tsx] docData.project_context is object:", docData.project_context);
            const contextObj = docData.project_context as { text_context?: string; file_context_summary?: string };
            let combinedContext = "";
            if (contextObj.text_context && typeof contextObj.text_context === 'string' && contextObj.text_context.trim() !== "") {
              combinedContext += `### Manueller Kontext\n\n${contextObj.text_context.trim()}\n\n`;
            }
            if (contextObj.file_context_summary && typeof contextObj.file_context_summary === 'string' && contextObj.file_context_summary.trim() !== "") {
              combinedContext += `### Zusammenfassung der Kontextdateien\n\n${contextObj.file_context_summary.trim()}\n\n`;
            }
            setProjectContext(combinedContext.trim());
            console.log("[Editor.tsx] Set projectContext from object (combined):", combinedContext.trim());
          } else {
            setProjectContext("");
            console.log("[Editor.tsx] Set projectContext to empty string (type was not string or expected object).");
          }
        } else {
          setProjectContext("");
          console.log("[Editor.tsx] docData.project_context is null or undefined, set projectContext to empty string.");
        }

        setUploadedFile(null); // Reset uploaded file when loading existing document
        // setContextFile(null); // contextFile state is no longer used for single file
        setContextFiles([]); // MYA-15: Reset multiple context files on loading existing document
        toast.success('Document loaded successfully');
        
        // Check URL to make sure we're respecting the view mode parameter
        const params = new URLSearchParams(window.location.search); // MYA-19: Use window.location.search for immediate consistency
        const viewParam = params.get('view');
        console.log('Final viewParam check after document load:', viewParam);
        if (viewParam === 'true' || viewParam === 'True' || viewParam === '1' || viewParam === 'yes') {
          console.log('Re-setting view mode to true after document load');
          setViewMode(true);
        }
      } catch (err) {
        console.error('Error loading document:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        toast.error(`Error loading document: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
        toast.dismiss();
      }
    };

    loadDocument();
  }, [location.search, user]);

  // Function to save document - Modified to return a promise
  const saveDocument = async (): Promise<boolean> => {
    if (!user?.uid) {
      toast.error('You must be logged in to save documents');
      return false;
    }
    
    setIsUpdating(true);
    toast.loading('Saving document...');
    
    try {
      const db = getFirestore(firebaseApp);
      
      // Permission fixing removed for performance - rely on direct updates and Firestore rules

      // Check if this is EXPLICITLY a new document
      // Only create a new document if new=true is explicitly in the URL
      // AND there's no document ID parameter
      const params = new URLSearchParams(window.location.search); // MYA-19: Ensure immediate URL state is used
      const hasDocId = params.get('id') !== null;
      const isNewDocument = !document || (params.get('new') === 'true' && !hasDocId);
      
      // Create timestamp for document operations
      const now = new Date();
      
      if (isNewDocument) {
        console.log('Creating new document');
        const newDocRef = doc(collection(db, COLLECTIONS.DOCUMENTS));
        sessionStorage.removeItem('lastDocumentCreationTime');

        const newDocData = {
          id: newDocRef.id,
          title: documentTitle || 'Untitled Document',
          description: documentDescription || '',
          project_context: projectContext, // MYA-15: Save the projectContext string
          createdAt: now,
          updatedAt: now,
          userId: user.uid,
          language: 'python', 
          codeSize: code.length,
          isPublic: false,
          tags: [],
          isDeleted: false,
          owner: user.uid,
          readers: [user.uid],
          writers: [user.uid],
          permissionsLastUpdated: now
        };

        const contentData = {
          originalCode: code,
          generatedDocumentation: null,
          parameters: null,
          toolsUsed: null,
          workflow: null,
          manualEdits: []
        };

        // Step 1: Create the main document
        await setDoc(newDocRef, newDocData);
        console.log('Main document created successfully');

        // Step 2: Create the content subcollection document AFTER main doc exists
        const contentRef = doc(collection(newDocRef, 'content'), 'latest');
        await setDoc(contentRef, contentData);
        console.log('Content subcollection document created successfully');

        // Update the state with the new document
        setDocument({
          ...newDocData,
          content: contentData
        } as Document);

        // Update the URL
        const newUrl = `${window.location.pathname}?id=${newDocRef.id}`;
        window.history.replaceState(null, '', newUrl);
        console.log('Updated URL without full navigation');

        toast.success('New document created successfully');
        return true;
      } else {
        console.log('Updating existing document');
        // Update existing document
        if (!document?.id) {
          toast.error('Cannot update document without an ID.');
          return false;
        }
        const docRef = doc(db, COLLECTIONS.DOCUMENTS, document.id);

        // REMOVED: Explicit permission update attempt - rely on Firestore rules
        // try { ... } catch { ... }

        // Update the main document first (Firestore rules will check permissions)
        await updateDoc(docRef, {
          title: documentTitle,
          description: documentDescription,
          project_context: projectContext, // MYA-15: Add projectContext string
          updatedAt: now,
          codeSize: code.length
          // Optionally update permissionsLastUpdated if needed on every save
          // permissionsLastUpdated: now 
        });
        console.log('Main document updated successfully');

        // Update the content subcollection (Firestore rules will check permissions via get())
        const contentRef = doc(collection(docRef, 'content'), 'latest');
        await updateDoc(contentRef, {
          originalCode: code,
          // Update manual edits if needed, otherwise remove or keep existing logic
          manualEdits: arrayUnion({
            timestamp: now,
            section: 'code',
            content: 'Manual update'
          })
        });
        console.log('Content subcollection updated successfully');

        // Update the local document state
        setDocument((prevDoc) => {
          if (!prevDoc) return null; // Should not happen here
          return {
            ...prevDoc,
            title: documentTitle,
            description: documentDescription,
            project_context: projectContext, // MYA-15: Add projectContext string
            updatedAt: now,
            codeSize: code.length,
            content: {
              ...prevDoc.content,
              originalCode: code,
              manualEdits: [
                ...(prevDoc.content.manualEdits || []),
                {
                  timestamp: now,
                  section: 'code',
                  content: 'Manual update'
                }
              ]
            }
          };
        });

        toast.success('Document updated successfully');
        return true;
      }
    } catch (err) {
      // Enhanced error logging
      console.error('Detailed error saving document:', err);
      let detailedMessage = 'An unknown error occurred';
      let firestoreErrorCode = 'N/A';
      
      if (err instanceof Error) {
        detailedMessage = err.message;
        // Attempt to access Firestore specific error properties if they exist
        // Use type assertion carefully or check property existence
        if (typeof err === 'object' && err !== null && 'code' in err) {
             firestoreErrorCode = (err as any).code; 
        }

        if (firestoreErrorCode && firestoreErrorCode !== 'N/A') {
          detailedMessage += ` (Firestore code: ${firestoreErrorCode})`;
        }
        
        console.error('Error Name:', err.name);
        console.error('Error Message:', err.message);
        if (firestoreErrorCode !== 'N/A') {
           console.error('Firestore Error Code:', firestoreErrorCode);
        }
        // Log the stack trace if available
        if (err.stack) {
          console.error('Stack Trace:', err.stack);
        }
      } else {
         console.error('Non-Error object thrown:', err);
         detailedMessage = 'An unexpected non-error type was thrown.';
      }

      // Always dismiss loading toast on error
      toast.dismiss(); 

      // Detailed error handling for Firestore permission issues in toast
      if ((err instanceof Error && err.message.includes('permission')) || (firestoreErrorCode.includes('permission-denied'))) {
        toast.error(
          <div className="space-y-2 p-2">
            <p className="font-semibold">Permissions error when saving document.</p>
            <p className="text-xs">This is likely due to Firestore security rules. Please check the browser console (F12) for more technical details.</p>
            <p className="text-xs font-mono bg-muted p-1 rounded">Error: {detailedMessage}</p>
            <p className="text-xs mt-2">Possible checks:</p>
            <ul className="text-xs list-disc list-inside space-y-1 pl-4">
              <li>Are you correctly signed in?</li>
              <li>Do Firestore rules for the 'documents' and their 'content' subcollections allow the necessary 'create' or 'update' operations for your user ID? (See console for specific operation causing failure)</li>
            </ul>
          </div>, 
          { duration: 15000 } // Show toast longer for debugging
        );
      } else {
        toast.error(`Error saving document: ${detailedMessage}`);
      }
      return false;
    } finally {
        setIsUpdating(false);
        // Make sure loading toast is dismissed even if error handling had issues
        // Use a small timeout to prevent race condition with error toast
        setTimeout(() => toast.dismiss(), 100);
    }
  };


  // Function to handle summarizing context files
  const handleSummarizeContextFiles = async () => {
    if (contextFiles.length === 0) {
      toast.info("Bitte wählen Sie zuerst Dateien für die Zusammenfassung aus.");
      return;
    }
    if (viewMode) {
      toast.info("Das Zusammenfassen von Kontextdateien ist im Ansichtsmodus nicht möglich.");
      return;
    }

    setIsSummarizing(true);
    const toastId = toast.loading("Kontextdateien werden zusammengefasst...");

    try {
      // Direkt das Objekt übergeben, das der Brain-Client erwartet
      const body = { files: contextFiles };

      // @ts-ignore // Vorerst ignorieren, falls TypeScript wegen FormData vs. Objekt meckert, der Client sollte es handhaben
      const response = await brain.summarize_uploaded_files(body);

      if (!response.ok) {
        let errorText = `Fehler beim Zusammenfassen: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorText = errorData.detail || errorText;
        } catch (e) {
          // Ignore if parsing error data fails
        }
        throw new Error(errorText);
      }

      const result = await response.json(); // Expects { summary: string | null, message: string, ...rest }
      const summary = result.summary;
      const message = result.message;

      if (summary && summary.trim() !== "") {
        setProjectContext(prevContext => {
          const newSummarySection = `### Zusammenfassung der Kontextdateien\n\n${summary.trim()}`;
          // Check if prevContext is not null and is a string before calling trim()
          const trimmedPrevContext = prevContext && typeof prevContext === 'string' ? prevContext.trim() : "";
          
          if (trimmedPrevContext !== "") {
            if (trimmedPrevContext.includes("### Zusammenfassung der Kontextdateien")) {
              // More robustly find and replace or append to the existing summary section
              // For now, simple append if header exists, or add new section
              // This could be improved to replace an old summary if one exists.
              return trimmedPrevContext + "\n\n" + summary.trim(); // Appending new summary part, might lead to multiple summaries if not handled carefully
            }
            return trimmedPrevContext + "\n\n" + newSummarySection;
          }
          return newSummarySection;
        });
        toast.success(message || "Kontextdateien erfolgreich zusammengefasst.", { id: toastId });
      } else {
        // No summary was returned, but the API call itself was successful (e.g., files processed, but no content to summarize)
        // Display the message from the API.
        if (message) {
          toast.info(message, { id: toastId, duration: 8000 }); // Show info toast for longer
        } else {
          // Fallback if API provides no message, though it should
          toast.warning("Die API hat keine Zusammenfassung zurückgegeben, aber auch keine spezifische Nachricht.", { id: toastId, duration: 8000 });
        }
      }
      
      setContextFiles([]); // Clear files after processing, regardless of summary outcome

    } catch (error) {
      console.error("Error summarizing context files:", error);
      toast.error(`Fehler beim Zusammenfassen der Kontextdateien: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`, { id: toastId });
    } finally {
      setIsSummarizing(false);
    }
  };

  // START: useMemo for processedDocumentation
  const processedDocumentation = useMemo(() => {
    if (!document?.content?.generatedDocumentation) {
      return {
        originalCode: document?.content?.originalCode || '',
        textualDescription: "Keine Textbeschreibung verfügbar.",
        parameters: [],
        toolsUsed: [],
        workflow: { steps: [] }
      };
    }

    let parsedDoc = {};
    if (typeof document.content.generatedDocumentation === 'string') {
      try {
        parsedDoc = JSON.parse(document.content.generatedDocumentation);
      } catch (e) {
        console.error("Failed to parse document.content.generatedDocumentation:", e);
        // parsedDoc remains {}
      }
    } else if (typeof document.content.generatedDocumentation === 'object' && document.content.generatedDocumentation !== null) {
      // If it's already an object (e.g., from a previous state or direct assignment by mistake)
      parsedDoc = document.content.generatedDocumentation;
    }
    
    return {
      originalCode: document.content.originalCode || '',
      textualDescription: parsedDoc.textualDescription || "Keine Textbeschreibung verfügbar.",
      parameters: parsedDoc.parameters || [],
      toolsUsed: parsedDoc.toolsUsed || [],
      workflow: parsedDoc.workflow || { steps: [] },
      // Spread any other properties from the parsed documentation
      ...(Object.keys(parsedDoc).length > 0 ? parsedDoc : {})
    };
  }, [document?.content?.generatedDocumentation, document?.content?.originalCode]);

  console.log('[Editor.tsx] Processed Documentation for Preview:', processedDocumentation);
  // END: useMemo for processedDocumentation
  console.log("[Editor.tsx] Project Context BEFORE passing to Preview:", projectContext);


  // Render loading state
  if (isLoading) {
    return (
      <div className="container p-4 flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <Loader2 className="h-10 w-10 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading document...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="container p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xl font-bold">Code Documentation Generator</h1>
          <Button onClick={() => {
          // Directly use window.location.href for more reliable navigation
          const basePath = window.location.pathname.endsWith('/') ? 
            window.location.pathname : 
            window.location.pathname + '/';
          window.location.href = './documents';
        }}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </div>
        
        <div className="border rounded p-6 bg-destructive/10">
          <h2 className="text-lg font-bold mb-4">Error Loading Document</h2>
          <p>{error}</p>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => {
              // Directly use window.location.href for more reliable navigation
              const basePath = window.location.pathname.endsWith('/') ? 
                window.location.pathname : 
                window.location.pathname + '/';
              window.location.href = basePath.split('/editor')[0] + '/documents';
            }}
          >
            Return to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-xl font-bold">Code Documentation Generator</h1>
        <Button onClick={() => {
          // Directly use window.location.href for more reliable navigation
          const basePath = window.location.pathname.endsWith('/') ? 
            window.location.pathname : 
            window.location.pathname + '/';
          window.location.href = basePath.split('/editor')[0] + '/documents';
        }}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Documents
        </Button>
      </div>
      
      {document ? (
        <div>
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-lg">{documentTitle}</h2>
              {documentDescription && <p className="text-sm text-muted-foreground">{documentDescription}</p>}
            </div>
            {!viewMode && (
              <Button 
                onClick={saveDocument} 
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> Saving...</>
                ) : (
                  <><Save className="h-4 w-4 mr-2" /> Save</>
                )}
              </Button>
            )}
          </div>
          
          {/* Add title and description fields */}
      <div className="bg-card/30 p-4 rounded-md mb-4">
        <div className="flex flex-col gap-2 mb-4">
          <div className="flex flex-col">
            <label htmlFor="documentTitle" className="text-sm font-medium mb-1">Dokumenttitel</label>
            <input
              id="documentTitle"
              type="text"
              value={documentTitle}
              onChange={(e) => setDocumentTitle(e.target.value)}
              placeholder="Geben Sie einen Titel für dieses Dokument ein"
              className="border rounded-md p-2"
              disabled={viewMode}
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="documentDescription" className="text-sm font-medium mb-1">Beschreibung</label>
            <textarea
              id="documentDescription"
              value={documentDescription}
              onChange={(e) => setDocumentDescription(e.target.value)}
              placeholder="Beschreiben Sie den Zweck dieses Codes"
              className="border rounded-md p-2 min-h-[80px]"
              disabled={viewMode}
            />
          </div>
          {/* NEW WRAPPER for Project Context and File Upload */}
          <div className="bg-muted/30 backdrop-blur-sm border border-border/30 rounded-lg p-4 mt-4">
            {/* Project Context Section */}
            <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <label htmlFor="projectContextTextarea" className="text-sm font-medium mb-1">Projektkontext (optional)</label>
            </div>
            <textarea
              id="projectContextTextarea"
              value={projectContext || ''}
              onChange={(e) => setProjectContext(e.target.value)}
              placeholder="Geben Sie zusätzlichen Kontext für die Dokumentationsgenerierung ein (z.B. Projekt-Hintergrundinformationen, spezifische Domänen-Terminologie)"
              className="border rounded-md p-2 min-h-[120px]"
              disabled={viewMode}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Dieser Kontext wird während der Dokumentationsgenerierung verwendet, um domänenspezifisches Wissen einzubeziehen.
            </p>
          </div>

          {/* Context File Upload Section */}
          <div className="flex flex-col mt-3">
            <label htmlFor="contextFiles" className="text-sm font-medium mb-1">Kontextdateien (optional, z.B. PDF, DOCX, TXT, Bilder)</label>
            <input
              id="contextFiles"
              type="file"
              accept=".pdf,.docx,.txt,.jpg,.jpeg,.png"
              multiple // Allow multiple file selection
              onChange={(e) => {
                if (e.target.files) {
                  setContextFiles(prevFiles => [...prevFiles, ...Array.from(e.target.files)]);
                  // Clear the input value to allow selecting the same file again if removed and re-added
                  e.target.value = ""; 
                }
              }}
              className="border rounded-md p-2 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
              disabled={viewMode}
            />
            {contextFiles.length > 0 && (
              <div className="mt-2 space-y-1">
                <p className="text-xs font-medium">Ausgewählte Kontextdateien:</p>
                <ul className="list-disc list-inside pl-4 text-xs">
                  {contextFiles.map((file, index) => (
                    <li key={index} className="flex justify-between items-center">
                      <span>{file.name} ({ (file.size / 1024).toFixed(2) } KB)</span>
                      {!viewMode && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive-foreground hover:bg-destructive/80 h-6 px-2"
                          onClick={() => {
                            setContextFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
                          }}
                        >
                          Entfernen
                        </Button>
                      )}
                    </li>
                  ))}
                </ul>
                {!viewMode && (
                  <Button
                    onClick={handleSummarizeContextFiles}
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    disabled={contextFiles.length === 0 || isUpdating || isSummarizing}
                  >
                    {isSummarizing ? (
                      <Loader2 size={14} className="mr-2 animate-spin" />
                    ) : (
                      <Book size={14} className="mr-2" />
                    )}
                    Inhalte Zusammenfassen
                  </Button>
                )}
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Die Inhalte dieser Dateien werden temporär verarbeitet, um eine Zusammenfassung für den Projektkontext zu erstellen. Die Dateien selbst werden NICHT dauerhaft gespeichert.
            </p>
          </div>
        </div> {/* Closing the NEW WRAPPER */}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Left side: Code editor */}
            <div className="border rounded-md overflow-hidden">
              <div className="bg-card/70 backdrop-blur-sm p-2 border-b font-mono text-sm flex justify-between items-center">
                <div className="flex items-center">
                  <FileCode2 size={14} className="mr-2 text-muted-foreground" />
                  <span>Code Editor</span>
                </div>
                {uploadedFile && (
                  <div className="flex items-center text-xs text-muted-foreground">
                    <FileTypeIndicator 
                      fileExtension={`.${uploadedFile.name.split('.').pop()}` || ''} 
                      isGisCode={false} 
                    />
                    <span className="ml-1 truncate max-w-[100px]">{uploadedFile.name}</span>
                  </div>
                )}
              </div>
              <CodeUploader
                value={code}
                onChange={viewMode ? () => {} : setCode}
                language={document.language || 'python'}
                onLanguageChange={(lang) => {
                  if (!viewMode && document.id === 'new') {
                    setDocument(prev => prev ? {...prev, language: lang} : null);
                  }
                }}
                height="600px"
                readOnly={viewMode}
                onGenerateDocumentation={async () => {
                  try {
                    console.log('Starting documentation generation process');
                    // Prevent any default behavior that might cause navigation
                    // Turn on flags to show loading animation and prevent URL updates
                    setIsUpdating(true);
                    setIsGeneratingDoc(true);
                    
                    // Check if we're editing a new document that hasn't been saved yet
                    const isNewDocument = !document || document.id === 'new' || location.search.includes('new=true');
                    console.log('Document state before saving:', { 
                      isNewDocument, 
                      documentId: document?.id, 
                      locationSearch: location.search 
                    });
                    
                    // First ensure document is saved, wait for save completion
                    const saveSuccess = await saveDocument();
                    if (!saveSuccess) {
                      console.log('Document save failed, aborting documentation generation');
                      toast.error('Document could not be saved. Please try again.');
                      setIsGeneratingDoc(false);
                      setIsUpdating(false);
                      return;
                    }
                    
                    // Get the current document ID from the URL after saving
                    const currentUrlParams = new URLSearchParams(window.location.search);
                    const urlDocId = currentUrlParams.get('id');
                    
                    // Check if we have a valid document ID either from state (if existing doc) or URL (if new doc just saved)
                    const effectiveDocId = (document && document.id !== 'new') ? document.id : urlDocId;
                    
                    if (!effectiveDocId) {
                      console.error('No valid document ID found after save attempt. Aborting generation.');
                      toast.error('Could not determine document ID for generation. Please save the document first.');
                      setIsGeneratingDoc(false);
                      setIsUpdating(false);
                      return;
                    }

                    console.log('Proceeding with documentation generation for document ID:', effectiveDocId);
                    
                    toast.loading('Generating documentation... This may take a moment.', { id: 'gen-doc-process' });

                    let requestData = {
                        code: code, // Use the current editor code instead of possibly stale document state
                        language: document?.language || 'python',
                        provider: 'auto', // Let the backend choose the best provider
                        format: 'json', // Request JSON format for easy parsing
                        documentId: effectiveDocId, // Pass the document ID for reference
                        documentationLanguage: profile?.preferences?.language || 'en' // Use user's preferred language
                      }

                    try {
                      // Make the API call
                      const response = await brain.generate_documentation(requestData);
                      console.log('Got response from documentation API:', response);
                      const data = await response.json();
                      console.log('Documentation generation data:', data);
                      
                      console.log('Processing API response - success:', data.success);
                      if (!data.success) {
                        console.error('API reported failure:', data.error_message || 'Unknown error');
                        toast.error('Failed to generate documentation: ' + (data.error_message || 'Unknown error'));
                        setIsGeneratingDoc(false);
                        setIsUpdating(false);
                        return;
                      }
                      
                      toast.success('Documentation generated successfully', { id: 'doc-success-' + Date.now() });
                      
                      // Update the document with the generated documentation in Firestore
                      try {
                        console.log(`[Firestore Update Debug] Starting update. User UID: ${user.uid}, Document ID: ${effectiveDocId}`);
                        const db = getFirestore(firebaseApp);
                        const docRef = doc(db, COLLECTIONS.DOCUMENTS, effectiveDocId);
                        const contentRef = doc(collection(docRef, 'content'), 'latest');
                        
                        // Prepare documentation data for saving
                        const docString = JSON.stringify(data.documentation);
                        
                        // Ensure description is set in document if missing
                        if (!documentDescription && data.documentation?.textualDescription) {
                          const summary = data.documentation.textualDescription.split('.')[0] + '.';
                          console.log(`[Firestore Update Debug] Attempting to update parent document description. Expected Owner (from state): ${document?.userId}`);
                          setDocumentDescription(summary);
                          await updateDoc(docRef, {
                            description: summary,
                            language_used: data.language_used // Save language used
                          });
                          console.log('[Firestore Update Debug] Parent document description updated.');
                        }
                        
                        // Update document content with generated documentation
                        console.log(`[Firestore Update Debug] Attempting to update content subcollection. Expected Parent Owner (from state): ${document?.userId}`);
                        await updateDoc(contentRef, {
                          generatedDocumentation: docString,
                          parameters: data.documentation?.parameters || null,
                          toolsUsed: data.documentation?.toolsUsed || null,
                          workflow: data.documentation?.workflow || null
                        });
                        console.log('[Firestore Update Debug] Content subcollection updated.');
                        
                        // Update local state directly without page reload - CRITICAL for preventing navigation issues
                        console.log('Updating document state with generated documentation');
                        setDocument((prev) => {
                          if (!prev) {
                            // If somehow we don't have a document state, create a new one based on current data
                            console.log('Creating new document state from remote data');
                            return {
                              id: effectiveDocId,
                              title: documentTitle || 'Untitled Document',
                              description: documentDescription || (data.documentation?.textualDescription ? data.documentation.textualDescription.split('.')[0] + '.' : ''),
                              language: 'python',
                              createdAt: new Date(),
                              updatedAt: new Date(),
                              userId: user.uid,
                              language_used: data.language_used, // Update local state
                              codeSize: code.length,
                              isPublic: false,
                              isDeleted: false,
                              content: {
                                originalCode: code,
                                generatedDocumentation: docString,
                                parameters: data.documentation?.parameters || null,
                                toolsUsed: data.documentation?.toolsUsed || null,
                                workflow: data.documentation?.workflow || null,
                                manualEdits: []
                              }
                            };
                          }
                          
                          const updatedDoc = {
                            ...prev,
                            // Ensure we always use the effective ID
                            id: effectiveDocId, 
                            description: documentDescription || (data.documentation?.textualDescription ? data.documentation.textualDescription.split('.')[0] + '.' : prev.description),
                            language_used: data.language_used, // Update local state
                            content: {
                              ...prev.content,
                              generatedDocumentation: docString,
                              parameters: data.documentation?.parameters || null,
                              toolsUsed: data.documentation?.toolsUsed || null,
                              workflow: data.documentation?.workflow || null
                            }
                          };
                          
                          return updatedDoc;
                        });
                        
                        // Log document state after update to verify correct ID
                        setTimeout(() => {
                          console.log('Document state after update:', { docId: document?.id, effectiveDocId });
                        }, 100);
                        
                        // Switch to documentation tab without triggering navigation
                        console.log('Switching to documentation tab');
                        setActiveTab('documentation');
                        
                        // Update URL with tab parameter without navigation
                        const params = new URLSearchParams(window.location.search);
                        params.set('tab', 'documentation');
                        const newUrl = `${window.location.pathname}?${params.toString()}`;
                        window.history.replaceState(null, '', newUrl);
                        
                        toast.success('Documentation is ready to view', { id: 'doc-view-' + Date.now() });
                      } catch (updateErr) {
                        console.error('Error updating document with generated documentation:', updateErr);
                        toast.error('Documentation was generated but could not be saved to the document');
                      }
                    } catch (err) {
                      console.error('Error generating documentation:', err);
                      toast.error(`Error generating documentation: ${err instanceof Error ? err.message : 'Unknown error'}`);
                    } finally {
                      // Always turn off loading indicators
                      toast.dismiss('gen-doc-process');
                      setIsGeneratingDoc(false);
                      setIsUpdating(false);
                    }
                  } catch (err) {
                    console.error('Unexpected error during documentation generation:', err);
                    toast.error(`Unexpected error: ${err instanceof Error ? err.message : 'Unknown error'}`);
                    setIsGeneratingDoc(false);
                    setIsUpdating(false);
                  }
                }}
                onFileUploaded={(file) => setUploadedFile(file)}
              />
            </div>
            
            {/* Right side: Documentation preview - ensure proper component import and structure with all tabs */}
            <div className="border rounded-md min-h-[600px] h-[calc(100vh-280px)] bg-card/70 backdrop-blur-sm overflow-hidden">
              {document.content.generatedDocumentation ? (
                <div className="flex flex-col h-full w-full">
                  <DocumentationPreview 
                    documentation={processedDocumentation} // Use the memoized object
                    isLoading={isLoading}
                    documentTitle={document.title}
                    createdAt={document.createdAt}
                    updatedAt={document.updatedAt}
                    author={user?.displayName || user?.email || "Unbekannter Autor"}
                    version={document.version || "1.0.0"} // Use dynamic version, fallback to 1.0.0
                    language_used={document.language_used || 'en'} // Pass language_used
                    projectContext={projectContext || null} // Pass projectContext
                    documentDescriptionProp={document.description || null} // Pass the main document description
                  />
                </div>
              ) : (
                <div className="h-full flex flex-col items-center justify-center text-center p-8">
                  <div className="w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mb-4">
                    <FileCode2 size={24} className="text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No documentation generated yet</h3>
                  <p className="text-muted-foreground max-w-md">
                    Upload your code and click the "Generate Documentation" button to create automatic documentation.
                  </p>
                  {uploadedFile && (
                    <div className="mt-4 text-sm p-2 border rounded-md bg-muted/20">
                      <p className="flex items-center gap-2">
                        <span className="font-medium">Uploaded file:</span> 
                        <span className="flex items-center">
                          <FileTypeIndicator 
                            fileExtension={uploadedFile.extension} 
                            isGisCode={false}
                          />
                          <span className="ml-1">{uploadedFile.name}</span>
                        </span>
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="border rounded p-6 bg-muted/30 backdrop-blur-sm">
          <h2 className="text-lg font-bold mb-4">No Document Selected</h2>
          <p className="mb-4">Please select a document from the documents page or create a new one.</p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              variant="default" 
              className="font-mono tracking-wide" 
              onClick={() => {
                // Use a direct approach with relative URLs to avoid path issues
                window.location.href = './editor?new=true';
              }}
            >
              <Plus size={18} className="mr-2" /> Create New Document
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                // Directly use window.location.href for more reliable navigation
                const basePath = window.location.pathname.endsWith('/') ? 
                  window.location.pathname : 
                  window.location.pathname + '/';
                window.location.href = basePath.split('/editor')[0] + '/documents';
              }}
            >
              Go to Documents
            </Button>
            <Button 
              variant="secondary" 
              onClick={() => {
                // Directly use window.location.href for more reliable navigation
                const basePath = window.location.pathname.endsWith('/') ? 
                  window.location.pathname : 
                  window.location.pathname + '/';
                window.location.href = './';
              }}
            >
              <ArrowLeft size={16} className="mr-2" /> Back to Home
            </Button>
          </div>
        </div>
      )}
      
      {/* Loading overlay for documentation generation */}
      <LoadingOverlay 
        isLoading={isGeneratingDoc} 
        message="Generating documentation"
      />
    </div>
  );
}
