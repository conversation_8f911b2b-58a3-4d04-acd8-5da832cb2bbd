import { useState, useEffect } from "react";
import { <PERSON>ertCircle, Terminal } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { FileTypeIndicator } from "./FileTypeIndicator";

interface Props {
  code: string;
  errorLine?: number;
  errorColumn?: number;
  language: string;
  onClose: () => void;
}

export function CodeHighlighter({ code, errorLine, errorColumn, language, onClose }: Props) {
  const [visible, setVisible] = useState(true);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      onClose();
    }, 8000);
    
    return () => clearTimeout(timer);
  }, [onClose]);
  
  if (!visible || !errorLine) return null;
  
  // Get the error line content and the surrounding context
  const lines = code.split('\n');
  const contextStart = Math.max(0, errorLine - 3); // Show 3 lines before
  const contextEnd = Math.min(lines.length - 1, errorLine + 2); // Show 2 lines after
  const errorLineContent = lines[errorLine - 1] || '';
  
  // Create the context for display
  const contextLines = [];
  for (let i = contextStart; i <= contextEnd; i++) {
    contextLines.push({
      number: i + 1,
      content: lines[i] || '',
      isError: i === errorLine - 1,
    });
  }
  
  // Determine if we should mark a specific column
  const highlightColumn = errorColumn && errorColumn > 0;
  
  // Get syntax highlighting class based on language
  const getSyntaxClass = (lang: string) => {
    switch (lang.toLowerCase()) {
      case 'python':
        return 'language-python';
      case 'javascript':
        return 'language-javascript';
      case 'typescript':
        return 'language-typescript';
      case 'r':
        return 'language-r';
      case 'sql':
        return 'language-sql';
      case 'java':
        return 'language-java';
      case 'cpp':
      case 'c++':
        return 'language-cpp';
      case 'c':
        return 'language-c';
      case 'csharp':
      case 'c#':
        return 'language-csharp';
      default:
        return 'language-plaintext';
    }
  };
  
  // Extract the file extension from language for better visualization
  const getFileExtensionFromLanguage = (lang: string): string => {
    switch (lang.toLowerCase()) {
      case 'python': return '.py';
      case 'javascript': return '.js';
      case 'typescript': return '.ts';
      case 'r': return '.r';
      case 'sql': return '.sql';
      case 'java': return '.java';
      case 'cpp': case 'c++': return '.cpp';
      case 'c': return '.c';
      case 'csharp': case 'c#': return '.cs';
      default: return '.txt';
    }
  };

  const fileExtension = getFileExtensionFromLanguage(language);

  return (
    <div 
      className="fixed bottom-4 right-4 z-50 w-[90vw] max-w-[600px] rounded-lg border border-destructive/20 bg-card/95 shadow-lg animate-in fade-in-50 slide-in-from-bottom-5 backdrop-blur-sm"
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cpath d='M15 10L30 0L45 10L45 30L30 40L15 30L15 10Z' fill='none' stroke='%23cccccc' stroke-opacity='0.05' stroke-width='1'/%3E%3C/svg%3E")`,
        backgroundSize: '60px 60px',
      }}
    >
      <div className="p-4 max-h-[50vh] overflow-auto">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 p-1 pl-0">
              <Terminal size={14} className="text-destructive" />
              <h3 className="font-semibold text-sm font-mono tracking-tight">Syntax Error</h3>
            </div>
            <div className="flex items-center gap-1 ml-1">
              <FileTypeIndicator fileExtension={fileExtension} />
              <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20 text-xs">
                <AlertCircle size={10} className="mr-1" />
                Line {errorLine}{errorColumn ? `, Col ${errorColumn}` : ''}
              </Badge>
            </div>
          </div>
          <button
            onClick={onClose}
            className="rounded-full h-6 w-6 inline-flex items-center justify-center text-muted-foreground hover:bg-muted/50 transition-colors"
          >
            &times;
          </button>
        </div>
        
        <div className="mt-2 rounded-md overflow-hidden border border-muted/50 bg-card/50">
          <div className="bg-muted/30 py-1 px-2 border-b border-muted/50 text-xs font-mono flex items-center justify-between">
            <span className="text-muted-foreground">{language.charAt(0).toUpperCase() + language.slice(1)} Syntax Error</span>
            <div className="px-2 py-0.5 bg-destructive/10 text-destructive rounded text-xs font-mono">
              Line {errorLine}{errorColumn ? `, Column ${errorColumn}` : ''}
            </div>
          </div>
          <div className="relative overflow-x-auto">
            <pre className="text-xs font-mono p-0 m-0">
              <code className={getSyntaxClass(language)}>
                {contextLines.map((line) => (
                  <div 
                    key={line.number}
                    className={`flex ${line.isError ? 'bg-destructive/10 relative' : ''}`}
                    data-error-line={line.isError ? 'true' : 'false'}
                  >
                    {/* Line number with enhanced visibility for error line */}
                    <div className={`w-10 flex-shrink-0 text-right select-none pr-2 text-muted-foreground border-r border-muted/30 ${line.isError ? 'bg-destructive/10 font-medium' : 'bg-muted/20'} py-1`}>
                      {line.number}
                    </div>
                    
                    {/* Line content with enhanced visibility */}
                    <div className={`flex-grow py-1 px-2 whitespace-pre overflow-x-auto ${line.isError ? 'font-medium' : ''}`}>
                      {line.content}
                      
                      {/* Enhanced column indicator with animated caret */}
                      {line.isError && highlightColumn && (
                        <>
                          {/* Underline for the error position */}
                          <div 
                            className="absolute bottom-0 left-0 h-[2px] bg-destructive/70"
                            style={{
                              left: `calc(${errorColumn}ch + 0.5rem)`,
                              width: '1ch',
                            }}
                          />
                          {/* Animated caret for better visibility */}
                          <div 
                            className="absolute top-0 left-0 h-full w-[2px] bg-destructive animate-pulse"
                            style={{
                              left: `calc(${errorColumn}ch + 0.5rem)`,
                            }}
                          />
                        </>
                      )}
                    </div>
                    
                    {/* Error indicator with improved design */}
                    {line.isError && (
                      <>
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-destructive" />
                        {/* Add small indicator to right side as well for better visibility */}
                        <div className="absolute right-0 top-0 bottom-0 w-1 bg-destructive/30" />
                      </>
                    )}
                  </div>
                ))}
              </code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
