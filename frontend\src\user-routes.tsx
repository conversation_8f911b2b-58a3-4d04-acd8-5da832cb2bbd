
// THIS FILE IS AUTOGENERATED WHEN PAGES ARE UPDATED
import { lazy } from "react";
import { RouteObject } from "react-router";


import { UserGuard } from "app";



const App = lazy(() => import("./pages/App.tsx"));
const Documents = lazy(() => import("./pages/Documents.tsx"));
const Editor = lazy(() => import("./pages/Editor.tsx"));
const EditorSimplified = lazy(() => import("./pages/EditorSimplified.tsx"));
const Login = lazy(() => import("./pages/Login.tsx"));
const Logout = lazy(() => import("./pages/Logout.tsx"));
const Profile = lazy(() => import("./pages/Profile.tsx"));
const Register = lazy(() => import("./pages/Register.tsx"));

export const userRoutes: RouteObject[] = [



	{ path: "/", element: <App />},
	{ path: "/documents", element: <UserGuard><Documents /></UserGuard>},
	{ path: "/editor", element: <UserGuard><Editor /></UserGuard>},
	{ path: "/editor-simplified", element: <UserGuard><EditorSimplified /></UserGuard>},
	{ path: "/editorsimplified", element: <UserGuard><EditorSimplified /></UserGuard>},
	{ path: "/login", element: <Login />},
	{ path: "/logout", element: <UserGuard><Logout /></UserGuard>},
	{ path: "/profile", element: <UserGuard><Profile /></UserGuard>},
	{ path: "/register", element: <Register />},

];
