from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import re
import traceback
import ast

router = APIRouter(tags=["DEPRECATED - Use /code/validate instead"])

class CodeValidationRequest(BaseModel):
    code: str
    language: str = ""

class CodeValidationResponse(BaseModel):
    is_valid: bool
    detected_language: str
    error_message: str = ""
    error_line_number: int | None = 0
    error_column_number: int | None = 0
    is_gis_code: bool = False

# Supported languages for validation
SUPPORTED_LANGUAGES = {
    "Python", "JavaScript", "TypeScript", "R", "SQL", "Java", "C++", "C#"
}

def detect_language(code: str) -> str:
    """
    Detect the programming language from the code.
    
    Args:
        code (str): The code to analyze
        
    Returns:
        str: The detected language
    """
    # Return "Unknown" for empty code
    if not code or code.strip() == "":
        return "Unknown"
    
    # Check for Python patterns
    python_patterns = [
        r'import\s+[a-zA-Z0-9_]+',
        r'from\s+[a-zA-Z0-9_\.]+\s+import',
        r'def\s+[a-zA-Z0-9_]+\s*\(.*\)\s*:',
        r'class\s+[a-zA-Z0-9_]+.*:',
        r'if\s+__name__\s*==\s*[\'"]__main__[\'"].*:',
        # GIS specific Python imports
        r'import\s+arcpy',
        r'import\s+arcgisscripting',
        r'from\s+qgis\.core',
        r'import\s+ogr',
        r'import\s+gdal',
        r'import\s+geopandas',
        r'import\s+rasterio',
        r'import\s+shapely',
        r'import\s+pyproj',
        r'import\s+fiona',
    ]
    
    # Check for JavaScript/TypeScript patterns
    js_patterns = [
        r'function\s+[a-zA-Z0-9_]+\s*\(.*\)\s*{',
        r'const\s+[a-zA-Z0-9_]+\s*=',
        r'let\s+[a-zA-Z0-9_]+\s*=',
        r'var\s+[a-zA-Z0-9_]+\s*=',
        r'export\s+(?:default\s+)?(?:function|class|const|let|var)',
        r'import\s+.*from\s+[\'"].[a-zA-Z0-9_\/\.]+[\'"]',
        r'document\.(?:getElementById|querySelector)',
        r'console\.(?:log|error|warn|info)',
    ]
    
    # Check for TypeScript-specific patterns
    ts_patterns = [
        r'interface\s+[a-zA-Z0-9_]+\s*{',
        r'type\s+[a-zA-Z0-9_]+\s*=',
        r':\s*[A-Za-z]+<[A-Za-z]+>',
        r'(?:private|public|protected)\s+[a-zA-Z0-9_]+\s*:',
        r'class.*implements',
        r'export\s+(?:interface|type|enum)',
    ]
    
    # Check for R patterns commonly used in GIS analysis
    r_patterns = [
        r'library\(([a-zA-Z0-9\.]+)\)',
        r'<-',
        r'^\s*#\'|roxygen',
        r'library\(sp\)|library\(sf\)|library\(raster\)|library\(rgdal\)',
        r'ggplot\(',
    ]
    
    # Check for SQL patterns
    sql_patterns = [
        r'SELECT\s+.*\s+FROM',
        r'INSERT\s+INTO',
        r'CREATE\s+TABLE',
        r'UPDATE\s+.*\s+SET',
        r'DELETE\s+FROM',
        r'POSTGIS',
        r'ST_[A-Za-z]+\(', # Spatial SQL functions
    ]
    
    # Check for Java patterns
    java_patterns = [
        r'public\s+class\s+',
        r'public\s+(?:static\s+)?void\s+main\s*\(\s*String\s*\[\]\s*args\s*\)',
        r'import\s+java\.',
        r'package\s+[a-z0-9_\.]+;',
    ]
    
    # Check for C/C++ patterns
    cpp_patterns = [
        r'#include\s+[<\"][a-zA-Z0-9_\.]+[>\"]',
        r'int\s+main\s*\(',
        r'void\s+main\s*\(',
        r'using\s+namespace\s+std',
        r'std::[a-zA-Z0-9_]+',
    ]
    
    # Check for C# patterns
    csharp_patterns = [
        r'using\s+System(?:\.[A-Za-z0-9_]+)?;',
        r'namespace\s+[A-Za-z0-9_\.]+',
        r'public\s+(?:sealed\s+)?class',
        r'Console\.Write(?:Line)?',
    ]
    
    # Count pattern matches for each language
    pattern_matches = {
        'Python': sum(1 for pattern in python_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'JavaScript': sum(1 for pattern in js_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'TypeScript': sum(1 for pattern in ts_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'R': sum(1 for pattern in r_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'SQL': sum(1 for pattern in sql_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'Java': sum(1 for pattern in java_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'C++': sum(1 for pattern in cpp_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
        'C#': sum(1 for pattern in csharp_patterns if re.search(pattern, code, re.IGNORECASE | re.MULTILINE)),
    }
    
    # JavaScript and TypeScript share patterns, so if TypeScript patterns are found,
    # add those TypeScript matches to the TypeScript count
    if pattern_matches['TypeScript'] > 0:
        pattern_matches['TypeScript'] += pattern_matches['JavaScript']
        pattern_matches['JavaScript'] = 0
    
    # Find the language with the most matches
    max_matches = max(pattern_matches.values())
    
    if max_matches == 0:
        # No definitive patterns found, try to guess based on specific syntax elements
        if '{' in code and '}' in code:
            return 'JavaScript' # Default for curly brace languages
        elif ':' in code and ('def ' in code or 'class ' in code):
            return 'Python'
        else:
            return 'Unknown'
    
    # Get all languages with the maximum number of matches
    best_matches = [lang for lang, count in pattern_matches.items() if count == max_matches]
    
    if len(best_matches) == 1:
        return best_matches[0]
    
    # If tie between languages, prioritize based on this order
    priority_order = ['Python', 'TypeScript', 'JavaScript', 'R', 'SQL', 'Java', 'C++', 'C#']
    for lang in priority_order:
        if lang in best_matches:
            return lang
    
    return 'Unknown'

def analyze_python_imports(code: str) -> list[str]:
    """
    Analyze Python code to extract imported modules specifically focusing on GIS packages.
    
    Args:
        code (str): The Python code to analyze
        
    Returns:
        list[str]: List of imported modules
    """
    imports = []
    
    try:
        tree = ast.parse(code)
        
        for node in ast.walk(tree):
            # Check for import statements
            if isinstance(node, ast.Import):
                for name in node.names:
                    imports.append(name.name)
            # Check for from ... import statements
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
    except Exception:
        # If parsing fails, return empty list
        pass
    
    return imports

def is_gis_code(code: str) -> bool:
    """
    Determine if the code is GIS-related by checking for common GIS packages, functions, and patterns.
    Enhanced to detect a wider range of GIS code patterns including advanced spatial operations.
    
    Args:
        code (str): The code to analyze
        
    Returns:
        bool: True if the code appears to be GIS-related
    """
    # Common GIS packages and modules with expanded coverage
    gis_packages = [
        # Core GIS packages
        'arcpy', 'arcgisscripting', 'qgis', 'ogr', 'gdal', 'geopandas', 
        'rasterio', 'shapely', 'pyproj', 'fiona', 'cartopy', 'folium', 
        'earthpy', 'pysal', 'geopy', 'osmnx', 'pydeck', 'geoviews',
        'arcgis', 'esri', 'mapbox', 'leaflet', 'openstreetmap', 'geopython',
        
        # Additional specialized GIS packages
        'whitebox', 'richdem', 'rasterstats', 'pcraster', 'xarray-spatial',
        'contextily', 'momepy', 'movingpandas', 'spaghetti', 'libpysal',
        'splot', 'geojson', 'topojson', 'geoalchemy2', 'postgis', 'geomet',
        'osgeo', 'rtree', 'geographiclib', 'geoplot', 'mapclassify',
        'sentinelsat', 'landsat', 'arcgis.features', 'geoip', 'geocoder', 
        'cenpy', 'rio_toa', 'snuggs', 'datashader', 'holoviews.element.geo',
        'scikit-image', 'opencv', 'terrapin', 'landsatxplore', 'spatialpandas'
    ]
    
    # Check imports
    imports = analyze_python_imports(code)
    for imp in imports:
        for gis_pkg in gis_packages:
            if gis_pkg in imp.lower():
                return True
    
    # Enhanced GIS function and pattern detection with specialized categorization
    gis_patterns = [
        # Common GIS data structures and formats
        r'feature_class', r'shapefile', r'geometry', r'spatial', r'raster',
        r'feature(?:_?set|_?layer|_?collection)', r'multi(?:point|line|polygon)', 
        r'topology', r'kriging', r'tin', r'terrain', r'dem', r'dsm', r'dtm',
        r'lidar', r'contour', r'fishnet', r'grid', r'tessellation',
        r'triangulate', r'voronoi', r'thiessen', r'delaunay', r'triangulation',
        
        # Enhanced GIS operations with more specific ArcGIS and QGIS operations
        r'buffer\(', r'intersect(?:ion)?\(', r'union\(', r'clip\(', r'dissolve\(',
        r'spatial_join', r'project\(', r'reproject\(', r'geocode\(', r'locate\(',
        r'interpolate', r'distance\(', r'area\(', r'length\(', r'centroid\(',
        r'overlay', r'extract\(', r'zonal\s*statistics', r'resample', r'regiongroup',
        r'reclassify', r'mosaic', r'sample', r'aspect', r'slope', r'hillshade',
        r'viewshed', r'network', r'routing', r'allocation', r'proximity', r'density',
        r'near(?:_analysis)?', r'watershed', r'flow\s*direction', r'flow\s*accumulation',
        r'rastertopolygon', r'polygontoraster', r'merge\s*?(?:feature|shapefile|raster)',
        r'erase', r'identity', r'symmetrical\s*difference', r'split', r'aggregate\s*polygons',
        r'simplify', r'smooth', r'integrate', r'eliminate', r'repair\s*geometry',
        
        # GIS analysis and spatial statistics
        r'hotspot', r'kernel\s*density', r'point\s*density', r'nearest\s*neighbor',
        r'pattern\s*analysis', r'cluster', r'outlier', r'morans?\s*i', r'getis\s*ord',
        r'spatial\s*autocorrelation', r'directional\s*distribution', r'standard\s*(?:distance|deviational)',
        r'spatial\s*weight', r'IDW', r'inverse\s*distance\s*weighted', r'spline',
        r'natural\s*neighbor', r'trend\s*surface', r'cokriging', r'geographically\s*weighted',
        
        # Enhanced GIS raster operations
        r'focal\s*statistics', r'neighborhood\s*analysis', r'block\s*statistics',
        r'map\s*algebra', r'raster\s*calculator', r'con\s*\(', r'conditional\s*raster',
        r'remap', r'slice', r'contour', r'cut\s*fill', r'raster\s*domain', r'iso\s*cluster',
        r'maximum\s*likelihood', r'principal\s*components', r'band\s*arithmetic', r'ndvi',
        r'normalized\s*difference', r'histogram\s*equalization', r'pan\s*sharpen',
        
        # Coordinates, projections and geographic references
        r'coordinates', r'latitude', r'longitude', r'geotransform', r'affine',
        r'spatial_reference', r'(?:map)?projection', r'epsg\s*[=:]\s*\d{4,5}',
        r'wgs\s*84', r'nad\s*83', r'utm', r'crs', r'srid', r'geographic',
        r'mercator', r'albers', r'datum', r'ellipsoid', r'transform', r'reproject',
        r'well\s*known\s*text', r'well\s*known\s*binary', r'coordinate\s*system',
        
        # Enhanced formats and standards
        r'geojson', r'topojson', r'kml', r'gml', r'wkt', r'wkb', r'geopandas',
        r'shp', r'geotiff', r'netcdf', r'grib', r'hdf', r'geopackage', r'gpkg',
        r'spatialite', r'postgis', r'filegdb', r'geodatabase', r'feature\s*class',
        r'feature\s*dataset', r'coverage', r'mosaic\s*dataset', r'las', r'e00',
        r'erdas', r'mrsid', r'ecw', r'img', r'mbtiles', r'pbf', r'web\s*mercator',
        
        # Services and platforms
        r'osm', r'open\s*street\s*map', r'wmts', r'wms', r'wfs', r'wcs', r'geojson\s*api',
        r'rest\s*api', r'arcgis\s*online', r'qgis\s*server', r'geoserver', r'mapserver',
        r'mapbox\s*gl', r'cesium', r'openlayers', r'leaflet', r'carto', r'maptiler',
        
        # Common GIS-specific function patterns
        r'(?:get|set)(?:spatial|projection|transform|extent|bounds|envelope)',
        r'spatial[A-Z][a-zA-Z]*', r'geo[A-Z][a-zA-Z]*', r'raster[A-Z][a-zA-Z]*',
        r'[gs]et_crs', r'to_crs', r'transform_(?:geom|point|bounds)',
        
        # Function calls specific to GIS libraries
        r'gdal\.', r'ogr\.', r'geopandas\.', r'shapely\.', r'rasterio\.', 
        r'arcpy\.', r'qgis\.', r'pyproj\.', r'fiona\.', r'osmnx\.', r'folium\.', 
        r'cartopy\.', r'matplotlib\.', r'geopy\.', r'osgeo\.', r'rtree\.',
        r'esri\.', r'arcgis\.', r'contextily\.', r'pysal\.', r'cenpy\.'
    ]
    
    # First search with word boundaries for more accurate matching
    for pattern in gis_patterns:
        if re.search(r'\b' + pattern + r'\b', code, re.IGNORECASE):
            return True
    
    # Fallback search without word boundaries for more flexible matching
    for pattern in gis_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return True
    
    # Enhanced check for common GIS function names, variables, or comments
    # This looks at code patterns that might indicate GIS functionality
    gis_keywords = [
        # General GIS terms
        'map', 'layer', 'feature', 'spatial', 'geo', 'gis', 'arcgis', 'qgis',
        'projection', 'coordinate', 'point', 'line', 'polygon', 'raster',
        'buffer', 'intersection', 'clip', 'dissolve', 'join', 'overlay',
        
        # GIS-specific objects and operations
        'shapefile', 'featureclass', 'featurelayer', 'geodatabase', 'geotiff',
        'projection', 'spatialreference', 'spatialindex', 'spatialjoin',
        'rasterband', 'vectorlayer', 'spatialfilter', 'topological',
        
        # Common GIS tools and processes
        'geoprocessing', 'geoenrichment', 'geocoding', 'reverse_geocode',
        'address_match', 'address_locator', 'route_analysis', 'network_analysis',
        'service_area', 'closest_facility', 'origin_destination', 'location_allocation',
        'terrain_analysis', 'watershed_analysis', 'hydrology', 'viewshed',
        'visibility', 'field_calculator', 'attribute_table', 'symbology',
        
        # Contextual words that strongly indicate GIS
        'esri', 'mapinfo', 'grass', 'saga', 'erdas', 'envi', 'arcmap',
        'arcscene', 'arcglobe', 'arcpro', 'qgis', 'geoda', 'postgis', 'mapbox'
    ]
    
    # Enhanced pattern search for variable names and function calls
    var_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
    for match in re.finditer(var_pattern, code):
        var_name = match.group(1).lower()
        for keyword in gis_keywords:
            if keyword in var_name:
                return True
    
    # Check for GIS-specific code comments
    comment_patterns = [
        r'#.*(?:GIS|spatial|geospatial|geographic|mapping|projection)',
        r'\/\/.*(?:GIS|spatial|geospatial|geographic|mapping|projection)',
        r'\/\*.*(?:GIS|spatial|geospatial|geographic|mapping|projection).*\*\/',
        r'""".*(?:GIS|spatial|geospatial|geographic|mapping|projection).*"""'
    ]
    
    for pattern in comment_patterns:
        if re.search(pattern, code, re.DOTALL | re.IGNORECASE):
            return True
    
    # Check for coordinate pairs that might indicate GIS operations
    coord_patterns = [
        r'\[\s*-?\d+\.?\d*\s*,\s*-?\d+\.?\d*\s*\]',  # [lon, lat] format
        r'\(\s*-?\d+\.?\d*\s*,\s*-?\d+\.?\d*\s*\)',  # (lon, lat) format
        r'x\s*=\s*-?\d+\.?\d*\s*,\s*y\s*=\s*-?\d+\.?\d*',  # x=lon, y=lat format
        r'lat\s*=\s*-?\d+\.?\d*\s*,\s*lon\s*=\s*-?\d+\.?\d*',  # lat=val, lon=val format
        r'longitude\s*=\s*-?\d+\.?\d*\s*,\s*latitude\s*=\s*-?\d+\.?\d*'  # longitude=val, latitude=val format
    ]
    
    for pattern in coord_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return True
    
    return False

def validate_python_syntax(code: str) -> tuple[bool, str, int | None, int | None]:
    """
    Perform a basic syntax validation of Python code.
    
    Args:
        code (str): The Python code to validate
        
    Returns:
        tuple: (is_valid, error_message)
            - is_valid (bool): True if the code is valid, False otherwise
            - error_message (str): The error message if the code is invalid, empty string otherwise
    """
    try:
        # Try to compile the code to check for syntax errors
        compile(code, '<string>', 'exec')
        return True, "", None, None
    except SyntaxError as e:
        # Return details about the syntax error with line and column information
        error_msg = f"Syntax error at line {e.lineno}, column {e.offset}: {e.msg}"
        return False, error_msg, e.lineno, e.offset
    except Exception as e:
        # Handle other compilation errors
        tb = traceback.extract_tb(e.__traceback__)
        line_num = None
        col_num = None
        
        if tb:
            # Try to extract line number from traceback
            line_num = tb[-1].lineno
        
        return False, f"Error validating code: {str(e)}", line_num, col_num

def validate_code(code: str, language: str = "") -> tuple[bool, str, str, int | None, int | None, bool]:
    """
    Validate the code and detect its language if not specified.
    
    Args:
        code (str): The code to validate
        language (str, optional): The expected language. If not provided, will be auto-detected.
        
    Returns:
        tuple: (is_valid, error_message, detected_language, line_num, col_num, is_gis_code)
            - is_valid (bool): True if the code is valid, False otherwise
            - error_message (str): The error message if the code is invalid, empty string otherwise
            - detected_language (str): The detected language
            - line_num (int | None): Line number of the error, if any
            - col_num (int | None): Column number of the error, if any
            - is_gis_code (bool): Whether the code appears to be GIS-related
    """
    # If code is empty, return invalid
    if not code or code.strip() == "":
        return False, "Code cannot be empty", "Unknown", 0, 0, False
        
    # Detect language if not specified
    detected_language = language or detect_language(code)
    
    # For non-Python languages, we will currently bypass server-side validation,
    # as a simple bracket check is unreliable and causes false positives.
    # The primary validation will occur in the frontend editor.
    if detected_language != "Python":
        return True, "", detected_language, 0, 0, False

    # Check if code is GIS-related (only for Python)
    is_gis_code_bool = is_gis_code(code)
    is_valid, error_message, line_num, col_num = validate_python_syntax(code)
    return is_valid, error_message, detected_language, line_num, col_num, is_gis_code_bool

# DEPRECATED: Endpunkt wurde zu /code/validate migriert - Funktionen bleiben für Import verfügbar
    """
    Validate the provided code and detect its language.
    
    Args:
        request (CodeValidationRequest): The code to validate and optional language
        
    Returns:
        CodeValidationResponse: The validation result
    """
    # Add debug logging to track validation process
    print(f"\n----- Code Validation Request -----")
    print(f"Language specified: {request.language or 'None (auto-detect)'}")
    print(f"Code length: {len(request.code)} characters")
    # Use string concatenation instead of problematic f-string with backslashes
    first_100_chars = request.code[:100].replace('\n', '[newline]')
    print(f"First 100 chars: {first_100_chars}...")
    
    # Execute validation
    is_valid, error_message, detected_language, line_num, col_num, is_gis_code_bool = validate_code(request.code, request.language)
    
    # Log the validation result
    print(f"\n----- Validation Result -----")
    print(f"Is valid: {is_valid}")
    print(f"Detected language: {detected_language}")
    print(f"Is GIS code: {is_gis_code_bool}")
    if not is_valid:
        print(f"Error message: {error_message}")
        print(f"Error location: Line {line_num}, Column {col_num}")
        
        # If there's an error, print the problematic line with context
        if line_num is not None and line_num > 0:
            lines = request.code.split('\n')
            if line_num <= len(lines):
                problematic_line = lines[line_num-1]
                print(f"\n----- Problematic Line -----")
                print(f"Line {line_num}: {problematic_line}")
                
                # Print surrounding context
                start_line = max(0, line_num-3)
                end_line = min(len(lines), line_num+2)
                print(f"\n----- Code Context -----")
                for i in range(start_line, end_line):
                    prefix = ">>> " if i == line_num-1 else "    "
                    print(f"{prefix}Line {i+1}: {lines[i]}")
    print("----- End of Validation -----\n")
    
    return CodeValidationResponse(
        is_valid=is_valid,
        detected_language=detected_language,
        error_message=error_message,
        error_line_number=line_num,
        error_column_number=col_num,
        is_gis_code=is_gis_code_bool
    )
