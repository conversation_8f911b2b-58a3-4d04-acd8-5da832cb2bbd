/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Tests für Code-Validierung und Syntax-Prüfung
 */

import { TestAssertions, TestRunner, MockUtils } from './test-utils';

// Interface für Code-Validierung
interface CodeValidationRequest {
  code: string;
  language: string;
}

interface CodeValidationResponse {
  is_valid: boolean;
  detected_language: string;
  is_gis_code: boolean;
  error_message?: string;
  error_line_number?: number;
  error_column_number?: number;
}

interface SyntaxErrorDetail {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info' | 'hint';
}

interface LlmValidateCodeSyntaxResponse {
  isValid: boolean;
  errors: SyntaxErrorDetail[];
  rawLLMResponse?: string;
}

// Test-Implementierung der Code-Validierung
class TestCodeValidator {
  
  // Simuliere grundlegende Code-Validierung
  validateCode(request: CodeValidationRequest): CodeValidationResponse {
    const { code, language } = request;
    
    // Basis-Validierung
    if (!code || code.trim().length === 0) {
      return {
        is_valid: false,
        detected_language: 'Unknown',
        is_gis_code: false,
        error_message: 'Code ist leer'
      };
    }

    // Sprach-Erkennung basierend auf Code-Inhalten
    const detectedLanguage = this.detectLanguage(code, language);
    
    // GIS-Code-Erkennung
    const isGisCode = this.detectGisCode(code);
    
    // Syntax-Validierung
    const syntaxValidation = this.validateSyntax(code, detectedLanguage);
    
    return {
      is_valid: syntaxValidation.isValid,
      detected_language: detectedLanguage,
      is_gis_code: isGisCode,
      error_message: syntaxValidation.errorMessage,
      error_line_number: syntaxValidation.errorLine,
      error_column_number: syntaxValidation.errorColumn
    };
  }

  // Simuliere LLM-basierte Syntax-Validierung
  validateCodeWithLLM(request: CodeValidationRequest): LlmValidateCodeSyntaxResponse {
    const { code, language } = request;
    
    if (!code || code.trim().length === 0) {
      return {
        isValid: false,
        errors: [{
          line: 1,
          column: 1,
          message: 'Code ist leer',
          severity: 'error'
        }]
      };
    }

    // Simuliere verschiedene Syntax-Fehler basierend auf Code-Inhalten
    const errors: SyntaxErrorDetail[] = [];
    
    // Python-spezifische Validierung
    if (language.toLowerCase() === 'python') {
      const lines = code.split('\n');
      lines.forEach((line, index) => {
        // Prüfe auf häufige Python-Syntax-Fehler
        if (line.includes('print(') && !line.includes(')')) {
          errors.push({
            line: index + 1,
            column: line.indexOf('print(') + 1,
            message: 'Fehlende schließende Klammer',
            severity: 'error'
          });
        }
        
        if (line.includes('if ') && !line.endsWith(':')) {
          errors.push({
            line: index + 1,
            column: line.length + 1,
            message: 'Fehlender Doppelpunkt nach if-Statement',
            severity: 'error'
          });
        }
      });
    }

    // JavaScript-spezifische Validierung
    if (language.toLowerCase() === 'javascript') {
      const lines = code.split('\n');
      lines.forEach((line, index) => {
        if (line.includes('console.log(') && !line.includes(')')) {
          errors.push({
            line: index + 1,
            column: line.indexOf('console.log(') + 1,
            message: 'Fehlende schließende Klammer',
            severity: 'error'
          });
        }
        
        if (line.trim().length > 0 && !line.includes('//') && !line.endsWith(';') && !line.endsWith('{') && !line.endsWith('}')) {
          errors.push({
            line: index + 1,
            column: line.length + 1,
            message: 'Fehlendes Semikolon',
            severity: 'warning'
          });
        }
      });
    }

    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errors
    };
  }

  private detectLanguage(code: string, providedLanguage: string): string {
    // Einfache Sprach-Erkennung basierend auf Code-Mustern
    if (code.includes('print(') || code.includes('import ') || code.includes('def ')) {
      return 'Python';
    }
    
    if (code.includes('console.log') || code.includes('function ') || code.includes('const ') || code.includes('let ')) {
      return 'JavaScript';
    }
    
    if (code.includes('System.out.println') || code.includes('public class') || code.includes('public static void main')) {
      return 'Java';
    }
    
    // Fallback auf bereitgestellte Sprache
    return providedLanguage || 'Unknown';
  }

  private detectGisCode(code: string): boolean {
    const gisKeywords = [
      'arcpy', 'arcgis', 'qgis', 'gdal', 'ogr', 'geopandas', 'shapely', 'fiona',
      'pyproj', 'rasterio', 'spatial', 'geometry', 'feature', 'shapefile'
    ];
    
    const lowerCode = code.toLowerCase();
    return gisKeywords.some(keyword => lowerCode.includes(keyword));
  }

  private validateSyntax(code: string, language: string): {
    isValid: boolean;
    errorMessage?: string;
    errorLine?: number;
    errorColumn?: number;
  } {
    // Einfache Syntax-Validierung
    const lines = code.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Prüfe auf ungeschlossene Klammern
      const openParens = (line.match(/\(/g) || []).length;
      const closeParens = (line.match(/\)/g) || []).length;
      
      if (openParens > closeParens) {
        return {
          isValid: false,
          errorMessage: 'Ungeschlossene Klammer',
          errorLine: i + 1,
          errorColumn: line.lastIndexOf('(') + 1
        };
      }
    }
    
    return { isValid: true };
  }
}

// Test-Suite für Code-Validierung
export function createCodeValidationTests(): TestRunner {
  const runner = new TestRunner();
  
  runner.addTest('Code-Validierung: Gültiger Python-Code', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'print("Hello World")\nprint("Test")',
      language: 'Python'
    };
    
    const result = validator.validateCode(request);
    
    TestAssertions.assertTrue(result.is_valid, 'Gültiger Python-Code sollte als valid erkannt werden');
    TestAssertions.assertEqual(result.detected_language, 'Python', 'Sprache sollte als Python erkannt werden');
    TestAssertions.assertFalse(result.is_gis_code, 'Normaler Code sollte nicht als GIS-Code erkannt werden');
  });

  runner.addTest('Code-Validierung: Ungültiger Python-Code', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'print("Hello World"',  // Fehlende schließende Klammer
      language: 'Python'
    };
    
    const result = validator.validateCode(request);
    
    TestAssertions.assertFalse(result.is_valid, 'Ungültiger Code sollte als invalid erkannt werden');
    TestAssertions.assertNotNull(result.error_message, 'Fehlermeldung sollte vorhanden sein');
    TestAssertions.assertEqual(result.error_line_number, 1, 'Fehler sollte in Zeile 1 sein');
  });

  runner.addTest('Code-Validierung: Leerer Code', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: '',
      language: 'Python'
    };
    
    const result = validator.validateCode(request);
    
    TestAssertions.assertFalse(result.is_valid, 'Leerer Code sollte als invalid erkannt werden');
    TestAssertions.assertEqual(result.error_message, 'Code ist leer', 'Korrekte Fehlermeldung für leeren Code');
    TestAssertions.assertEqual(result.detected_language, 'Unknown', 'Sprache sollte als Unknown erkannt werden');
  });

  runner.addTest('Code-Validierung: GIS-Code-Erkennung', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'import arcpy\nprint("GIS processing")',
      language: 'Python'
    };
    
    const result = validator.validateCode(request);
    
    TestAssertions.assertTrue(result.is_gis_code, 'Code mit arcpy sollte als GIS-Code erkannt werden');
    TestAssertions.assertEqual(result.detected_language, 'Python', 'Sprache sollte korrekt erkannt werden');
  });

  runner.addTest('Code-Validierung: JavaScript-Erkennung', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'console.log("Hello World");\nconst x = 5;',
      language: 'Unknown'
    };
    
    const result = validator.validateCode(request);
    
    TestAssertions.assertEqual(result.detected_language, 'JavaScript', 'JavaScript sollte automatisch erkannt werden');
    TestAssertions.assertTrue(result.is_valid, 'Gültiger JavaScript-Code sollte valid sein');
  });

  runner.addTest('LLM-Validierung: Python-Syntax-Fehler', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'print("Hello"\nif True\n    print("test")',
      language: 'Python'
    };
    
    const result = validator.validateCodeWithLLM(request);
    
    TestAssertions.assertFalse(result.isValid, 'Code mit Syntax-Fehlern sollte invalid sein');
    TestAssertions.assertTrue(result.errors.length > 0, 'Fehler-Array sollte nicht leer sein');
    
    // Prüfe spezifische Fehler
    const errorMessages = result.errors.map(e => e.message);
    TestAssertions.assertTrue(
      errorMessages.some(msg => msg.includes('Klammer') || msg.includes('Doppelpunkt')),
      'Sollte Klammer- oder Doppelpunkt-Fehler enthalten'
    );
  });

  runner.addTest('LLM-Validierung: JavaScript-Warnungen', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'console.log("Hello")\nconst x = 5',  // Fehlende Semikolons
      language: 'JavaScript'
    };
    
    const result = validator.validateCodeWithLLM(request);
    
    TestAssertions.assertTrue(result.isValid, 'Code sollte trotz Warnungen valid sein');
    TestAssertions.assertTrue(result.errors.length > 0, 'Sollte Warnungen enthalten');
    
    const warnings = result.errors.filter(e => e.severity === 'warning');
    TestAssertions.assertTrue(warnings.length > 0, 'Sollte mindestens eine Warnung enthalten');
  });

  runner.addTest('LLM-Validierung: Gültiger Code', () => {
    const validator = new TestCodeValidator();
    
    const request: CodeValidationRequest = {
      code: 'print("Hello World")\nprint("Test")',
      language: 'Python'
    };
    
    const result = validator.validateCodeWithLLM(request);
    
    TestAssertions.assertTrue(result.isValid, 'Gültiger Code sollte valid sein');
    TestAssertions.assertArrayLength(result.errors, 0, 'Gültiger Code sollte keine Fehler haben');
  });

  return runner;
}
