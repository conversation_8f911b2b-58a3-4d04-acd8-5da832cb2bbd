from app.apis.llm_service import analyze_code_with_llm, ModelType, LLMProvider
from app.apis.documentation_models import (
    DocumentationParameter,
    DocumentationWorkflowStep,
    DocumentationFunction # Assuming this is for tools/functions used
)
import json
import re
from typing import Dict, Any, List, Tuple, Optional

# GIS-specific tool and function patterns to enhance documentation (can be moved to a gis_utils if it grows)
GIS_FUNCTIONS_PATTERNS = [
    # ArcGIS patterns
    r'arcpy\.\w+',
    r'arcpy\.[a-zA-Z]+\.[a-zA-Z]+',
    # QGIS patterns
    r'qgis\.\w+',
    r'QgsProcessing\w+',
    # GeoPandas/Shapely patterns
    r'geopandas\.\w+',
    r'shapely\.\w+',
    # GDAL/OGR patterns
    r'gdal\.\w+',
    r'ogr\.\w+',
    # GIS-related terms
    r'\b(?:buffer|intersect|clip|union|dissolve|overlay)\b'
]

def is_gis_code(code: str) -> bool:
    """Determines if the code is GIS-related by checking for common GIS patterns."""
    return any(re.search(pattern, code) for pattern in GIS_FUNCTIONS_PATTERNS)

async def generate_textual_description(
    code: str,
    provider: LLMProvider,
    retry_on_failure: bool,
    system_prompt: str,
    preferred_language: str,
    is_gis_code_detected: bool,
    project_context: Optional[str] = None
) -> Dict[str, Any]:
    """Generates the textual description section of the documentation."""
    prompt = f"""
Analyze the given code and provide a detailed description of:
1. What the script does (its primary purpose)
2. How it works (key steps or methodology)
3. When it would be used (typical use cases)
4. Any GIS-specific operations or analyses it performs
5. Any spatial data types used (point, line, polygon, raster, etc.)
6. Any relevant performance or scale considerations

For GIS-specific code, be very detailed about:
- The specific GIS operations (buffer, intersect, spatial join, etc.)
- The coordinate systems or projections used (mention EPSG codes if present)
- The spatial data formats processed (shapefiles, feature classes, rasters, GeoJSON, etc.)
- The type of analysis (proximity, overlay, network, terrain, hydrology, etc.)
- Any specialized spatial algorithms or techniques used

If geodatabase operations or workspace management are involved, mention them.
If dealing with raster data, mention resolution, resampling methods, or cell operations.
If using specialized GIS libraries (ArcPy, PyQGIS, GeoPandas, GDAL/OGR), explain their role.

Format your response as a well-structured paragraph. Be specific about operations and data types.
Focus particularly on GIS operations and spatial analysis if present.
Keep your response to 5-7 sentences, making them information-dense and technically precise.
Remember: The entire response MUST be in {preferred_language}.
"""
    # Add project_context to the prompt if it exists
    if project_context:
        prompt += f"\n\nAdditional Project Context to consider:\n{project_context}"

    return await analyze_code_with_llm(
        code=code,
        prompt=prompt,
        provider=provider,
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=system_prompt,
        is_gis_code=is_gis_code_detected,
        project_context=project_context # Pass it down
    )

async def generate_parameters(
    code: str,
    provider: LLMProvider,
    retry_on_failure: bool,
    system_prompt: str,
    preferred_language: str,
    is_gis_code_detected: bool,
    project_context: Optional[str] = None
) -> Tuple[Dict[str, Any], List[DocumentationParameter]]:
    """Generates the parameters section of the documentation."""
    prompt = f"""
Analyze the given code and extract all parameters with special focus on GIS parameters, organized as follows:

IMPORTANT: Your response MUST be a valid JSON object that can be parsed. Structure it EXACTLY as shown in the example format at the end.

1. INPUT PARAMETERS: List all input parameters with these details for each:
   - Name: Parameter name
   - Type: Data type (be very specific about GIS types like Point Feature Class, Polygon Feature Class, Line Feature Class, Raster Dataset, TIN, etc.)
   - Required: Whether it's required or optional (true/false)
   - Description: What the parameter does, including any restrictions
   - Constraints: Any constraints or limitations on the parameter values (e.g., coordinate system requirements, geometry validity requirements)

2. OUTPUT PARAMETERS: List all output parameters with the same details
   For GIS outputs, specify the format precisely (e.g., GeoTIFF raster, File Geodatabase Feature Class, Shapefile)

3. HARDCODED PARAMETERS: Identify any hardcoded values that could be parameters
   Look for coordinate systems (EPSG codes), buffer distances, threshold values, etc.

For GIS code, pay extra attention to:
- Spatial reference parameters (coordinate systems, EPSG codes, projection information)
- Geometric parameters (buffer distances, tolerances, simplification thresholds)
- Raster parameters (cell size, resampling method, mask values, NoData values)
- Analysis parameters (search radius, distance method, interpolation method)
- File paths and geodatabase locations (workspaces, feature datasets, catalog paths)
- Transformation parameters (geographic/datum transformations, method codes)
- Environment settings (extent, cell size, mask, processing extent)
- SQL query parameters, attribute selections, or expression values

Be extra thorough with ArcPy, QGIS, and GDAL parameter detection, checking both function arguments and tool parameters.
For Python-based GIS code, also look for environment variables and configuration dictionaries.

Format your response as a structured JSON object with these exact keys:
{{"input_parameters": [...], "output_parameters": [...], "hardcoded_parameters": [...]}}

Each parameter should have these exact keys: "name", "type", "required", "description", "constraints"
Ensure all textual descriptions ('description' fields) within the JSON are generated in the target language: {preferred_language}. Technical identifiers like 'name' and 'type' should remain untranslated.

Example of proper JSON format (follow this EXACTLY):
```json
{{
  "input_parameters": [
    {{
      "name": "input_shapefile",
      "type": "Point Feature Class",
      "required": true,
      "description": "[Translated description of input point data]",
      "constraints": "EPSG:4326 coordinate system required"
    }}
  ],
  "output_parameters": [
    {{
      "name": "output_raster",
      "type": "GeoTIFF Raster",
      "required": true,
      "description": "[Translated description of analysis result raster]",
      "constraints": "300dpi minimum resolution"
    }}
  ],
  "hardcoded_parameters": [
    {{
      "name": "buffer_distance",
      "type": "float",
      "required": true,
      "description": "Hardcoded: [Translated description of distance value]",
      "constraints": "Set to 100.0 meters in line 42"
    }}
  ]
}}
```

Return ONLY the JSON object with no additional text.
"""
    # Add project_context to the prompt if it exists
    if project_context:
        prompt += f"\n\nAdditional Project Context to consider for parameter extraction:\n{project_context}"

    llm_result = await analyze_code_with_llm(
        code=code,
        prompt=prompt,
        provider=provider,
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=system_prompt,
        is_gis_code=is_gis_code_detected,
        project_context=project_context # Pass it down
    )
    
    extracted_parameters: List[DocumentationParameter] = []
    if llm_result["success"]:
        try:
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', llm_result["result"])
            parameters_text = json_match.group(1) if json_match else llm_result["result"]
            parameters_data = json.loads(parameters_text)
            
            for param_type_key in ["input_parameters", "output_parameters", "hardcoded_parameters"]:
                for param_data in parameters_data.get(param_type_key, []):
                    param = DocumentationParameter(
                        name=param_data.get("name", ""),
                        type=param_data.get("type", "Unknown"),
                        description=param_data.get("description", ""),
                        required=param_data.get("required", True),
                        constraints=param_data.get("constraints", "")
                    )
                    if param_type_key == "hardcoded_parameters" and not param.description.startswith("Hardcoded:"):
                        param.description = "Hardcoded: " + param.description
                    extracted_parameters.append(param)
        except Exception as e:
            print(f"Error processing parameters from LLM: {str(e)}")
            # Log the error or return a specific error indicator if needed
            llm_result["success"] = False # Mark as failed if parsing fails
            llm_result["error"] = f"JSON parsing error for parameters: {str(e)}"
            if not extracted_parameters: # Add a placeholder error if list is empty
                 extracted_parameters.append(DocumentationParameter(
                    name="error_parsing_parameters",
                    type="Error",
                    description=f"Error parsing parameters JSON: {str(e)}",
                    required=False,
                    constraints="Raw LLM response was malformed or unparsable."
                ))

    return llm_result, extracted_parameters

async def generate_workflow_steps(
    code: str,
    provider: LLMProvider,
    retry_on_failure: bool,
    system_prompt: str,
    preferred_language: str,
    is_gis_code_detected: bool,
    project_context: Optional[str] = None
) -> Tuple[Dict[str, Any], List[DocumentationWorkflowStep]]:
    """Generates the workflow steps section of the documentation."""
    prompt = f"""
Analyze the given code and extract a detailed workflow that explains the code execution as a series of steps, with special focus on GIS operations.

IMPORTANT: Your response MUST be a valid JSON object that can be parsed. Structure it EXACTLY as shown in the example format at the end.

For each workflow step, include:
1. Step number as "id" (int)
2. Name: A short title for the step that clearly indicates what GIS operation or data processing is happening
3. Description: Detailed explanation of what happens in this step, including:
   - What specific GIS processes or operations are applied
   - What data transformations occur
   - What spatial algorithms are used
   - Any conditionals or special cases handled

For GIS workflows, be sure to identify these specific phases:
- Data loading/import phase (reading from files, geodatabases, web services)
- Data validation phase (checking geometry validity, attribute completeness)
- Data pre-processing/cleaning phase (repairing geometry, handling missing values)
- Coordinate system definition or transformation phase (reprojection operations)
- Core spatial analysis operations (geoprocessing tools, spatial operations)
- Attribute processing (field calculations, statistics, classification)
- Post-processing of results (generalization, simplification, symbol assignment)
- Output generation/export phase (writing to files, geodatabases, visualization)

Pay special attention to iterations and batch processing in GIS workflows.
Identify any error handling or quality control measures specific to spatial data.
Note any performance optimization techniques used for large spatial datasets.
Highlight dependencies between steps and data flow through the workflow.

Make sure to capture the logical flow of the program including conditionals, loops, and function calls.
For GIS code, focus on the spatial operations and transformations.

Format your response as a structured JSON object with this format:
{{"steps": [{{"id": 1, "name": "...", "description": "..."}}]}}

Ensure all textual descriptions ('description' fields) within the JSON are generated in the target language: {preferred_language}. Technical identifiers like 'id' and 'name' should remain untranslated.
Include all significant steps in the workflow, typically 4-8 steps for most GIS scripts.

Example of proper JSON format (follow this EXACTLY):
```json
{{
  "steps": [
    {{
      "id": 1,
      "name": "Data Import",
      "description": "[Translated description of importing point data]"
    }},
    {{
      "id": 2,
      "name": "Coordinate System Transformation",
      "description": "[Translated description of reprojection]"
    }}
  ]
}}
```

Return ONLY the JSON object with no additional text.
"""
    # Add project_context to the prompt if it exists
    if project_context:
        prompt += f"\n\nAdditional Project Context to consider for workflow extraction:\n{project_context}"

    llm_result = await analyze_code_with_llm(
        code=code,
        prompt=prompt,
        provider=provider,
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=system_prompt,
        is_gis_code=is_gis_code_detected,
        project_context=project_context # Pass it down
    )

    extracted_steps: List[DocumentationWorkflowStep] = []
    if llm_result["success"]:
        try:
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', llm_result["result"])
            workflow_text = json_match.group(1) if json_match else llm_result["result"]
            workflow_data = json.loads(workflow_text)
            
            for step_data in workflow_data.get("steps", []):
                extracted_steps.append(DocumentationWorkflowStep(
                    id=step_data.get("id", 0),
                    name=step_data.get("name", ""),
                    description=step_data.get("description", "")
                ))
        except Exception as e:
            print(f"Error processing workflow steps from LLM: {str(e)}")
            llm_result["success"] = False
            llm_result["error"] = f"JSON parsing error for workflow: {str(e)}"
            if not extracted_steps:
                extracted_steps.append(DocumentationWorkflowStep(
                    id=0, # Or another suitable default/error ID
                    name="Error Parsing Workflow",
                    description=f"Error parsing workflow JSON: {str(e)}"
                ))
    return llm_result, extracted_steps

async def generate_tools_used(
    code: str,
    provider: LLMProvider,
    retry_on_failure: bool,
    system_prompt: str,
    preferred_language: str, # Though not used in prompt, kept for consistency
    is_gis_code_detected: bool,
    project_context: Optional[str] = None
) -> Tuple[Dict[str, Any], List[str]]: # Assuming List[str] for tools
    """Generates the tools used section of the documentation."""
    prompt = f"""
Analyze the given code and extract information about all tools, functions, and methods used, with special focus on GIS-specific tools.

IMPORTANT: Your response MUST be a valid JSON array that can be parsed. Structure it EXACTLY as shown in the example format at the end.

List all function names, method calls, and GIS tools used in the code.
For GIS code, identify:
- Core GIS library functions (e.g., arcpy.Buffer_analysis, QgsProcessingAlgorithm, ogr.Geometry, geopandas.overlay)
- Spatial operation functions (e.g., intersect, dissolve, buffer, union, clip, erase)
- Raster analysis functions (e.g., focal statistics, map algebra, reclassify, hillshade, aspect, slope)
- Coordinate transformation functions (e.g., project, transform_geometries, reproject)
- Geodatabase operations (e.g., create_feature_class, add_field, create_relationship)
- Spatial data I/O functions (e.g., read_file, to_file, ExportToWkt, Open, GetLayer)
- Map generation or visualization functions (e.g., add_layer, symbolize, export_map)
- Spatial statistics functions (e.g., nearest_neighbor, hotspot, cluster)
- Terrain analysis functions (e.g., visibility, watershed, flow direction)
- Network analysis functions (e.g., shortest_path, service_area)
- Geoprocessing models or workflows (e.g., ModelBuilder tools, QGIS Processing algorithms)

For ArcPy specifically, extract toolbox and tool name (e.g., "arcpy.analysis.Buffer", "arcpy.sa.Slope").
For QGIS, note both core functions and Processing algorithms.
For GDAL/OGR, note driver types and transformation options.
For GeoPandas/Shapely, note both the method and the geometric operation.

Categorize tools where possible as:
- Data Management functions
- Spatial Analysis functions
- Geometric operations
- Raster operations
- Conversion tools
- Visualization functions
- 3D Analysis functions
- Network Analysis functions
- Spatial Statistics functions

Format your response as a structured JSON array of strings:
["function_name1", "function_name2", ...]

Focus on the most important functions and tools, excluding basic language constructs.
Sort by importance, with the most significant GIS operations first, limited to the top 20 most important tools.
If any descriptive text were needed within the JSON structure (though not requested here), it MUST be in {preferred_language}.

Example of proper JSON format (follow this EXACTLY):
```json
[
  "arcpy.Buffer_analysis",
  "geopandas.overlay",
  "QgsVectorLayer",
  "ogr.Open",
  "spatial_join"
]
```

Return ONLY the JSON array with no additional text.
Remember: Descriptions MUST be in {preferred_language} if they were requested (though they are not here).
"""
    # Add project_context to the prompt if it exists
    if project_context:
        prompt += f"\n\nAdditional Project Context to consider for tool extraction:\n{project_context}"

    llm_result = await analyze_code_with_llm(
        code=code,
        prompt=prompt,
        provider=provider,
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=system_prompt,
        is_gis_code=is_gis_code_detected,
        project_context=project_context # Pass it down
    )

    extracted_tools: List[str] = []
    if llm_result["success"]:
        try:
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', llm_result["result"])
            tools_text = json_match.group(1) if json_match else llm_result["result"]
            extracted_tools = json.loads(tools_text)
        except Exception as e:
            print(f"Error processing tools used from LLM: {str(e)}")
            llm_result["success"] = False
            llm_result["error"] = f"JSON parsing error for tools: {str(e)}"
            if not extracted_tools: # Add a placeholder error if list is empty
                extracted_tools.append("Error parsing tools - see backend logs for details")
    
    return llm_result, extracted_tools
