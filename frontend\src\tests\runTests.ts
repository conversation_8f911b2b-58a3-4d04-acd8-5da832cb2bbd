/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Haupt-Test-Runner für alle Test-Suites
 */

import { TestRunner } from './test-utils';
import { createDocumentStoreTests } from './documentStore.test';
import { createCodeValidationTests } from './codeValidation.test';
import { createFileUploadTests } from './fileUpload.test';

// Haupt-Test-Runner-Klasse
class MainTestRunner {
  private testSuites: Array<{ name: string; runner: TestRunner }> = [];
  private overallResults: Array<{ suiteName: string; passed: number; failed: number; coverage: number }> = [];

  constructor() {
    this.initializeTestSuites();
  }

  private initializeTestSuites(): void {
    // Registriere alle Test-Suites
    this.testSuites = [
      {
        name: 'DocumentStore Tests',
        runner: createDocumentStoreTests()
      },
      {
        name: 'Code-Validierung Tests',
        runner: createCodeValidationTests()
      },
      {
        name: 'File-Upload Tests',
        runner: createFileUploadTests()
      }
    ];
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 CODESCRIBE GIS - TEST-SUITE GESTARTET');
    console.log('=====================================\n');

    const startTime = performance.now();

    for (const suite of this.testSuites) {
      console.log(`📋 ${suite.name}`);
      console.log('─'.repeat(50));
      
      try {
        await suite.runner.runAll();
        
        const results = suite.runner.getResults();
        const passed = results.filter(r => r.passed).length;
        const failed = results.filter(r => !r.passed).length;
        const coverage = suite.runner.getCoverage();
        
        this.overallResults.push({
          suiteName: suite.name,
          passed,
          failed,
          coverage
        });
        
      } catch (error) {
        console.log(`❌ Fehler beim Ausführen der Test-Suite: ${error.message}`);
        this.overallResults.push({
          suiteName: suite.name,
          passed: 0,
          failed: 1,
          coverage: 0
        });
      }
      
      console.log(''); // Leerzeile zwischen Test-Suites
    }

    const endTime = performance.now();
    this.printOverallSummary(endTime - startTime);
  }

  private printOverallSummary(totalDuration: number): void {
    console.log('🎯 GESAMTERGEBNIS');
    console.log('=====================================');

    let totalPassed = 0;
    let totalFailed = 0;
    let totalTests = 0;

    // Detaillierte Ergebnisse pro Test-Suite
    this.overallResults.forEach(result => {
      const total = result.passed + result.failed;
      totalPassed += result.passed;
      totalFailed += result.failed;
      totalTests += total;

      const status = result.failed === 0 ? '✅' : '❌';
      const coverageColor = result.coverage >= 70 ? '🟢' : result.coverage >= 50 ? '🟡' : '🔴';
      
      console.log(`${status} ${result.suiteName}:`);
      console.log(`   Bestanden: ${result.passed}/${total}`);
      console.log(`   Coverage: ${coverageColor} ${result.coverage.toFixed(1)}%`);
    });

    console.log('─'.repeat(50));

    // Gesamt-Statistiken
    const overallSuccessRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;
    const averageCoverage = this.overallResults.length > 0 
      ? this.overallResults.reduce((sum, r) => sum + r.coverage, 0) / this.overallResults.length 
      : 0;

    console.log(`📊 Gesamt-Statistiken:`);
    console.log(`   Tests bestanden: ${totalPassed}/${totalTests}`);
    console.log(`   Erfolgsrate: ${overallSuccessRate.toFixed(1)}%`);
    console.log(`   Durchschnittliche Coverage: ${averageCoverage.toFixed(1)}%`);
    console.log(`   Gesamtdauer: ${totalDuration.toFixed(2)}ms`);

    // Bewertung nach Regel 14 (70% Coverage-Ziel)
    console.log('\n🎯 REGEL 14 COMPLIANCE:');
    if (averageCoverage >= 70) {
      console.log('✅ Test Coverage Ziel erreicht (≥70%)');
    } else {
      console.log(`❌ Test Coverage Ziel nicht erreicht (${averageCoverage.toFixed(1)}% < 70%)`);
    }

    if (overallSuccessRate >= 90) {
      console.log('✅ Hohe Test-Qualität (≥90% Erfolgsrate)');
    } else if (overallSuccessRate >= 70) {
      console.log('🟡 Akzeptable Test-Qualität (≥70% Erfolgsrate)');
    } else {
      console.log('❌ Niedrige Test-Qualität (<70% Erfolgsrate)');
    }

    // Empfehlungen
    console.log('\n💡 EMPFEHLUNGEN:');
    
    if (averageCoverage < 70) {
      console.log('- Weitere Tests für kritische Funktionen hinzufügen');
      console.log('- Edge Cases und Fehlerfälle testen');
    }
    
    if (totalFailed > 0) {
      console.log('- Fehlgeschlagene Tests analysieren und beheben');
      console.log('- Code-Qualität in betroffenen Bereichen verbessern');
    }
    
    if (overallSuccessRate >= 90 && averageCoverage >= 70) {
      console.log('- Ausgezeichnete Test-Qualität! 🎉');
      console.log('- Regel 14 vollständig erfüllt');
    }
  }

  // Methode für CI/CD Integration
  getExitCode(): number {
    const totalFailed = this.overallResults.reduce((sum, r) => sum + r.failed, 0);
    const averageCoverage = this.overallResults.length > 0 
      ? this.overallResults.reduce((sum, r) => sum + r.coverage, 0) / this.overallResults.length 
      : 0;

    // Exit Code 0 = Erfolg, 1 = Fehler
    return totalFailed === 0 && averageCoverage >= 70 ? 0 : 1;
  }

  // Methode für detaillierte Berichte
  generateDetailedReport(): string {
    const report = [];
    
    report.push('# CodeScribe GIS - Test Report');
    report.push(`Generiert am: ${new Date().toLocaleString('de-DE')}`);
    report.push('');
    
    this.overallResults.forEach(result => {
      report.push(`## ${result.suiteName}`);
      report.push(`- Tests bestanden: ${result.passed}`);
      report.push(`- Tests fehlgeschlagen: ${result.failed}`);
      report.push(`- Coverage: ${result.coverage.toFixed(1)}%`);
      report.push('');
    });

    const totalPassed = this.overallResults.reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = this.overallResults.reduce((sum, r) => sum + r.failed, 0);
    const averageCoverage = this.overallResults.length > 0 
      ? this.overallResults.reduce((sum, r) => sum + r.coverage, 0) / this.overallResults.length 
      : 0;

    report.push('## Zusammenfassung');
    report.push(`- Gesamt Tests: ${totalPassed + totalFailed}`);
    report.push(`- Erfolgsrate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
    report.push(`- Durchschnittliche Coverage: ${averageCoverage.toFixed(1)}%`);
    report.push(`- Regel 14 erfüllt: ${averageCoverage >= 70 ? 'Ja' : 'Nein'}`);

    return report.join('\n');
  }
}

// Haupt-Funktion zum Ausführen aller Tests
export async function runAllTests(): Promise<number> {
  const runner = new MainTestRunner();
  
  try {
    await runner.runAllTests();
    return runner.getExitCode();
  } catch (error) {
    console.error('❌ Kritischer Fehler beim Ausführen der Tests:', error);
    return 1;
  }
}

// Für direkte Ausführung in Node.js/Browser
if (typeof window !== 'undefined') {
  // Browser-Umgebung
  (window as any).runCodeScribeTests = runAllTests;
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js-Umgebung
  module.exports = { runAllTests };
}

// Automatische Ausführung wenn direkt aufgerufen
if (typeof window !== 'undefined' && window.location.search.includes('runTests=true')) {
  runAllTests().then(exitCode => {
    console.log(`Tests beendet mit Exit Code: ${exitCode}`);
  });
}
