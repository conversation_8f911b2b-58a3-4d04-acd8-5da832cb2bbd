import {
  BodySummarizeUploadedFiles,
  CheckHealthData,
  CodeValidationRequest,
  DeleteExportData,
  DeleteExportError,
  DeleteExportParams,
  DocumentExportRequest,
  DocumentationRequest,
  DownloadDocumentData,
  DownloadDocumentError,
  DownloadDocumentParams,
  DownloadFromStorageData,
  DownloadFromStorageError,
  DownloadFromStorageParams,
  ExportDocumentData,
  ExportDocumentError,
  GenerateDocumentationData,
  GenerateDocumentationError,
  GetExportStatusData,
  GetExportStatusError,
  GetExportStatusParams,
  LlmValidateCodeSyntaxEndpointData,
  LlmValidateCodeSyntaxEndpointError,
  LlmValidateCodeSyntaxRequest,
  RunTestEndpointData,
  SummarizeUploadedFilesData,
  SummarizeUploadedFilesError,
  TestingDocumentationLanguageData,
  TestingDocumentationLanguageError,
  ValidateCodeEndpoint2Data,
  ValidateCodeEndpoint2Error,
  ValidateCodeEndpoint2Params,
  ValidateCodeEndpointData,
  ValidateCodeEndpointError,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Brain<SecurityDataType = unknown> extends HttpClient<SecurityDataType> {
  /**
   * @description Check health of application. Returns 200 when OK, 500 when not.
   *
   * @name check_health
   * @summary Check Health
   * @request GET:/_healthz
   */
  check_health = (params: RequestParams = {}) =>
    this.request<CheckHealthData, any>({
      path: `/_healthz`,
      method: "GET",
      ...params,
    });

  /**
   * @description Test endpoint for documentation generation
   *
   * @tags dbtn/module:testing, dbtn/hasAuth
   * @name run_test_endpoint
   * @summary Run Test Endpoint
   * @request GET:/routes/test
   */
  run_test_endpoint = (params: RequestParams = {}) =>
    this.request<RunTestEndpointData, any>({
      path: `/routes/test`,
      method: "GET",
      ...params,
    });

  /**
   * @description Test endpoint for documentation language settings. Returns the language that would be used for documentation.
   *
   * @tags dbtn/module:testing, dbtn/hasAuth
   * @name testing_documentation_language
   * @summary Testing Documentation Language
   * @request POST:/routes/test_language
   */
  testing_documentation_language = (data: DocumentationRequest, params: RequestParams = {}) =>
    this.request<TestingDocumentationLanguageData, TestingDocumentationLanguageError>({
      path: `/routes/test_language`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });

  /**
   * @description Test endpoint for documentation generation
   *
   * @tags dbtn/module:test_utils, dbtn/hasAuth
   * @name validate_code_endpoint2
   * @summary Validate Code Endpoint2
   * @request GET:/routes/test-documentation
   */
  validate_code_endpoint2 = (query: ValidateCodeEndpoint2Params, params: RequestParams = {}) =>
    this.request<ValidateCodeEndpoint2Data, ValidateCodeEndpoint2Error>({
      path: `/routes/test-documentation`,
      method: "GET",
      query: query,
      ...params,
    });

  /**
   * @description Download a file directly from storage using its key.
   *
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name download_from_storage
   * @summary Download From Storage
   * @request GET:/routes/export/download/{storage_key}
   */
  download_from_storage = ({ storageKey, ...query }: DownloadFromStorageParams, params: RequestParams = {}) =>
    this.request<DownloadFromStorageData, DownloadFromStorageError>({
      path: `/routes/export/download/${storageKey}`,
      method: "GET",
      ...params,
    });

  /**
   * @description Export a document in the specified format, using cache if available.
   *
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name export_document
   * @summary Export Document
   * @request POST:/routes/export/document
   */
  export_document = (data: DocumentExportRequest, params: RequestParams = {}) =>
    this.request<ExportDocumentData, ExportDocumentError>({
      path: `/routes/export/document`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });

  /**
   * @description Get the status of an export task.
   *
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name get_export_status
   * @summary Get Export Status
   * @request GET:/routes/export/document/{export_id}/status
   */
  get_export_status = ({ exportId, ...query }: GetExportStatusParams, params: RequestParams = {}) =>
    this.request<GetExportStatusData, GetExportStatusError>({
      path: `/routes/export/document/${exportId}/status`,
      method: "GET",
      ...params,
    });

  /**
   * @description Download a previously generated document.
   *
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name download_document
   * @summary Download Document
   * @request GET:/routes/export/document/{export_id}/download
   */
  download_document = ({ exportId, ...query }: DownloadDocumentParams, params: RequestParams = {}) =>
    this.request<DownloadDocumentData, DownloadDocumentError>({
      path: `/routes/export/document/${exportId}/download`,
      method: "GET",
      ...params,
    });

  /**
   * @description Delete an export task and its associated document.
   *
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name delete_export
   * @summary Delete Export
   * @request DELETE:/routes/export/document/{export_id}
   */
  delete_export = ({ exportId, ...query }: DeleteExportParams, params: RequestParams = {}) =>
    this.request<DeleteExportData, DeleteExportError>({
      path: `/routes/export/document/${exportId}`,
      method: "DELETE",
      ...params,
    });

  /**
   * No description
   *
   * @tags Context Processor, dbtn/module:context_processor, dbtn/hasAuth
   * @name summarize_uploaded_files
   * @summary Summarize Uploaded Files
   * @request POST:/routes/context-processor/summarize-uploaded-files
   */
  summarize_uploaded_files = (data: BodySummarizeUploadedFiles, params: RequestParams = {}) =>
    this.request<SummarizeUploadedFilesData, SummarizeUploadedFilesError>({
      path: `/routes/context-processor/summarize-uploaded-files`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      ...params,
    });

  /**
   * No description
   *
   * @tags dbtn/module:llm_validator, dbtn/hasAuth
   * @name llm_validate_code_syntax_endpoint
   * @summary Llm Validate Code Syntax Endpoint
   * @request POST:/routes/llm_validate_code_syntax
   */
  llm_validate_code_syntax_endpoint = (data: LlmValidateCodeSyntaxRequest, params: RequestParams = {}) =>
    this.request<LlmValidateCodeSyntaxEndpointData, LlmValidateCodeSyntaxEndpointError>({
      path: `/routes/llm_validate_code_syntax`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });

  /**
   * @description Generate documentation for code. This endpoint generates detailed documentation for the provided code, leveraging LLM services like OpenAI's GPT models or DeepSeek's coding models. The documentation will include: - Textual description of what the code does - Parameters (input, output, and hardcoded) - Tools and functions used in the code - Workflow steps showing the execution flow Args: request (DocumentationRequest): The request containing: - code: The source code to document - language: Programming language (default: Python) - provider: LLM provider to use ("openai", "deepseek", or "auto") - format: Output format (default: "markdown") - documentationLanguage: Language for generated docs (en, de, etc.) - documentation_id: Optional ID of existing doc to fetch context for Returns: DocumentationGenerationResponse: The generated documentation or error details
   *
   * @tags dbtn/module:documentation_generator, dbtn/hasAuth
   * @name generate_documentation
   * @summary Generate Documentation
   * @request POST:/routes/documentation/generate
   */
  generate_documentation = (data: DocumentationRequest, params: RequestParams = {}) =>
    this.request<GenerateDocumentationData, GenerateDocumentationError>({
      path: `/routes/documentation/generate`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });

  /**
   * @description Validate the provided code and detect its language. Args: request (CodeValidationRequest): The code to validate and optional language Returns: CodeValidationResponse: The validation result
   *
   * @tags dbtn/module:code_validation, dbtn/hasAuth
   * @name validate_code_endpoint
   * @summary Validate Code Endpoint
   * @request POST:/routes/validate
   */
  validate_code_endpoint = (data: CodeValidationRequest, params: RequestParams = {}) =>
    this.request<ValidateCodeEndpointData, ValidateCodeEndpointError>({
      path: `/routes/validate`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });
}
