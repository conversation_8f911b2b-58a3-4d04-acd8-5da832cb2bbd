"""
Authentication module for CodeScribe Backend
Provides Firebase-based authentication for API endpoints
"""

from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from pydantic import BaseModel
import os
import json
import firebase_admin
from firebase_admin import auth as firebase_auth, credentials


class User(BaseModel):
    """User model representing an authenticated user"""
    sub: str  # Firebase UID
    email: Optional[str] = None
    name: Optional[str] = None
    picture: Optional[str] = None


def get_authorized_user() -> User:
    """
    Dependency function to get the current authenticated user.
    For now, returns a mock user since we're transitioning from Databutton.
    In production, this should validate Firebase tokens.
    """
    # TODO: Implement proper Firebase token validation
    # For now, return a mock user to maintain functionality
    return User(
        sub="mock_user_id",
        email="<EMAIL>",
        name="Mock User"
    )


# Export the user type and dependency
from .user import AuthorizedUser

__all__ = ["User", "get_authorized_user", "AuthorizedUser"]
