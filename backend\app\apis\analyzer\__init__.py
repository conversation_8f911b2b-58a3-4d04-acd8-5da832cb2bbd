from fastapi import APIRouter
import ast
import re
from typing import List, Dict, Optional, Any, Tuple

# Create empty router for compatibility
router = APIRouter()

from app.apis.models import Parameter, Function, Library, Workflow
from app.apis.gis_utils import GIS_LIBRARIES, is_gis_parameter, identify_gis_library, identify_gis_function

def extract_docstring(node: ast.AST) -> str:
    """Extract the docstring from an AST node"""
    if not hasattr(node, 'body') or not node.body:
        return ""
    
    # Check for docstring in the first statement of the body
    first = node.body[0]
    if isinstance(first, ast.Expr) and isinstance(first.value, ast.Str):
        return first.value.s.strip()
    
    return ""

def extract_description_from_docstring(docstring: str) -> str:
    """Extract the main description from a docstring"""
    if not docstring:
        return ""
    
    # Take the first paragraph of the docstring as the description
    lines = docstring.split('\n\n', 1)
    return lines[0].strip()

def extract_parameter_info(docstring: str, arg_names: List[str], defaults: Optional[List[Any]] = None) -> Dict[str, Dict[str, str]]:
    """Extract parameter descriptions and types from a docstring"""
    if not docstring:
        return {}
    
    param_info = {}
    for arg in arg_names:
        param_info[arg] = {
            'description': '',
            'type': 'Unknown',
            'required': True,
            'restrictions': None
        }
    
    # Set defaults for optional parameters
    if defaults:
        for i, default in enumerate(defaults):
            idx = len(arg_names) - len(defaults) + i
            if idx < len(arg_names):
                arg = arg_names[idx]
                param_info[arg]['required'] = False
                try:
                    param_info[arg]['default_value'] = repr(default)
                except Exception:
                    param_info[arg]['default_value'] = 'Default value provided'
    
    # Regex patterns for different docstring styles
    # Google style
    google_pattern = r'(?:Args|Parameters):\s*([\s\S]*?)(?:(?:Returns|Yields|Raises|Examples|Note|Notes|Attributes|See Also|References):|$)'
    google_param_pattern = r'([\w_]+)\s*(?:\(([^)]+)\))?\s*:\s*(.*)'
    
    # NumPy style
    numpy_pattern = r'Parameters\s*\n\s*[-]+\s*\n([\s\S]*?)(?:(?:Returns|Yields|Raises|Examples|Note|Notes|Attributes|See Also|References)\s*\n\s*[-]+|$)'
    numpy_param_pattern = r'([\w_]+)\s*:\s*([^\n]*)(?:\n\s+(.*))?'
    
    # Try to match Google style first
    google_match = re.search(google_pattern, docstring, re.MULTILINE)
    if google_match:
        param_block = google_match.group(1)
        param_descriptions = [p.strip() for p in param_block.split('\n') if p.strip()]
        
        current_param = None
        current_desc = []
        
        for line in param_descriptions:
            param_match = re.match(google_param_pattern, line)
            if param_match:
                # If we were working on a previous parameter, save its description
                if current_param and current_param in param_info:
                    param_info[current_param]['description'] = ' '.join(current_desc).strip()
                    current_desc = []
                
                # Get the new parameter info
                param_name = param_match.group(1)
                param_type = param_match.group(2) if param_match.group(2) else 'Unknown'
                param_desc = param_match.group(3) if param_match.group(3) else ''
                
                if param_name in param_info:
                    param_info[param_name]['description'] = param_desc.strip()
                    param_info[param_name]['type'] = param_type.strip()
                    
                current_param = param_name
                if param_desc:
                    current_desc = [param_desc]
            elif current_param and line.strip() and current_param in param_info:
                # Continue the description of the current parameter
                current_desc.append(line.strip())
        
        # Handle the last parameter
        if current_param and current_param in param_info and current_desc:
            param_info[current_param]['description'] = ' '.join(current_desc).strip()
    
    # If Google style doesn't match, try NumPy style
    else:
        numpy_match = re.search(numpy_pattern, docstring, re.MULTILINE)
        if numpy_match:
            param_block = numpy_match.group(1)
            params = re.findall(numpy_param_pattern, param_block, re.MULTILINE)
            
            for param_name, param_type, param_desc in params:
                if param_name in param_info:
                    param_info[param_name]['description'] = param_desc.strip()
                    param_info[param_name]['type'] = param_type.strip()
    
    # Look for type hints in the comments for any untyped parameters
    for arg in arg_names:
        if arg in param_info and param_info[arg]['type'] == "Unknown":
            type_hint_pattern = r'@type\s+' + re.escape(arg) + r'\s*:\s*([^\n]+)'
            type_hint_match = re.search(type_hint_pattern, docstring)
            if type_hint_match:
                param_info[arg]['type'] = type_hint_match.group(1).strip()
    
    # Look for parameter restrictions
    for arg in arg_names:
        if arg in param_info:
            restriction_patterns = [
                r'(?:' + re.escape(arg) + r')\s+must\s+be\s+([^\n.,]+)',
                r'(?:' + re.escape(arg) + r')\s+should\s+be\s+([^\n.,]+)',
                r'(?:' + re.escape(arg) + r')\s+is\s+([^\n.,]+)',
                r'(?:' + re.escape(arg) + r'):\s+([^\n.,]+)'
            ]
            
            for pattern in restriction_patterns:
                restriction_match = re.search(pattern, docstring, re.IGNORECASE)
                if restriction_match:
                    param_info[arg]['restrictions'] = restriction_match.group(1).strip()
                    break
    
    return param_info

def extract_return_info(docstring: str) -> Dict[str, str]:
    """Extract return type and description from a docstring"""
    return_info = {
        'type': 'Unknown',
        'description': ''
    }
    
    if not docstring:
        return return_info
    
    # Google style
    google_pattern = r'Returns:\s*([\s\S]*?)(?:(?:Args|Parameters|Yields|Raises|Examples|Note|Notes|Attributes|See Also|References):|$)'
    google_return_pattern = r'(?:\(([^)]+)\))?\s*:\s*(.*)'
    
    # NumPy style
    numpy_pattern = r'Returns\s*\n\s*[-]+\s*\n([\s\S]*?)(?:(?:Parameters|Yields|Raises|Examples|Note|Notes|Attributes|See Also|References)\s*\n\s*[-]+|$)'
    numpy_return_pattern = r'([\w_]+)\s*:\s*([^\n]*)(?:\n\s+(.*))?'
    
    # Try Google style first
    google_match = re.search(google_pattern, docstring, re.MULTILINE)
    if google_match:
        return_block = google_match.group(1)
        return_match = re.match(google_return_pattern, return_block.strip())
        
        if return_match:
            return_type = return_match.group(1) if return_match.group(1) else 'Unknown'
            return_desc = return_match.group(2) if return_match.group(2) else ''
            
            return_info['type'] = return_type.strip()
            return_info['description'] = return_desc.strip()
        else:
            return_info['description'] = return_block.strip()
    
    # If Google style doesn't match, try NumPy style
    else:
        numpy_match = re.search(numpy_pattern, docstring, re.MULTILINE)
        if numpy_match:
            return_block = numpy_match.group(1)
            returns = re.findall(numpy_return_pattern, return_block, re.MULTILINE)
            
            if returns:
                return_type, return_desc1, return_desc2 = returns[0]
                return_info['type'] = return_type.strip()
                return_info['description'] = (return_desc1 + ' ' + (return_desc2 or '')).strip()
            else:
                return_info['description'] = return_block.strip()
    
    # Look for return type hint in the comments
    if return_info['type'] == "Unknown":
        return_type_pattern = r'@rtype\s*:\s*([^\n]+)'
        return_type_match = re.search(return_type_pattern, docstring)
        if return_type_match:
            return_info['type'] = return_type_match.group(1).strip()
    
    return return_info

def identify_input_output_parameters(func_node: ast.FunctionDef, docstring: str) -> tuple[List[Parameter], List[Parameter]]:
    """Identify which parameters are inputs and which are outputs"""
    input_params = []
    output_params = []
    
    if not hasattr(func_node, 'args') or not func_node.args:
        return input_params, output_params
    
    arg_names = [arg.arg for arg in func_node.args.args if arg.arg != 'self']
    defaults = []
    
    # Extract defaults
    if func_node.args.defaults:
        defaults = func_node.args.defaults
        # Pad with None for positional args
        defaults = [None] * (len(arg_names) - len(defaults)) + defaults
    else:
        defaults = [None] * len(arg_names)
    
    # Check docstring for parameter descriptions
    params_docs = {}
    
    if docstring:
        lines = docstring.split('\n')
        param_section = False
        param_pattern = r'\s*([\w_]+)\s*:\s*(.+)'
        
        for line in lines:
            line = line.strip()
            if line.lower().startswith('parameters:') or line.lower().startswith('args:') or line.lower().startswith('arguments:'):
                param_section = True
                continue
            
            if param_section:
                # Check if we've reached the end of the parameters section
                if line.startswith('Returns:') or not line:
                    param_section = False
                    continue
                
                # Try to extract parameter name and description
                match = re.search(param_pattern, line)
                if match:
                    param_name = match.group(1)
                    param_desc = match.group(2).strip()
                    params_docs[param_name] = param_desc
    
    # Check return pattern in docstring
    return_values = []
    if docstring:
        lines = docstring.split('\n')
        return_section = False
        for line in lines:
            line = line.strip()
            if line.lower().startswith('returns:') or line.lower().startswith('return:'):
                return_section = True
                # Extract from this line if it has a description
                parts = line.split(':', 1)
                if len(parts) > 1 and parts[1].strip():
                    return_values.append(parts[1].strip())
                continue
            
            if return_section and line:
                # Add the line as a return value description
                return_values.append(line)
            elif return_section and not line:
                return_section = False
    
    # Process the function arguments
    for i, arg_name in enumerate(arg_names):
        default_value = None
        if defaults[i] is not None:
            if isinstance(defaults[i], ast.Constant):
                default_value = repr(defaults[i].value)
            elif isinstance(defaults[i], ast.Name):
                default_value = defaults[i].id
            elif isinstance(defaults[i], ast.List):
                default_value = '[]'
            elif isinstance(defaults[i], ast.Dict):
                default_value = '{}'
        
        # Try to determine type from type annotations
        param_type = 'Unknown'
        if hasattr(func_node.args.args[i], 'annotation') and func_node.args.args[i].annotation:
            annotation = func_node.args.args[i].annotation
            if isinstance(annotation, ast.Name):
                param_type = annotation.id
            elif isinstance(annotation, ast.Subscript):
                # Handle complex types like List[str], Dict[str, int], etc.
                if isinstance(annotation.value, ast.Name):
                    base_type = annotation.value.id
                    
                    # Try to extract the inner type
                    inner_type = ''
                    if isinstance(annotation.slice, ast.Index):  # Python < 3.9
                        if isinstance(annotation.slice.value, ast.Name):
                            inner_type = annotation.slice.value.id
                    elif isinstance(annotation.slice, ast.Name):  # Python >= 3.9
                        inner_type = annotation.slice.id
                    
                    if inner_type:
                        param_type = f'{base_type}[{inner_type}]'
                    else:
                        param_type = base_type
        
        # Get description from docstring if available
        description = params_docs.get(arg_name, 'No description available')
        
        # Create parameter object
        param = Parameter(
            name=arg_name,
            type=param_type,
            description=description,
            default_value=default_value,
            required=default_value is None,
            line_number=func_node.lineno
        )
        
        # Determine if this is an input or output parameter
        # By default, all parameters are considered inputs
        is_output = False
        
        # Check parameter name for output indicators
        if any(kw in arg_name.lower() for kw in ['output', 'out', 'return', 'result', 'dest', 'destination']):
            is_output = True
        
        # Check parameter type for output indicators
        if any(output_type in param_type.lower() for output_type in ['output', 'result', 'file', 'path']):
            is_output = True
        
        # Check parameter description for output indicators
        if any(output_word in description.lower() for output_word in ['output', 'create', 'write', 'return']):
            is_output = True
        
        if is_output:
            output_params.append(param)
        else:
            input_params.append(param)
    
    # Look for return statement to identify outputs
    # If function has a return statement but no output parameters identified, use the return description
    if return_values and not output_params:
        return_description = ' '.join(return_values)
        output_params.append(Parameter(
            name='return_value',
            type=str(func_node.returns) if func_node.returns else 'Unknown',
            description=return_description,
            required=True,
            line_number=func_node.lineno
        ))
    
    return input_params, output_params

def extract_io_parameters(tree: ast.Module) -> Tuple[List[Parameter], List[Parameter]]:
    """Extract global input and output parameters from the AST"""
    input_params = []
    output_params = []
    
    # Look for global variables that might indicate input/output parameters
    hardcoded_params = extract_hardcoded_parameters(tree)
    
    # Classify hardcoded parameters into input/output
    for param in hardcoded_params:
        # Check if it seems like an input or output parameter based on name patterns
        if any(kw in param.name.lower() for kw in ['input', 'in_', 'source', 'src']):
            input_params.append(param)
        elif any(kw in param.name.lower() for kw in ['output', 'out_', 'target', 'dest', 'result']):
            output_params.append(param)
        else:
            # Default to input parameter
            input_params.append(param)
    
    # Check for file operations (open, read, write) to detect I/O parameters
    for node in ast.walk(tree):
        # Look for open() calls which indicate file I/O
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name) and node.func.id == 'open':
            # Check if we have enough arguments
            if len(node.args) >= 1 and len(node.args) >= 2:
                # First arg is the file path
                file_path = None
                if isinstance(node.args[0], ast.Constant) and isinstance(node.args[0].value, str):
                    file_path = node.args[0].value
                elif isinstance(node.args[0], ast.Name):
                    # It's a variable - check if we can find its value
                    var_name = node.args[0].id
                    # Check if this variable is defined earlier
                    for param in hardcoded_params:
                        if param.name == var_name and param.default_value:
                            try:
                                # Try to extract the string from the default value
                                if param.default_value.startswith('"') and param.default_value.endswith('"'):
                                    file_path = param.default_value[1:-1]
                                elif param.default_value.startswith('\'') and param.default_value.endswith('\''):
                                    file_path = param.default_value[1:-1]
                            except:
                                pass
                
                # Second arg is the mode
                mode = 'r'  # Default to read mode
                if isinstance(node.args[1], ast.Constant) and isinstance(node.args[1].value, str):
                    mode = node.args[1].value
                
                if file_path:
                    file_name = file_path.split('/')[-1].split('\\')[-1]  # Extract filename from path
                    file_type = 'Unknown'
                    
                    # Try to determine file type from extension
                    if '.' in file_name:
                        ext = file_name.split('.')[-1].lower()
                        if ext in ['shp', 'geojson', 'kml', 'gml']:
                            file_type = 'Vector File'
                        elif ext in ['tif', 'tiff', 'img', 'dem', 'asc', 'adf']:
                            file_type = 'Raster Dataset'
                        elif ext in ['dbf', 'csv', 'txt', 'tsv', 'xls', 'xlsx']:
                            file_type = 'Attribute Table'
                        elif ext in ['prj', 'sld', 'qml']:
                            file_type = 'GIS Metadata'
                        elif ext in ['py', 'sql', 'r', 'js']:
                            file_type = 'Script'
                        else:
                            file_type = 'File'
                    
                    # Determine if it's an input or output parameter based on the mode
                    if 'r' in mode and '+' not in mode:
                        # Read-only mode - input
                        input_params.append(Parameter(
                            name=file_name,
                            type=file_type,
                            description=f"Input file: {file_path}",
                            required=True,
                            line_number=node.lineno
                        ))
                    elif 'w' in mode or 'a' in mode:
                        # This is a write/append operation - identify the file as an output
                        output_params.append(Parameter(
                            name=file_name,
                            type=file_type,
                            description=f"Output file: {file_path}",
                            required=True,
                            line_number=node.lineno
                        ))
        
        # Look for command-line argument parsing which indicates input parameters
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name) and node.func.value.id in ['argparse', 'parser']:
                if node.func.attr == 'add_argument':
                    # This is an argparse parameter definition
                    param_name = ''
                    param_type = 'Unknown'
                    param_desc = ''
                    is_required = False
                    
                    # Extract argument name
                    for arg in node.args:
                        if isinstance(arg, ast.Constant) and isinstance(arg.value, str):
                            if arg.value.startswith('-'):
                                # This is an option like --input, extract the name
                                if param_name == '':
                                    # Remove leading dashes and use as parameter name
                                    param_name = arg.value.lstrip('-').replace('-', '_')
                            else:
                                # This is a positional argument
                                param_name = arg.value
                                is_required = True
                    
                    # Look for type, help and required in keywords
                    for kw in node.keywords:
                        if kw.arg == 'type' and isinstance(kw.value, ast.Name):
                            param_type = kw.value.id
                        elif kw.arg == 'help' and isinstance(kw.value, ast.Constant):
                            param_desc = kw.value.value
                        elif kw.arg == 'required' and isinstance(kw.value, ast.Constant):
                            is_required = kw.value.value
                    
                    # Check if this parameter name indicates input or output
                    if param_name:
                        param = Parameter(
                            name=param_name,
                            type=param_type,
                            description=param_desc,
                            required=is_required,
                            line_number=node.lineno
                        )
                        
                        # Determine if it's input or output based on name
                        if is_gis_parameter(param_name, param_type):
                            # For GIS parameters, check name patterns
                            if any(kw in param_name.lower() for kw in ['output', 'out_', 'dest', 'result', 'target']):
                                output_params.append(param)
                            else:
                                input_params.append(param)
                        else:
                            # For non-GIS parameters, default to input
                            input_params.append(param)
    
    # Look for sys.argv usage which indicates command-line parameters
    for node in ast.walk(tree):
        if isinstance(node, ast.Subscript) and isinstance(node.value, ast.Attribute):
            if hasattr(node.value, 'value') and isinstance(node.value.value, ast.Name) and node.value.value.id == 'sys' and node.value.attr == 'argv':
                # This is accessing command line arguments - sys.argv[i]
                if isinstance(node.slice, ast.Constant) and isinstance(node.slice.value, int) and node.slice.value > 0:
                    # Create a parameter for this command line argument
                    param_name = f"arg{node.slice.value}"
                    input_params.append(Parameter(
                        name=param_name,
                        type="Command Line Argument",
                        description=f"Command line argument at position {node.slice.value}",
                        required=True,
                        line_number=node.lineno
                    ))
    
    # Look for GIS-specific data imports
    for node in ast.walk(tree):
        if isinstance(node, ast.Call):
            func_name = ""
            if isinstance(node.func, ast.Name):
                func_name = node.func.id
            elif isinstance(node.func, ast.Attribute) and isinstance(node.func.value, ast.Name):
                func_name = f"{node.func.value.id}.{node.func.attr}"
            
            # Look for common GIS data import functions
            import_patterns = [
                (r'read_', 'Import'),
                (r'load_', 'Import'),
                (r'open_', 'Import'),
                (r'get_', 'Import'),
                (r'import_', 'Import'),
                (r'save_', 'Export'),
                (r'write_', 'Export'),
                (r'export_', 'Export'),
                (r'output_', 'Export')
            ]
            
            for pattern, io_type in import_patterns:
                if re.search(pattern, func_name, re.IGNORECASE):
                    # This looks like a data import or export function
                    # Check the arguments to see if they contain filenames
                    for i, arg in enumerate(node.args):
                        if isinstance(arg, ast.Constant) and isinstance(arg.value, str):
                            file_path = arg.value
                            file_name = file_path.split('/')[-1].split('\\')[-1]
                            
                            # Determine file type based on extension
                            file_type = 'File'
                            if '.' in file_name:
                                ext = file_name.split('.')[-1].lower()
                                if ext in ['shp', 'geojson', 'kml', 'gml']:
                                    file_type = 'Vector File'
                                elif ext in ['tif', 'tiff', 'img', 'dem', 'asc', 'adf']:
                                    file_type = 'Raster Dataset'
                                elif ext in ['dbf', 'csv', 'txt', 'tsv', 'xls', 'xlsx']:
                                    file_type = 'Attribute Table'
                            
                            # Add to appropriate list based on function name
                            if io_type == 'Import':
                                input_params.append(Parameter(
                                    name=file_name,
                                    type=file_type,
                                    description=f"Input file imported using {func_name}",
                                    required=True,
                                    line_number=node.lineno
                                ))
                            else:  # Export
                                output_params.append(Parameter(
                                    name=file_name,
                                    type=file_type,
                                    description=f"Output file exported using {func_name}",
                                    required=True,
                                    line_number=node.lineno
                                ))
                    
                    # Also check keyword arguments for filenames
                    for kw in node.keywords:
                        if kw.arg in ['file', 'filename', 'path', 'src', 'source', 'input', 'output', 'dest', 'destination']:
                            if isinstance(kw.value, ast.Constant) and isinstance(kw.value.value, str):
                                file_path = kw.value.value
                                file_name = file_path.split('/')[-1].split('\\')[-1]
                                
                                # Determine file type based on extension
                                file_type = 'File'
                                if '.' in file_name:
                                    ext = file_name.split('.')[-1].lower()
                                    if ext in ['shp', 'geojson', 'kml', 'gml']:
                                        file_type = 'Vector File'
                                    elif ext in ['tif', 'tiff', 'img', 'dem', 'asc', 'adf']:
                                        file_type = 'Raster Dataset'
                                    elif ext in ['dbf', 'csv', 'txt', 'tsv', 'xls', 'xlsx']:
                                        file_type = 'Attribute Table'
                                
                                # Add to appropriate list based on function name and parameter name
                                if io_type == 'Import' or kw.arg in ['file', 'filename', 'path', 'src', 'source', 'input']:
                                    input_params.append(Parameter(
                                        name=file_name,
                                        type=file_type,
                                        description=f"Input file specified by {kw.arg} parameter",
                                        required=True,
                                        line_number=node.lineno
                                    ))
                                else:  # Export or output parameter
                                    output_params.append(Parameter(
                                        name=file_name,
                                        type=file_type,
                                        description=f"Output file specified by {kw.arg} parameter",
                                        required=True,
                                        line_number=node.lineno
                                    ))
    
    # Deduplicate parameters by name
    unique_input_params = []
    unique_output_params = []
    input_param_names = set()
    output_param_names = set()
    
    for param in input_params:
        if param.name not in input_param_names:
            input_param_names.add(param.name)
            unique_input_params.append(param)
    
    for param in output_params:
        if param.name not in output_param_names:
            output_param_names.add(param.name)
            unique_output_params.append(param)
    
    return unique_input_params, unique_output_params
    
    arg_names = [arg.arg for arg in func_node.args.args if arg.arg != 'self']
    defaults = []
    
    # Extract default values for optional parameters
    if func_node.args.defaults:
        defaults = func_node.args.defaults
    
    # Get parameter info from docstring
    param_info = extract_parameter_info(docstring, arg_names, defaults)
    
    # Analyze function body to find potential output parameters
    output_param_names = set()
    
    # Look for patterns like "return x, y, z" or assignments to output parameters
    for node in ast.walk(func_node):
        if isinstance(node, ast.Return) and isinstance(node.value, ast.Tuple):
            # Return multiple values - potential outputs if they match parameter names
            for elt in node.value.elts:
                if isinstance(elt, ast.Name) and elt.id in arg_names:
                    output_param_names.add(elt.id)
        elif isinstance(node, ast.Return) and isinstance(node.value, ast.Name) and node.value.id in arg_names:
            # Return a single parameter value
            output_param_names.add(node.value.id)
        
        # Look for modifications to parameters which might indicate they're output parameters
        elif isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Attribute) and isinstance(target.value, ast.Name) and target.value.id in arg_names:
                    output_param_names.add(target.value.id)
                elif isinstance(target, ast.Subscript) and isinstance(target.value, ast.Name) and target.value.id in arg_names:
                    output_param_names.add(target.value.id)
    
    # Check parameter names for output indicators
    for arg in arg_names:
        if any(kw in arg.lower() for kw in ['output', 'result', 'export', 'dest', 'target']):
            output_param_names.add(arg)
    
    # Also check docstring for clues about output parameters
    # Common patterns like "modifies param_name" or "updates param_name"
    for arg in arg_names:
        output_patterns = [
            r'(?:modifies|updates|sets|writes to|populates|fills)\s+' + re.escape(arg),
            r'' + re.escape(arg) + r'\s+(?:is|will be)\s+(?:modified|updated|set|written to|populated|filled)',
            r'output\s+(?:to|into)\s+' + re.escape(arg),
            r'' + re.escape(arg) + r'\s+contains\s+(?:the\s+)?(?:output|result)'
        ]
        
        for pattern in output_patterns:
            if re.search(pattern, docstring, re.IGNORECASE):
                output_param_names.add(arg)
                break
        
        # Look for parameter type hints that suggest it's an output
        param_type = param_info.get(arg, {}).get('type', '').lower()
        if param_type and any(kw in param_type for kw in ['output', 'result', 'return']):
            output_param_names.add(arg)
    
    # Create parameter objects
    for arg_name in arg_names:
        param = Parameter(
            name=arg_name,
            type=param_info.get(arg_name, {}).get('type', 'Unknown'),
            description=param_info.get(arg_name, {}).get('description', ''),
            required=param_info.get(arg_name, {}).get('required', True),
            default_value=param_info.get(arg_name, {}).get('default_value'),
            restrictions=param_info.get(arg_name, {}).get('restrictions'),
            is_hardcoded=False,  # Will be set later if appropriate
        )
        
        # Enhanced GIS parameter detection
        if is_gis_parameter(arg_name, param.type):
            # Try to infer better type information for GIS parameters
            if 'shapefile' in arg_name.lower() or 'feature' in arg_name.lower() or '.shp' in str(param.default_value).lower():
                param.type = 'Feature Class' if param.type == 'Unknown' else param.type
            elif 'raster' in arg_name.lower() or 'dem' in arg_name.lower() or any(ext in str(param.default_value).lower() for ext in ['.tif', '.tiff', '.img']):
                param.type = 'Raster Dataset' if param.type == 'Unknown' else param.type
        
        # Determine if it's an input or output parameter
        if arg_name in output_param_names:
            output_params.append(param)
        else:
            input_params.append(param)
    
    return input_params, output_params

def extract_functions(tree: ast.Module) -> List[Function]:
    """Extract functions from the AST"""
    functions = []
    
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            # Skip private helper functions
            if node.name.startswith('_') and node.name != '__init__':
                continue
                
            docstring = extract_docstring(node)
            description = extract_description_from_docstring(docstring)
            
            # Get input and output parameters
            input_params, output_params = identify_input_output_parameters(node, docstring)
            
            # Get return information
            return_info = extract_return_info(docstring)
            
            # Determine if function is GIS-related
            is_gis_function = False
            
            # Check function name for GIS terms
            gis_function_patterns = [
                r'(?:spatial|geo|map|layer|feature|shape|buffer|intersect|dissolve|union|clip)',
                r'(?:project|reproject|transform|convert|calculate|analyze|extract|overlay)',
                r'(?:raster|vector|point|line|polygon|coordinate|projection|crs)',
                r'(?:interpolate|classify|symbolize|render|display|visualize)'
            ]
            
            for pattern in gis_function_patterns:
                if re.search(pattern, node.name, re.IGNORECASE):
                    is_gis_function = True
                    break
            
            # Check for GIS-related parameters
            if not is_gis_function:
                for param in input_params + output_params:
                    if is_gis_parameter(param.name, param.type):
                        is_gis_function = True
                        break
            
            # Check function body for GIS library calls
            if not is_gis_function:
                for subnode in ast.walk(node):
                    if isinstance(subnode, ast.Call) and isinstance(subnode.func, ast.Attribute):
                        if isinstance(subnode.func.value, ast.Name):
                            module_name = subnode.func.value.id
                            if module_name.lower() in [lib.lower() for lib in GIS_LIBRARIES.keys()]:
                                is_gis_function = True
                                break
            
            function = Function(
                name=node.name,
                description=description,
                parameters=input_params,  # Only include input params in function definition
                return_type=return_info['type'],
                return_description=return_info['description'],
                line_number=node.lineno,
                end_line_number=node.end_lineno if hasattr(node, 'end_lineno') else node.lineno,
                is_gis_related=is_gis_function
            )
            
            functions.append(function)
    
    return functions

def extract_libraries(tree: ast.Module) -> List[Library]:
    """Extract imported libraries from the AST"""
    libraries = []
    
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for name in node.names:
                lib_name = name.name
                alias = name.asname
                
                # Check if it's a GIS library
                is_gis_lib = any(gis_lib.lower() in lib_name.lower() for gis_lib in GIS_LIBRARIES.keys())
                
                libraries.append(Library(
                    name=lib_name,
                    alias=alias,
                    is_gis_library=is_gis_lib,
                    imported_items=[]
                ))
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                lib_name = node.module
                imported_items = [name.name for name in node.names]
                
                # Check if it's a GIS library
                is_gis_lib = any(gis_lib.lower() in lib_name.lower() for gis_lib in GIS_LIBRARIES.keys())
                
                # Check if this library was already imported
                existing_lib = next((lib for lib in libraries if lib.name == lib_name), None)
                
                if existing_lib:
                    existing_lib.imported_items.extend(imported_items)
                else:
                    libraries.append(Library(
                        name=lib_name,
                        is_gis_library=is_gis_lib,
                        imported_items=imported_items
                    ))
    
    return libraries

def extract_hardcoded_parameters(tree: ast.Module) -> List[Parameter]:
    """Extract hardcoded parameters from the AST"""
    hardcoded_params = []
    
    # Look for global assignments that might be hardcoded parameters
    for node in ast.iter_child_nodes(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and not target.id.startswith('_'):
                    # This looks like a global variable/parameter
                    param_name = target.id
                    
                    # Try to extract the value and its type
                    param_type = "Unknown"
                    default_value = None
                    
                    if isinstance(node.value, ast.Constant) and isinstance(node.value.value, str):
                        param_type = "str"
                        default_value = f'"{node.value.value}"'
                    elif isinstance(node.value, ast.Constant) and isinstance(node.value.value, (int, float)):
                        param_type = type(node.value.value).__name__
                        default_value = str(node.value.value)
                    elif isinstance(node.value, ast.Constant) and node.value.value is None:
                        param_type = "NoneType"
                        default_value = "None"
                    elif isinstance(node.value, ast.List):
                        param_type = "list"
                        # Try to get a simplified representation of the list
                        try:
                            elements = []
                            for elt in node.value.elts:
                                if isinstance(elt, ast.Constant) and isinstance(elt.value, str):
                                    elements.append(f'"{elt.value}"')
                                elif isinstance(elt, ast.Constant) and isinstance(elt.value, (int, float)):
                                    elements.append(str(elt.value))
                                else:
                                    elements.append("...")
                            default_value = f"[{', '.join(elements)}]"
                        except Exception:
                            default_value = "[...]"  # Fallback if we can't get the elements
                    elif isinstance(node.value, ast.Dict):
                        param_type = "dict"
                        default_value = "{...}"
                    elif isinstance(node.value, ast.Call):
                        if isinstance(node.value.func, ast.Name):
                            param_type = node.value.func.id
                        elif isinstance(node.value.func, ast.Attribute):
                            param_type = f"{node.value.func.value.id}.{node.value.func.attr}"
                        default_value = f"{param_type}(...)"
                    
                    # Check if this seems like a parameter (all caps or contains certain words)
                    is_param = param_name.isupper() or any(word in param_name.lower() for word in ['param', 'arg', 'input', 'output', 'path', 'file', 'dir', 'threshold', 'factor', 'radius', 'distance', 'buffer'])
                    
                    if is_param or is_gis_parameter(param_name, param_type):
                        hardcoded_params.append(Parameter(
                            name=param_name,
                            type=param_type,
                            description=f"Hardcoded {param_type} value",
                            required=True,
                            default_value=default_value,
                            is_hardcoded=True,
                            line_number=node.lineno
                        ))
    
    return hardcoded_params

def extract_script_description(tree: ast.Module) -> str:
    """Extract the script description from module docstring or first comments"""
    # Try to get the module docstring first
    docstring = ast.get_docstring(tree)
    if docstring:
        # Return the first paragraph of the docstring
        return docstring.split('\n\n')[0].strip()
    
    # If no docstring, look at imported libraries and functions to generate a description
    libraries = extract_libraries(tree)
    functions = extract_functions(tree)
    
    # Check if this is a GIS script based on the libraries
    gis_libraries = [lib for lib in libraries if lib.is_gis_library]
    gis_functions = [func for func in functions if func.is_gis_related]
    
    if gis_libraries or gis_functions:
        # Generate description based on GIS libraries and functions
        description_parts = []
        
        # Mention GIS libraries
        if gis_libraries:
            gis_lib_names = [lib.name for lib in gis_libraries]
            if len(gis_lib_names) == 1:
                description_parts.append(f"This script uses the {gis_lib_names[0]} library for geospatial data processing.")
            elif len(gis_lib_names) == 2:
                description_parts.append(f"This script uses {gis_lib_names[0]} and {gis_lib_names[1]} libraries for geospatial data processing.")
            else:
                libs_text = ", ".join(gis_lib_names[:-1]) + f", and {gis_lib_names[-1]}"
                description_parts.append(f"This script uses multiple GIS libraries ({libs_text}) for geospatial data processing.")
        
        # Describe main script functions based on function names
        if functions:
            function_categories = categorize_gis_functions(functions)
            
            for category, funcs in function_categories.items():
                if funcs:
                    description_parts.append(f"It includes {category} operations.")
        
        if description_parts:
            return " ".join(description_parts)
    
    # If we couldn't generate a specific description, provide a generic one
    if functions:
        function_names = [f.name for f in functions]
        return f"This script contains {len(functions)} functions: {', '.join(function_names[:3])}{'...' if len(functions) > 3 else ''}." 
    
    return "This script performs data processing operations."

def categorize_gis_functions(functions: List[Function]) -> Dict[str, List[Function]]:
    """Categorize GIS functions by their purpose"""
    categories = {
        "data loading": [],
        "data processing": [],
        "spatial analysis": [],
        "visualization": [],
        "export": []
    }
    
    for func in functions:
        name = func.name.lower()
        
        # Categorize by function name patterns
        if any(term in name for term in ['load', 'read', 'import', 'get', 'fetch', 'open']):
            categories["data loading"].append(func)
        elif any(term in name for term in ['plot', 'map', 'display', 'show', 'render', 'visualize', 'draw']):
            categories["visualization"].append(func)
        elif any(term in name for term in ['save', 'write', 'export', 'output', 'store']):
            categories["export"].append(func)
        elif any(term in name for term in ['buffer', 'intersect', 'union', 'clip', 'overlay', 'spatial', 'geo']):
            categories["spatial analysis"].append(func)
        else:
            categories["data processing"].append(func)
    
    return categories

def extract_workflow_steps(tree: ast.Module, functions: List[Function]) -> List[Workflow]:
    """Extract workflow steps from the code"""
    workflow_steps = []
    step_counter = 1
    
    # Look for main functions or __main__ blocks that might contain workflow steps
    main_function_nodes = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef) and node.name == 'main':
            main_function_nodes.append(node)
        elif isinstance(node, ast.If) and hasattr(node, 'test') and isinstance(node.test, ast.Compare):
            # Check for if __name__ == "__main__" pattern
            if (isinstance(node.test.left, ast.Name) and node.test.left.id == '__name__' and 
                len(node.test.ops) == 1 and isinstance(node.test.ops[0], ast.Eq) and 
                len(node.test.comparators) == 1 and isinstance(node.test.comparators[0], ast.Constant) and 
                node.test.comparators[0].value == '__main__'):
                # Add the entire block to capture all steps
                main_function_nodes.append(node)
    
    # If no main function or __main__ block, look for global function calls
    if not main_function_nodes:
        # Find all top-level function calls
        for node in ast.iter_child_nodes(tree):
            if isinstance(node, ast.Expr) and isinstance(node.value, ast.Call):
                main_function_nodes.append(node)
            # Also include top-level assignments with function calls
            elif isinstance(node, ast.Assign) and isinstance(node.value, ast.Call):
                main_function_nodes.append(node)
    
    # If we still don't have any clear workflow steps, use any function definitions
    if not main_function_nodes and functions:
        # For simple scripts, just use all function definitions as a workflow
        function_names_set = set()
        for func in functions:
            # Skip if we've already included this function name (avoid duplicates)
            if func.name in function_names_set:
                continue
            function_names_set.add(func.name)
            
            # Create a step for each function
            if is_gis_parameter(func.name) or func.is_gis_related or any(is_gis_parameter(p.name) for p in func.parameters):
                # Put GIS functions earlier in the workflow
                workflow_steps.insert(0, Workflow(
                    step_number=step_counter,
                    description=f"Process: {func.name}",
                    input_data=[p.name for p in func.parameters],
                    processing=func.description or f"Execute function {func.name}",
                    function_calls=[func.name]
                ))
            else:
                workflow_steps.append(Workflow(
                    step_number=step_counter,
                    description=f"Process: {func.name}",
                    input_data=[p.name for p in func.parameters],
                    processing=func.description or f"Execute function {func.name}",
                    function_calls=[func.name]
                ))
            step_counter += 1
        
        # Renumber steps after sorting
        for i, step in enumerate(workflow_steps):
            step.step_number = i + 1
        
        return workflow_steps
    
    # Process the main execution blocks to extract workflow
    for main_node in main_function_nodes:
        # Handle different node types
        if isinstance(main_node, ast.FunctionDef):
            # For function definitions like main()
            workflow_step = Workflow(
                step_number=step_counter,
                description=f"Main function: {main_node.name}",
                input_data=[arg.arg for arg in main_node.args.args if arg.arg != 'self'],
                output_data=[],
                processing=extract_description_from_docstring(extract_docstring(main_node)) or f"Execute main function {main_node.name}",
                function_calls=[]
            )
            
            # Look for function calls within main
            extract_function_calls_from_body(main_node.body, workflow_step)
            
            if workflow_step.function_calls or workflow_step.processing:
                workflow_steps.append(workflow_step)
                step_counter += 1
                
        elif isinstance(main_node, ast.If):
            # For the if __name__ == "__main__": block
            if_main_steps = extract_steps_from_body(main_node.body, step_counter)
            workflow_steps.extend(if_main_steps)
            step_counter += len(if_main_steps)
            
        elif isinstance(main_node, ast.Expr) and isinstance(main_node.value, ast.Call):
            # For direct function calls
            step = create_step_from_call(main_node.value, step_counter, "Execute")
            if step:
                workflow_steps.append(step)
                step_counter += 1
                
        elif isinstance(main_node, ast.Assign) and isinstance(main_node.value, ast.Call):
            # For assignments with function calls
            step = create_step_from_call(main_node.value, step_counter, "Calculate")
            if step:
                # Add output variables
                for target in main_node.targets:
                    if isinstance(target, ast.Name):
                        step.output_data.append(target.id)
                workflow_steps.append(step)
                step_counter += 1
    
    # If we still have no workflow steps but we have functions, create a synthetic workflow
    if not workflow_steps and functions:
        # Classify functions into data loading, processing, and output phases
        data_loading = []
        processing = []
        output = []
        
        for func in functions:
            # Classify based on name patterns
            if any(kw in func.name.lower() for kw in ['load', 'read', 'import', 'get', 'fetch']):
                data_loading.append(func)
            elif any(kw in func.name.lower() for kw in ['write', 'save', 'export', 'output', 'display', 'plot', 'show', 'print']):
                output.append(func)
            else:
                processing.append(func)
        
        # Create workflow in logical order: data loading -> processing -> output
        for phase, funcs, phase_name in [(data_loading, "Data Loading"), (processing, "Processing"), (output, "Output")]:
            for func in funcs:
                workflow_steps.append(Workflow(
                    step_number=step_counter,
                    description=f"{phase_name}: {func.name}",
                    input_data=[p.name for p in func.parameters],
                    processing=func.description or f"Execute function {func.name}",
                    function_calls=[func.name]
                ))
                step_counter += 1
    
    return workflow_steps

def extract_steps_from_body(body, start_step_number):
    """Extract workflow steps from a block of statements"""
    steps = []
    step_counter = start_step_number
    
    for stmt in body:
        step = None
        
        if isinstance(stmt, ast.Expr) and isinstance(stmt.value, ast.Call):
            # Direct function call
            step = create_step_from_call(stmt.value, step_counter, "Execute")
            
        elif isinstance(stmt, ast.Assign) and isinstance(stmt.value, ast.Call):
            # Assignment with function call
            step = create_step_from_call(stmt.value, step_counter, "Calculate")
            if step:
                # Add output variables
                for target in stmt.targets:
                    if isinstance(target, ast.Name):
                        step.output_data.append(target.id)
        
        elif isinstance(stmt, ast.For) or isinstance(stmt, ast.While) or isinstance(stmt, ast.If):
            # Loop or conditional - extract a step describing it and recursively process its body
            condition_desc = ""
            if isinstance(stmt, ast.For):
                if isinstance(stmt.target, ast.Name) and isinstance(stmt.iter, ast.Name):
                    condition_desc = f"For each {stmt.target.id} in {stmt.iter.id}"
                else:
                    condition_desc = "For loop iteration"
            elif isinstance(stmt, ast.While):
                condition_desc = "While condition is true"
            elif isinstance(stmt, ast.If):
                condition_desc = "If condition is true"
            
            step = Workflow(
                step_number=step_counter,
                description=condition_desc,
                input_data=[],
                output_data=[],
                processing=condition_desc,
                function_calls=[]
            )
            
            # Add the control flow as a step
            steps.append(step)
            step_counter += 1
            
            # Process the body recursively
            inner_steps = extract_steps_from_body(stmt.body, step_counter)
            steps.extend(inner_steps)
            step_counter += len(inner_steps)
        
        if step and (step.function_calls or step.processing):
            steps.append(step)
            step_counter += 1
    
    return steps

def create_step_from_call(call_node, step_number, action_prefix):
    """Create a workflow step from a function call node"""
    func_name = ""
    is_gis_call = False
    
    # Extract function name
    if isinstance(call_node.func, ast.Name):
        func_name = call_node.func.id
    elif isinstance(call_node.func, ast.Attribute):
        if isinstance(call_node.func.value, ast.Name):
            module_name = call_node.func.value.id
            func_attr = call_node.func.attr
            func_name = f"{module_name}.{func_attr}"
            
            # Check if this is a GIS library call
            if module_name.lower() in [name.lower() for name in GIS_LIBRARIES.keys()]:
                is_gis_call = True
    
    if not func_name:
        return None
    
    # Check if the function name indicates a GIS operation
    if not is_gis_call:
        is_gis_call = identify_gis_function(func_name)
    
    # Create step with appropriate description
    if is_gis_call:
        action_prefix = "Process GIS data with"
    
    step = Workflow(
        step_number=step_number,
        description=f"{action_prefix}: {func_name}",
        input_data=[],
        output_data=[],
        processing=f"Call function {func_name}",
        function_calls=[func_name]
    )
    
    # Extract argument values as inputs
    for arg in call_node.args:
        if isinstance(arg, ast.Name):
            step.input_data.append(arg.id)
    
    # Look for keyword arguments
    for keyword in call_node.keywords:
        if keyword.arg and isinstance(keyword.value, ast.Name):
            step.input_data.append(keyword.value.id)
    
    return step

def extract_function_calls_from_body(body, workflow_step):
    """Extract function calls from a block of statements and add them to the workflow step"""
    for stmt in body:
        if isinstance(stmt, ast.Expr) and isinstance(stmt.value, ast.Call):
            if isinstance(stmt.value.func, ast.Name):
                workflow_step.function_calls.append(stmt.value.func.id)
            elif isinstance(stmt.value.func, ast.Attribute):
                if isinstance(stmt.value.func.value, ast.Name):
                    workflow_step.function_calls.append(f"{stmt.value.func.value.id}.{stmt.value.func.attr}")
        
        elif isinstance(stmt, ast.Assign) and isinstance(stmt.value, ast.Call):
            if isinstance(stmt.value.func, ast.Name):
                workflow_step.function_calls.append(stmt.value.func.id)
            elif isinstance(stmt.value.func, ast.Attribute):
                if isinstance(stmt.value.func.value, ast.Name):
                    workflow_step.function_calls.append(f"{stmt.value.func.value.id}.{stmt.value.func.attr}")
            
            # Add outputs
            for target in stmt.targets:
                if isinstance(target, ast.Name):
                    workflow_step.output_data.append(target.id)
        
        # Recursively process nested blocks
        elif hasattr(stmt, 'body') and stmt.body:
            extract_function_calls_from_body(stmt.body, workflow_step)
        
        # Handle else clauses in if statements
        if hasattr(stmt, 'orelse') and stmt.orelse:
            extract_function_calls_from_body(stmt.orelse, workflow_step)

def extract_io_parameters(tree: ast.Module) -> Tuple[List[Parameter], List[Parameter]]:
    """Extract global input and output parameters from the AST"""
    input_params = []
    output_params = []
    
    # Look for common patterns that indicate global input/output parameters
    # This can include checking for command line argument parsing, file I/O, etc.
    
    # Pattern: sys.argv usage
    for node in ast.walk(tree):
        if isinstance(node, ast.Subscript) and isinstance(node.value, ast.Attribute):
            if isinstance(node.value.value, ast.Name) and node.value.value.id == 'sys' and node.value.attr == 'argv':
                # This is likely using command line arguments
                input_params.append(Parameter(
                    name="Command line arguments",
                    type="list",
                    description="Input parameters passed via command line",
                    required=True,
                    line_number=node.lineno
                ))
                break
    
    # Pattern: argparse usage
    for node in ast.walk(tree):
        if isinstance(node, ast.Assign):
            if isinstance(node.value, ast.Call) and isinstance(node.value.func, ast.Attribute):
                if isinstance(node.value.func.value, ast.Name) and node.value.func.value.id == 'argparse' and node.value.func.attr == 'ArgumentParser':
                    # This is using argparse - follow the parser to find arguments
                    parser_name = None
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            parser_name = target.id
                    
                    if parser_name:
                        # Look for add_argument calls
                        for subnode in ast.walk(tree):
                            if isinstance(subnode, ast.Call) and isinstance(subnode.func, ast.Attribute):
                                if isinstance(subnode.func.value, ast.Name) and subnode.func.value.id == parser_name and subnode.func.attr == 'add_argument':
                                    # This is adding an argument
                                    arg_name = None
                                    arg_help = ""
                                    arg_required = True
                                    arg_type = "Unknown"
                                    
                                    # Get argument name
                                    if subnode.args:
                                        for arg in subnode.args:
                                            if isinstance(arg, ast.Constant) and isinstance(arg.value, str) and arg.value.startswith('-'):
                                                arg_name = arg.value.lstrip('-').replace('-', '_')
                                    
                                    # Check keywords for help, required, etc.
                                    for kw in subnode.keywords:
                                        if kw.arg == 'help' and isinstance(kw.value, ast.Constant) and isinstance(kw.value.value, str):
                                            arg_help = kw.value.value
                                        elif kw.arg == 'required' and isinstance(kw.value, ast.Constant):
                                            if hasattr(kw.value, 'value'):
                                                arg_required = bool(kw.value.value)
                                        elif kw.arg == 'type' and isinstance(kw.value, ast.Name):
                                            arg_type = kw.value.id
                                    
                                    if arg_name:
                                        # Check if this is likely a GIS parameter
                                        is_gis_param = is_gis_parameter(arg_name, arg_type)
                                        
                                        if 'output' in arg_name.lower() or any(term in arg_help.lower() for term in ['output', 'save', 'write', 'export']):
                                            # This is likely an output parameter
                                            output_params.append(Parameter(
                                                name=arg_name,
                                                type=arg_type,
                                                description=arg_help,
                                                required=arg_required,
                                                line_number=subnode.lineno
                                            ))
                                        else:
                                            # Assume it's an input parameter
                                            input_params.append(Parameter(
                                                name=arg_name,
                                                type=arg_type,
                                                description=arg_help,
                                                required=arg_required,
                                                line_number=subnode.lineno
                                            ))
    
    # Look for file open() calls to identify input/output files
    for node in ast.walk(tree):
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name) and node.func.id == 'open':
            if len(node.args) >= 2 and isinstance(node.args[1], ast.Constant) and isinstance(node.args[1].value, str):
                mode = node.args[1].value
                if 'r' in mode and not ('w' in mode or 'a' in mode):
                    # This is a read operation - identify the file as an input
                    if isinstance(node.args[0], ast.Constant) and isinstance(node.args[0].value, str):
                        file_path = node.args[0].value
                        file_name = file_path.split('/')[-1].split('\\')[-1]  # Extract filename from path
                        
                        input_params.append(Parameter(
                            name=file_name,
                            type="File",
                            description=f"Input file: {file_path}",
                            required=True,
                            line_number=node.lineno
                        ))
                elif 'w' in mode or 'a' in mode:
                    # This is a write/append operation - identify the file as an output
                    if isinstance(node.args[0], ast.Constant) and isinstance(node.args[0].value, str):
                        file_path = node.args[0].value
                        file_name = file_path.split('/')[-1].split('\\')[-1]  # Extract filename from path
                        
                        output_params.append(Parameter(
                            name=file_name,
                            type="File",
                            description=f"Output file: {file_path}",
                            required=True,
                            line_number=node.lineno
                        ))
    
    return input_params, output_params