/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import "../variables";


.mdl-spinner {
  display: inline-block;
  position: relative;
  width: $spinner-size;
  height: $spinner-size;

  &:not(.is-upgraded).is-active:after {
    content: "Loading...";
  }

  &.is-upgraded.is-active {
    animation: mdl-spinner__container-rotate $spinner-duration linear infinite;
  }
}

@keyframes mdl-spinner__container-rotate {
  to { transform: rotate(360deg) }
}

.mdl-spinner__layer {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.mdl-spinner__layer-1 {
  border-color: $spinner-color-1;

  .mdl-spinner--single-color & {
    border-color: $spinner-single-color;
  }

  .mdl-spinner.is-active & {
    animation:
        mdl-spinner__fill-unfill-rotate (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both,
        mdl-spinner__layer-1-fade-in-out (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both;
  }
}

.mdl-spinner__layer-2 {
  border-color: $spinner-color-2;

  .mdl-spinner--single-color & {
    border-color: $spinner-single-color;
  }

  .mdl-spinner.is-active & {
    animation:
        mdl-spinner__fill-unfill-rotate (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both,
        mdl-spinner__layer-2-fade-in-out (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both;
  }
}

.mdl-spinner__layer-3 {
  border-color: $spinner-color-3;

  .mdl-spinner--single-color & {
    border-color: $spinner-single-color;
  }

  .mdl-spinner.is-active & {
    animation:
        mdl-spinner__fill-unfill-rotate (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both,
        mdl-spinner__layer-3-fade-in-out (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both;
  }
}

.mdl-spinner__layer-4 {
  border-color: $spinner-color-4;

  .mdl-spinner--single-color & {
    border-color: $spinner-single-color;
  }

  .mdl-spinner.is-active & {
    animation:
        mdl-spinner__fill-unfill-rotate (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both,
        mdl-spinner__layer-4-fade-in-out (4 * $spinner-arc-time)
            $animation-curve-fast-out-slow-in infinite both;
  }
}

@keyframes mdl-spinner__fill-unfill-rotate {
  12.5% { transform: rotate(0.5 * $spinner-arc-size);  }
  25%   { transform: rotate($spinner-arc-size);  }
  37.5% { transform: rotate(1.5 * $spinner-arc-size);  }
  50%   { transform: rotate(2 * $spinner-arc-size);  }
  62.5% { transform: rotate(2.5 * $spinner-arc-size);  }
  75%   { transform: rotate(3 * $spinner-arc-size);  }
  87.5% { transform: rotate(3.5 * $spinner-arc-size); }
  to    { transform: rotate(4 * $spinner-arc-size); }
}

/**
* HACK: Even though the intention is to have the current .mdl-spinner__layer-N
* at `opacity: 1`, we set it to `opacity: 0.99` instead since this forces Chrome
* to do proper subpixel rendering for the elements being animated. This is
* especially visible in Chrome 39 on Ubuntu 14.04. See:
*
* - https://github.com/Polymer/paper-spinner/issues/9
* - https://code.google.com/p/chromium/issues/detail?id=436255
*/
@keyframes mdl-spinner__layer-1-fade-in-out {
  from { opacity: 0.99; }
  25% { opacity: 0.99; }
  26% { opacity: 0; }
  89% { opacity: 0; }
  90% { opacity: 0.99; }
  100% { opacity: 0.99; }
}

@keyframes mdl-spinner__layer-2-fade-in-out {
  from { opacity: 0; }
  15% { opacity: 0; }
  25% { opacity: 0.99; }
  50% { opacity: 0.99; }
  51% { opacity: 0; }
}

@keyframes mdl-spinner__layer-3-fade-in-out {
  from { opacity: 0; }
  40% { opacity: 0; }
  50% { opacity: 0.99; }
  75% { opacity: 0.99; }
  76% { opacity: 0; }
}

@keyframes mdl-spinner__layer-4-fade-in-out {
  from { opacity: 0; }
  65% { opacity: 0; }
  75% { opacity: 0.99; }
  90% { opacity: 0.99; }
  100% { opacity: 0; }
}

/**
* Patch the gap that appear between the two adjacent
* div.mdl-spinner__circle-clipper while the spinner is rotating
* (appears on Chrome 38, Safari 7.1, and IE 11).
*
* Update: the gap no longer appears on Chrome when .mdl-spinner__layer-N's
* opacity is 0.99, but still does on Safari and IE.
*/
.mdl-spinner__gap-patch {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  left: 45%;
  width: 10%;
  height: 100%;
  overflow: hidden;
  border-color: inherit;

  & .mdl-spinner__circle {
    width: 1000%;
    left: -450%;
  }
}

.mdl-spinner__circle-clipper {
  display: inline-block;
  position: relative;
  width: 50%;
  height: 100%;
  overflow: hidden;
  border-color: inherit;

  &.mdl-spinner__left {
    float: left;
  }

  &.mdl-spinner__right {
    float: right;
  }

  & .mdl-spinner__circle {
    width: 200%;
  }
}

.mdl-spinner__circle {
  box-sizing: border-box;
  height: 100%;
  border-width: $spinner-stroke-width;
  border-style: solid;
  border-color: inherit;
  border-bottom-color: transparent !important;
  border-radius: 50%;
  animation: none;

  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  .mdl-spinner__left & {
    border-right-color: transparent !important;
    transform: rotate(129deg);

    .mdl-spinner.is-active & {
      animation: mdl-spinner__left-spin $spinner-arc-time
          $animation-curve-fast-out-slow-in infinite both;
    }
  }

  .mdl-spinner__right & {
    left: -100%;
    border-left-color: transparent !important;
    transform: rotate(-129deg);

    .mdl-spinner.is-active & {
      animation: mdl-spinner__right-spin $spinner-arc-time
          $animation-curve-fast-out-slow-in infinite both;
    }
  }
}

@keyframes mdl-spinner__left-spin {
  from { transform: rotate(130deg); }
  50% { transform: rotate(-5deg); }
  to { transform: rotate(130deg); }
}

@keyframes mdl-spinner__right-spin {
  from { transform: rotate(-130deg); }
  50% { transform: rotate(5deg); }
  to { transform: rotate(-130deg); }
}
