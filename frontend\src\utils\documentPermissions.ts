import { doc, getFirestore, updateDoc, arrayUnion, arrayRemove, collection, query, where, getDocs } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { COLLECTIONS } from './firebaseConfig';
import { toast } from 'sonner';

/**
 * Utility for managing document permissions
 * Helps solve permission issues by directly modifying document access
 */

// Initialize Firestore
const db = getFirestore(firebaseApp);

/**
 * Grant a user read permission to a document
 * @param documentId Document ID to modify permissions for
 * @param userId User ID to grant access to
 */
export const grantDocumentReadAccess = async (documentId: string, userId: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTIONS.DOCUMENTS, documentId);
    
    // Add the user to readers array
    await updateDoc(docRef, {
      readers: arrayUnion(userId),
      // Include this field to track permission changes
      permissionsLastUpdated: new Date()
    });
    
    return true;
  } catch (error) {
    console.error('Error granting document access:', error);
    return false;
  }
};

/**
 * Grant a user write permission to a document
 * @param documentId Document ID to modify permissions for
 * @param userId User ID to grant access to
 */
export const grantDocumentWriteAccess = async (documentId: string, userId: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTIONS.DOCUMENTS, documentId);
    
    // Add the user to writers array
    await updateDoc(docRef, {
      writers: arrayUnion(userId),
      // Include this field to track permission changes
      permissionsLastUpdated: new Date()
    });
    
    return true;
  } catch (error) {
    console.error('Error granting document write access:', error);
    return false;
  }
};

/**
 * Revoke a user's read permission from a document
 * @param documentId Document ID to modify permissions for
 * @param userId User ID to revoke access from
 */
export const revokeDocumentReadAccess = async (documentId: string, userId: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTIONS.DOCUMENTS, documentId);
    
    // Remove the user from readers array
    await updateDoc(docRef, {
      readers: arrayRemove(userId),
      // Include this field to track permission changes
      permissionsLastUpdated: new Date()
    });
    
    return true;
  } catch (error) {
    console.error('Error revoking document access:', error);
    return false;
  }
};

/**
 * Revoke a user's write permission from a document
 * @param documentId Document ID to modify permissions for
 * @param userId User ID to revoke access from
 */
export const revokeDocumentWriteAccess = async (documentId: string, userId: string): Promise<boolean> => {
  try {
    const docRef = doc(db, COLLECTIONS.DOCUMENTS, documentId);
    
    // Remove the user from writers array
    await updateDoc(docRef, {
      writers: arrayRemove(userId),
      // Include this field to track permission changes
      permissionsLastUpdated: new Date()
    });
    
    return true;
  } catch (error) {
    console.error('Error revoking document write access:', error);
    return false;
  }
};

/**
 * Fix permissions for all documents owned by a user
 * This is a utility function to repair broken permissions
 * @param userId User ID who owns the documents
 */
export const repairUserDocumentPermissions = async (userId: string): Promise<{ success: boolean, fixed: number }> => {
  try {
    // Query for all documents belonging to this user
    const docsQuery = query(
      collection(db, COLLECTIONS.DOCUMENTS),
      where('userId', '==', userId)
    );
    
    const querySnapshot = await getDocs(docsQuery);
    let fixedCount = 0;
    
    // Process each document
    const updates = querySnapshot.docs.map(async (snapshot) => {
      const docId = snapshot.id;
      const docRef = doc(db, COLLECTIONS.DOCUMENTS, docId);
      
      try {
        // Update the document to ensure the owner has full access
        await updateDoc(docRef, {
          // Ensure arrays exist and contain the owner
          readers: arrayUnion(userId),
          writers: arrayUnion(userId),
          owner: userId, // Explicitly set owner field
          permissionsLastUpdated: new Date(),
          permissionRepaired: true
        });
        fixedCount++;
        return true;
      } catch (err) {
        console.error(`Error repairing permissions for document ${docId}:`, err);
        return false;
      }
    });
    
    // Wait for all updates to complete
    await Promise.all(updates);
    
    return { 
      success: true, 
      fixed: fixedCount 
    };
  } catch (error) {
    console.error('Error repairing user document permissions:', error);
    return { 
      success: false, 
      fixed: 0 
    };
  }
};

/**
 * Helper function to invoke from UI to fix all document permissions for the current user
 * Shows toast notifications for feedback
 * @param userId Current user's ID
 */
export const fixAllDocumentPermissions = async (userId: string): Promise<void> => {
  if (!userId) {
    toast.error('You must be logged in to fix document permissions');
    return;
  }
  
  toast.loading('Fixing document permissions...');
  
  try {
    const result = await repairUserDocumentPermissions(userId);
    
    if (result.success) {
      toast.success(`Fixed permissions for ${result.fixed} documents`);
    } else {
      toast.error('Failed to fix document permissions');
    }
  } catch (err) {
    console.error('Error in fixAllDocumentPermissions:', err);
    toast.error('An error occurred while fixing permissions');
  } finally {
    toast.dismiss();
  }
};
