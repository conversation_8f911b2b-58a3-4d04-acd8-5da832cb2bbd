from fastapi import APIRouter, HTTPException, Query
from app.auth import AuthorizedUser
from firebase_admin import firestore
import json
import re
from typing import Dict, Any, List, Optional

# Import models from documentation_models module
from app.apis.documentation_models import (
    DocumentationRequest,
    DocumentationResponse,
    DocumentationGenerationResponse,
    DocumentationParameter,
    DocumentationWorkflowStep,
    DocumentationWorkflow,
    DocumentationFunction
)

# Import code validation to check if code is valid
from app.apis.code_validation import validate_code

# Import LLM service
from app.apis.llm_service import analyze_code_with_llm, get_available_llm_providers, LLMProvider, ModelType

# -- Firebase Admin SDK Initialization --
import firebase_admin
from firebase_admin import credentials
import os
import json # Added for json.loads

# Initialize Firebase Admin SDK if not already initialized
if not firebase_admin._apps:
    service_account_key_json = os.environ.get("FIREBASE_SERVICE_ACCOUNT_KEY")
    if service_account_key_json:
        try:
            service_account_info = json.loads(service_account_key_json)
            cred = credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully with service account.")
        except Exception as e:
            print(f"ERROR: Firebase Admin SDK initialization with service account failed: {e}. Firestore access might fail.")
            # Consider if a fallback or raising an error is more appropriate
            # For now, just printing error, app might not work correctly if this fails.
    else:
        print("ERROR: FIREBASE_SERVICE_ACCOUNT_KEY environment variable not found. Firebase Admin SDK not initialized. Firestore access will fail.")
        # App will likely not function correctly without Firebase access.
# -- End Firebase Initialization --

router = APIRouter(prefix="/documentation")

# GIS-specific tool and function patterns to enhance documentation
GIS_FUNCTIONS = [
    # ArcGIS patterns
    r'arcpy\.\w+',
    r'arcpy\.[a-zA-Z]+\.[a-zA-Z]+',
    # QGIS patterns
    r'qgis\.\w+',
    r'QgsProcessing\w+',
    # GeoPandas/Shapely patterns
    r'geopandas\.\w+',
    r'shapely\.\w+',
    # GDAL/OGR patterns
    r'gdal\.\w+',
    r'ogr\.\w+',
    # GIS-related terms
    r'\b(?:buffer|intersect|clip|union|dissolve|overlay)\b'
]

@router.post("/generate")
async def generate_documentation(request: DocumentationRequest, user: AuthorizedUser) -> DocumentationGenerationResponse:
    """
    Generate documentation for code.
    
    This endpoint generates detailed documentation for the provided code,
    leveraging LLM services like OpenAI's GPT models or DeepSeek's coding models.
    
    The documentation will include:
    - Textual description of what the code does
    - Parameters (input, output, and hardcoded)
    - Tools and functions used in the code
    - Workflow steps showing the execution flow
    
    Args:
        request (DocumentationRequest): The request containing:
            - code: The source code to document
            - language: Programming language (default: Python)
            - provider: LLM provider to use ("openai", "deepseek", or "auto")
            - format: Output format (default: "markdown")
            - documentationLanguage: Language for generated docs (en, de, etc.)
            - documentation_id: Optional ID of existing doc to fetch context for
        
    Returns:
        DocumentationGenerationResponse: The generated documentation or error details
    """
    print("\n============== DOCUMENTATION GENERATION STARTED ==============")
    print(f"Request received - Code length: {len(request.code)} characters, Language: {request.language}, Provider: {request.provider}, DocID: {request.documentation_id}")
    # Use string concatenation instead of problematic f-string with backslashes
    first_100_chars = request.code[:100].replace('\n', '[newline]')
    print(f"First 100 chars of code: {first_100_chars}...")
    
    # First verify code is valid
    print("\n----- Step 1: Validating Code -----")
    is_valid, error_message, detected_language, line_num, col_num, is_gis_code = validate_code(request.code, request.language)
    
    print(f"Code validation result: {is_valid}")
    print(f"Detected language: {detected_language}")
    print(f"Is GIS code: {is_gis_code}")
    
    if not is_valid:
        print(f"Validation failed: {error_message}")
        print(f"Error at line {line_num}, column {col_num}")
        return DocumentationGenerationResponse(
            success=False,
            error_message=f"Invalid code: {error_message}"
        )
    
    # Check if we have available LLM providers
    print("\n----- Step 2: Checking Available LLM Providers -----")
    available_providers = await get_available_llm_providers()
    print(f"Available providers: {available_providers}")
    
    if not available_providers:
        print("Error: No LLM providers available")
        return DocumentationGenerationResponse(
            success=False,
            error_message="No LLM providers available. Please configure an API key."
        )
    
    # Determine which provider to use
    provider = request.provider.lower()
    if provider == "auto" or provider not in available_providers:
        provider = available_providers[0]
    
    # Set retry behavior - if user explicitly chooses a provider, don't auto-retry with another
    retry_on_failure = provider == "auto"
    
    # Determine if the code is GIS-related by checking for common GIS patterns
    is_gis_code_detected = any(re.search(pattern, request.code) for pattern in GIS_FUNCTIONS)
    
    print(f"Using provider: {provider} with retry_on_failure={retry_on_failure}, is_gis_code={is_gis_code_detected}")
    
    # Initialize documentation object
    documentation = DocumentationResponse(
        originalCode=request.code
    )
    
    # Determine preferred language from request body, default to 'en'
    print("\n----- Determining Documentation Language -----")
    print(f"DEBUG: Received User ID (user.sub): {user.sub}")
    print(f"DEBUG: Received requested_language from frontend: {request.documentationLanguage}")
    preferred_language = request.documentationLanguage.lower().strip() if request.documentationLanguage else 'en'
    # Basic validation for supported languages
    supported_languages = ['en', 'de', 'fr', 'it', 'es']
    if preferred_language not in supported_languages:
        print(f"WARN: Unsupported language '{preferred_language}' requested. Defaulting to 'en'.")
        preferred_language = 'en' # Fallback to 'en' if unsupported
    print(f"DEBUG: Using documentation language from request: {preferred_language}")

    # Fetch project_context:
# 1. Directly from the request if provided (new way for immediate context)
# 2. Fallback to fetching from Firestore if documentation_id is provided and request.project_context is not set (old way, now expecting string)
    project_custom_context = ""

    if request.project_context:
        print("\n----- Using Project Context from Request -----")
        project_custom_context = request.project_context
        print(f"Project context provided in request. Length: {len(project_custom_context)}")
        context_preview = project_custom_context[:200].replace('\n', '[newline]')
        print(f"Request context preview: {context_preview}...")
    elif request.documentation_id:
        print(f"\n----- Attempting to Fetch Project Context from Firestore for Doc ID: {request.documentation_id} -----")
        try:
            db_client = firestore.client()
            doc_ref = db_client.collection("documentations").document(request.documentation_id)
            doc_snapshot = doc_ref.get()
            if doc_snapshot.exists:
                doc_data = doc_snapshot.to_dict()
                # Expect project_context to be a string directly as per MYA-15
                firestore_project_context = doc_data.get("project_context") 
                
                if isinstance(firestore_project_context, str) and firestore_project_context.strip():
                    project_custom_context = firestore_project_context
                    print(f"Successfully fetched project context string from Firestore. Length: {len(project_custom_context)}")
                    context_preview = project_custom_context[:200].replace('\n', '[newline]')
                    print(f"Firestore context preview: {context_preview}...")
                elif firestore_project_context is None:
                    print("Project context field is null or not found in the Firestore document.")
                elif not firestore_project_context.strip():
                    print("Project context field in Firestore is an empty string.")
                else: # It exists but is not a string (e.g. old dictionary format)
                    print(f"Project context in Firestore is not a string (found type: {type(firestore_project_context).__name__}). Ignoring old format as per MYA-15.")
                    # As per MYA-15, we are moving away from the dictionary format.
                    # If old format is found, we don't use it. New context must be a string.
            else:
                print(f"No document found with ID {request.documentation_id} in 'documentations' collection to fetch context from.")
        except Exception as e:
            print(f"Error fetching project context from Firestore for {request.documentation_id}: {type(e).__name__} - {str(e)}")
            # For now, we log and continue if Firestore fetch fails.
    else:
        print("\n----- No Project Context Provided (neither in request nor via documentation_id) -----")

    # Define a robust system prompt first that includes GIS expertise
    base_system_prompt_text = f"""IMPORTANT: YOU MUST GENERATE ALL TEXTUAL OUTPUTS STRICTLY IN THE FOLLOWING LANGUAGE: {preferred_language}.
Language codes: en=English, de=German, fr=French, it=Italian, es=Spanish.
Failure to use {preferred_language} will result in an incorrect response.

You are an expert in programming documentation and code analysis for the **{request.language.upper()}** language.
Your specialty is analyzing scripts, especially those used in data analysis and automation.
If the code appears to be related to GIS (Geographic Information Systems), apply your knowledge of spatial data formats, coordinate systems, and geoprocessing operations.
Your task is to generate accurate, detailed, and technically precise documentation components.
Always follow the exact output format requested in the prompt.
For JSON outputs, ensure they are properly formatted and parsable.
Focus on identifying the core logic, parameters, functions, and overall workflow of the provided **{request.language.upper()}** code.
"""
    
    final_system_prompt = base_system_prompt_text
    if project_custom_context:
        print("\n----- Integrating Project Context into System Prompt -----")
        final_system_prompt = f"ADDITIONAL PROJECT CONTEXT TO CONSIDER:\n{project_custom_context}\n\n---\n\n{base_system_prompt_text}"
        print(f"System prompt updated with project context. New length: {len(final_system_prompt)}")
        # Log preview of final system prompt (be mindful of length and sensitivity)
        final_system_prompt_preview = final_system_prompt[:300].replace('\n', '[newline]')
        print(f"Final system prompt preview: {final_system_prompt_preview}...")
    else:
        print("\n----- No Project Context to Integrate -----")
        print(f"Using base system prompt. Length: {len(final_system_prompt)}")


    print(f"DEBUG: System Prompt start for LLM: {final_system_prompt[:150]}...") # Log start of system prompt
    
    # Generate textual description with enhanced GIS-awareness
    print("\n----- Step 3: Generating Textual Description -----")
    description_prompt = """
    Analyze the given code and provide a detailed description of:
    1. What the script does (its primary purpose)
    2. How it works (key steps or methodology)
    3. When it would be used (typical use cases)
    4. Any GIS-specific operations or analyses it performs
    5. Any spatial data types used (point, line, polygon, raster, etc.)
    6. Any relevant performance or scale considerations

    For GIS-specific code, be very detailed about:
    - The specific GIS operations (buffer, intersect, spatial join, etc.)
    - The coordinate systems or projections used (mention EPSG codes if present)
    - The spatial data formats processed (shapefiles, feature classes, rasters, GeoJSON, etc.)
    - The type of analysis (proximity, overlay, network, terrain, hydrology, etc.)
    - Any specialized spatial algorithms or techniques used

    If geodatabase operations or workspace management are involved, mention them.
    If dealing with raster data, mention resolution, resampling methods, or cell operations.
    If using specialized GIS libraries (ArcPy, PyQGIS, GeoPandas, GDAL/OGR), explain their role.

    Format your response as a well-structured paragraph. Be specific about operations and data types.
    Focus particularly on GIS operations and spatial analysis if present.
    Keep your response to 5-7 sentences, making them information-dense and technically precise.
    Remember: The entire response MUST be in {preferred_language}.
    """
    
    # Generate textual description with enhanced GIS-awareness
    description_result = await analyze_code_with_llm(
        code=request.code, 
        prompt=description_prompt, 
        provider=provider, 
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=final_system_prompt, # Use the potentially augmented system prompt
        is_gis_code=is_gis_code_detected
    )
    print(f"Description generation result: success={description_result['success']}")
    if description_result["success"]:
        documentation.textualDescription = description_result["result"]
        print(f"Description generated successfully. Length: {len(description_result['result'])} characters")
    else:
        print(f"Description generation failed: {description_result.get('error', 'Unknown error')}")
    
    # Generate parameters with enhanced GIS parameter detection
    print("\n----- Step 4: Generating Parameters -----")
    parameters_prompt = """
    Analyze the given code and extract all parameters with special focus on GIS parameters, organized as follows:
    
    IMPORTANT: Your response MUST be a valid JSON object that can be parsed. Structure it EXACTLY as shown in the example format at the end.

    1. INPUT PARAMETERS: List all input parameters with these details for each:
       - Name: Parameter name
       - Type: Data type (be very specific about GIS types like Point Feature Class, Polygon Feature Class, Line Feature Class, Raster Dataset, TIN, etc.)
       - Required: Whether it's required or optional (true/false)
       - Description: What the parameter does, including any restrictions
       - Constraints: Any constraints or limitations on the parameter values (e.g., coordinate system requirements, geometry validity requirements)

    2. OUTPUT PARAMETERS: List all output parameters with the same details
       For GIS outputs, specify the format precisely (e.g., GeoTIFF raster, File Geodatabase Feature Class, Shapefile)

    3. HARDCODED PARAMETERS: Identify any hardcoded values that could be parameters
       Look for coordinate systems (EPSG codes), buffer distances, threshold values, etc.

    For GIS code, pay extra attention to:
    - Spatial reference parameters (coordinate systems, EPSG codes, projection information)
    - Geometric parameters (buffer distances, tolerances, simplification thresholds)
    - Raster parameters (cell size, resampling method, mask values, NoData values)
    - Analysis parameters (search radius, distance method, interpolation method)
    - File paths and geodatabase locations (workspaces, feature datasets, catalog paths)
    - Transformation parameters (geographic/datum transformations, method codes)
    - Environment settings (extent, cell size, mask, processing extent)
    - SQL query parameters, attribute selections, or expression values
    
    Be extra thorough with ArcPy, QGIS, and GDAL parameter detection, checking both function arguments and tool parameters.
    For Python-based GIS code, also look for environment variables and configuration dictionaries.

    Format your response as a structured JSON object with these exact keys:
    {"input_parameters": [...], "output_parameters": [...], "hardcoded_parameters": [...]}

    Each parameter should have these exact keys: "name", "type", "required", "description", "constraints"
    Ensure all textual descriptions ('description' fields) within the JSON are generated in the target language: {preferred_language}. Technical identifiers like 'name' and 'type' should remain untranslated.
    
    Example of proper JSON format (follow this EXACTLY):
    ```json
    {
      "input_parameters": [
        {
          "name": "input_shapefile",
          "type": "Point Feature Class",
          "required": true,
          "description": "[Translated description of input point data]",
          "constraints": "EPSG:4326 coordinate system required"
        }
      ],
      "output_parameters": [
        {
          "name": "output_raster",
          "type": "GeoTIFF Raster",
          "required": true,
          "description": "[Translated description of analysis result raster]",
          "constraints": "300dpi minimum resolution"
        }
      ],
      "hardcoded_parameters": [
        {
          "name": "buffer_distance",
          "type": "float",
          "required": true,
          "description": "Hardcoded: [Translated description of distance value]",
          "constraints": "Set to 100.0 meters in line 42"
        }
      ]
    }
    ```
    
    Return ONLY the JSON object with no additional text.
    """
    
    # Generate parameters with enhanced GIS parameter detection
    parameters_result = await analyze_code_with_llm(
        code=request.code, 
        prompt=parameters_prompt, 
        provider=provider, 
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=final_system_prompt, # Use the potentially augmented system prompt
        is_gis_code=is_gis_code_detected
    )
    print(f"Parameters generation result: success={parameters_result['success']}")
    if parameters_result["success"]:
        try:
            # Try to extract JSON if it's embedded in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', parameters_result["result"])
            if json_match:
                parameters_text = json_match.group(1)
                print("Extracted JSON from markdown code block")
            else:
                parameters_text = parameters_result["result"]
                print("Using raw text as JSON")
            
            # Parse JSON
            print(f"Attempting to parse JSON: {parameters_text[:100]}...")
            parameters_data = json.loads(parameters_text)
            print("JSON successfully parsed")
            
            # Process all parameter types
            param_counts = {"input": 0, "output": 0, "hardcoded": 0}
            for param_type in ["input_parameters", "output_parameters", "hardcoded_parameters"]:
                for param_data in parameters_data.get(param_type, []):
                    param = DocumentationParameter(
                        name=param_data.get("name", ""),
                        type=param_data.get("type", "Unknown"),
                        description=param_data.get("description", ""),
                        required=param_data.get("required", True),
                        constraints=param_data.get("constraints", "")
                    )
                    
                    # Add a prefix to hardcoded parameters description
                    if param_type == "hardcoded_parameters" and not param.description.startswith("Hardcoded:"):
                        param.description = "Hardcoded: " + param.description
                        param_counts["hardcoded"] += 1
                    elif param_type == "input_parameters":
                        param_counts["input"] += 1
                    elif param_type == "output_parameters":
                        param_counts["output"] += 1
                    
                    documentation.parameters.append(param)
            
            print(f"Parameters processed: {param_counts['input']} input, {param_counts['output']} output, {param_counts['hardcoded']} hardcoded")
        except Exception as e:
            print(f"Error processing parameters: {str(e)}")
            # If JSON parsing failed, include the raw response for debugging
            if not documentation.parameters:
                documentation.parameters.append(DocumentationParameter(
                    name="error_parsing_parameters",
                    type="Error",
                    description=f"Error parsing parameters JSON: {str(e)}",
                    required=False,
                    constraints="Raw response was too large or malformed to display"
                ))
    else:
        print(f"Parameters generation failed: {parameters_result.get('error', 'Unknown error')}")
    
    # Generate workflow steps with enhanced GIS workflow understanding
    print("\n----- Step 5: Generating Workflow Steps -----")
    workflow_prompt = """
    Analyze the given code and extract a detailed workflow that explains the code execution as a series of steps, with special focus on GIS operations.
    
    IMPORTANT: Your response MUST be a valid JSON object that can be parsed. Structure it EXACTLY as shown in the example format at the end.

    For each workflow step, include:
    1. Step number as "id" (int)
    2. Name: A short title for the step that clearly indicates what GIS operation or data processing is happening
    3. Description: Detailed explanation of what happens in this step, including:
       - What specific GIS processes or operations are applied
       - What data transformations occur
       - What spatial algorithms are used
       - Any conditionals or special cases handled

    For GIS workflows, be sure to identify these specific phases:
    - Data loading/import phase (reading from files, geodatabases, web services)
    - Data validation phase (checking geometry validity, attribute completeness)
    - Data pre-processing/cleaning phase (repairing geometry, handling missing values)
    - Coordinate system definition or transformation phase (reprojection operations)
    - Core spatial analysis operations (geoprocessing tools, spatial operations)
    - Attribute processing (field calculations, statistics, classification)
    - Post-processing of results (generalization, simplification, symbol assignment)
    - Output generation/export phase (writing to files, geodatabases, visualization)

    Pay special attention to iterations and batch processing in GIS workflows.
    Identify any error handling or quality control measures specific to spatial data.
    Note any performance optimization techniques used for large spatial datasets.
    Highlight dependencies between steps and data flow through the workflow.

    Make sure to capture the logical flow of the program including conditionals, loops, and function calls.
    For GIS code, focus on the spatial operations and transformations.

    Format your response as a structured JSON object with this format:
    {"steps": [{"id": 1, "name": "...", "description": "..."}]}

    Ensure all textual descriptions ('description' fields) within the JSON are generated in the target language: {preferred_language}. Technical identifiers like 'id' and 'name' should remain untranslated.
    Include all significant steps in the workflow, typically 4-8 steps for most GIS scripts.
    
    Example of proper JSON format (follow this EXACTLY):
    ```json
    {
      "steps": [
        {
          "id": 1,
          "name": "Data Import",
          "description": "[Translated description of importing point data]"
        },
        {
          "id": 2,
          "name": "Coordinate System Transformation",
          "description": "[Translated description of reprojection]"
        }
      ]
    }
    ```
    
    Return ONLY the JSON object with no additional text.
    """
    
    # Generate workflow steps with enhanced GIS workflow understanding
    workflow_result = await analyze_code_with_llm(
        code=request.code, 
        prompt=workflow_prompt, 
        provider=provider, 
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=final_system_prompt, # Use the potentially augmented system prompt
        is_gis_code=is_gis_code_detected
    )
    print(f"Workflow generation result: success={workflow_result['success']}")
    if workflow_result["success"]:
        try:
            # Try to extract JSON if it's embedded in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', workflow_result["result"])
            if json_match:
                workflow_text = json_match.group(1)
                print("Extracted workflow JSON from markdown code block")
            else:
                workflow_text = workflow_result["result"]
                print("Using raw text as workflow JSON")
            
            # Parse JSON
            print(f"Attempting to parse workflow JSON: {workflow_text[:100]}...")
            workflow_data = json.loads(workflow_text)
            print("Workflow JSON successfully parsed")
            
            # Process workflow steps
            step_count = 0
            for step_data in workflow_data.get("steps", []):
                step = DocumentationWorkflowStep(
                    id=step_data.get("id", 0),
                    name=step_data.get("name", ""),
                    description=step_data.get("description", "")
                )
                documentation.workflow.steps.append(step)
                step_count += 1
            
            print(f"Workflow steps processed: {step_count} steps")
        except Exception as e:
            print(f"Error processing workflow: {str(e)}")
            # If JSON parsing failed, include the error information in the workflow
            if not documentation.workflow.steps:
                documentation.workflow.steps.append(DocumentationWorkflowStep(
                    id=1,
                    name="Error Parsing Workflow",
                    description=f"Error parsing workflow JSON: {str(e)}"
                ))
    else:
        print(f"Workflow generation failed: {workflow_result.get('error', 'Unknown error')}")
    
    # Generate tools used with comprehensive GIS tool detection
    print("\n----- Step 6: Generating Tools Used -----")
    tools_prompt = """
    Analyze the given code and extract information about all tools, functions, and methods used, with special focus on GIS-specific tools.
    
    IMPORTANT: Your response MUST be a valid JSON array that can be parsed. Structure it EXACTLY as shown in the example format at the end.

    List all function names, method calls, and GIS tools used in the code.
    For GIS code, identify:
    - Core GIS library functions (e.g., arcpy.Buffer_analysis, QgsProcessingAlgorithm, ogr.Geometry, geopandas.overlay)
    - Spatial operation functions (e.g., intersect, dissolve, buffer, union, clip, erase)
    - Raster analysis functions (e.g., focal statistics, map algebra, reclassify, hillshade, aspect, slope)
    - Coordinate transformation functions (e.g., project, transform_geometries, reproject)
    - Geodatabase operations (e.g., create_feature_class, add_field, create_relationship)
    - Spatial data I/O functions (e.g., read_file, to_file, ExportToWkt, Open, GetLayer)
    - Map generation or visualization functions (e.g., add_layer, symbolize, export_map)
    - Spatial statistics functions (e.g., nearest_neighbor, hotspot, cluster)
    - Terrain analysis functions (e.g., visibility, watershed, flow direction)
    - Network analysis functions (e.g., shortest_path, service_area)
    - Geoprocessing models or workflows (e.g., ModelBuilder tools, QGIS Processing algorithms)

    For ArcPy specifically, extract toolbox and tool name (e.g., "arcpy.analysis.Buffer", "arcpy.sa.Slope").
    For QGIS, note both core functions and Processing algorithms.
    For GDAL/OGR, note driver types and transformation options.
    For GeoPandas/Shapely, note both the method and the geometric operation.

    Categorize tools where possible as:
    - Data Management functions
    - Spatial Analysis functions
    - Geometric operations
    - Raster operations
    - Conversion tools
    - Visualization functions
    - 3D Analysis functions
    - Network Analysis functions
    - Spatial Statistics functions

    Format your response as a structured JSON array of strings:
    ["function_name1", "function_name2", ...]

    Focus on the most important functions and tools, excluding basic language constructs.
    Sort by importance, with the most significant GIS operations first, limited to the top 20 most important tools.
    If any descriptive text were needed within the JSON structure (though not requested here), it MUST be in {preferred_language}.
    
    Example of proper JSON format (follow this EXACTLY):
    ```json
    [
      "arcpy.Buffer_analysis",
      "geopandas.overlay",
      "QgsVectorLayer",
      "ogr.Open",
      "spatial_join"
    ]
    ```
    
    Return ONLY the JSON array with no additional text.
    Remember: Descriptions MUST be in {preferred_language} if they were requested (though they are not here).
    """
    
    # Generate tools used with comprehensive GIS tool detection
    tools_result = await analyze_code_with_llm(
        code=request.code, 
        prompt=tools_prompt, 
        provider=provider, 
        retry_on_failure=retry_on_failure,
        model_type=ModelType.CODE,
        system_prompt=final_system_prompt, # Use the potentially augmented system prompt
        is_gis_code=is_gis_code_detected
    )
    print(f"Tools detection result: success={tools_result['success']}")
    if tools_result["success"]:
        try:
            # Try to extract JSON if it's embedded in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', tools_result["result"])
            if json_match:
                tools_text = json_match.group(1)
                print("Extracted tools JSON from markdown code block")
            else:
                tools_text = tools_result["result"]
                print("Using raw text as tools JSON")
            
            # Parse JSON
            print(f"Attempting to parse tools JSON: {tools_text[:100]}...")
            tools_data = json.loads(tools_text)
            print("Tools JSON successfully parsed")
            print(f"Found {len(tools_data)} tools and functions")
            documentation.toolsUsed = tools_data
        except Exception as e:
            print(f"Error processing tools: {str(e)}")
            # If JSON parsing failed, include a default error entry
            documentation.toolsUsed = ["Error parsing tools - see backend logs for details"]
    else:
        print(f"Tools detection failed: {tools_result.get('error', 'Unknown error')}")
        documentation.toolsUsed = ["Tools detection failed - see backend logs for details"]
    
    # Check if any major section failed
    print("\n----- Step 7: Finalizing Documentation -----")
    all_success = all([
        description_result.get("success", False),
        parameters_result.get("success", False),
        workflow_result.get("success", False),
        tools_result.get("success", False)
    ])
    
    if not all_success:
        # Compile error messages
        error_msgs = []
        for section, result in [
            ("description", description_result), 
            ("parameters", parameters_result), 
            ("workflow", workflow_result), 
            ("tools", tools_result)
        ]:
            if not result.get("success"):
                error_msgs.append(f"{section}: {result.get('error', 'Unknown error')}")
        
        error_summary = "; ".join(error_msgs)
        print(f"Documentation generation incomplete. Errors: {error_summary}")
        return DocumentationGenerationResponse(
            success=False,
            error_message=f"Error generating documentation: {error_summary}",
            provider_used=provider
        )
    
    # Return the successful documentation response
    actual_provider = description_result.get("provider_used", provider)

    # MYA-13 Debug: Force a value if textualDescription is empty or None to test serialization
    if hasattr(documentation, 'textualDescription'):
        if not documentation.textualDescription: # Covers empty string and None if type allows None
            documentation.textualDescription = "[TEST DEBUG] Textual description was empty or None."
    else:
        # This case should ideally not happen if DocumentationResponse is well-defined
        print("[WARN] MYA-13 Debug: documentation object does not have textualDescription attribute.")

    print(f"Documentation generation completed successfully using provider: {actual_provider}")
    print("============== DOCUMENTATION GENERATION COMPLETED ==============\n")
    return DocumentationGenerationResponse(
        success=True,
        provider_used=actual_provider,
        language_used=preferred_language, # Pass the language used
        documentation=documentation
    )
