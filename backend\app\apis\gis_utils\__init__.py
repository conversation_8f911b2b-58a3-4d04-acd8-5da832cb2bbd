from fastapi import APIRouter

# Create empty router for compatibility
router = APIRouter()

# Dictionary of common GIS libraries and their descriptions
GIS_LIBRARIES = {
    # Core GIS processing libraries
    'arcpy': 'ArcGIS Python library for spatial data processing',
    'arcgis': 'ArcGIS API for Python',
    'qgis': 'QGIS Python API for spatial data processing',
    'geopandas': 'Python library for geospatial data handling',
    'shapely': 'Python library for manipulation and analysis of planar geometric objects',
    'fiona': 'Python library for reading and writing spatial data files',
    'rasterio': 'Python library for reading and writing geospatial raster data',
    'pyproj': 'Python interface to PROJ library for cartographic projections and coordinate transformations',
    'gdal': 'Geospatial Data Abstraction Library',
    'osgeo': 'Python bindings for GDAL/OGR and related libraries',
    'ogr': 'OGR Simple Features Library for vector data',
    
    # Analysis and specialized libraries
    'earthpy': 'Python library for working with spatial data',
    'georasters': 'Python library for working with geographic rasters',
    'pysal': 'Python Spatial Analysis Library',
    'rasterstats': 'Python library for summarizing geospatial raster datasets based on vector geometries',
    'xarray-spatial': 'Python library for labeled multi-dimensional arrays for geospatial processing',
    'libpysal': 'Core components of the Python Spatial Analysis Library',
    'esda': 'Exploratory Spatial Data Analysis',
    'geostatspy': 'Geostatistics and spatial data analysis',
    'spaghetti': 'Spatial Graphs: Networks, Topology, & Inference',
    'spopt': 'Spatial Optimization',
    'tobler': 'Areal interpolation library',
    'segregation': 'Spatial segregation measures',
    'mapclassify': 'Classification schemes for choropleth maps',
    'whitebox': 'Python frontend for WhiteboxTools GIS',
    'richdem': 'Terrain analysis',
    'momepy': 'Quantitative analysis of urban morphology',
    'movingpandas': 'Movement data handling and analysis',
    'pointpats': 'Point pattern analysis',
    
    # Remote sensing and raster analysis
    'sentinelsat': 'Sentinel satellite data access',
    'landsat-util': 'Landsat satellite data processing',
    'rio-toa': 'Top of Atmosphere calculations for remote sensing data',
    'rio-color': 'Color operations for raster data',
    'snuggs': 'Map algebra expressions with Numpy',
    'rio_cogeo': 'Cloud Optimized GeoTIFF creation',
    'rio-tiler': 'Raster tile creation',
    'terracotta': 'Lightweight geospatial raster data management',
    
    # Visualization libraries
    'cartopy': 'Cartographic plotting library',
    'folium': 'Interactive maps using Leaflet.js',
    'geoviews': 'GIS visualizations using HoloViews',
    'plotly': 'Interactive visualizations',
    'contextily': 'Web tile maps in static plots',
    'geopandas.plotting': 'Plotting tools for geopandas',
    'mplleaflet': 'Convert matplotlib plots to Leaflet web maps',
    'matplotlib': 'Plotting library often used for maps',
    'ipyleaflet': 'Interactive maps for Jupyter',
    'pydeck': 'Large-scale geospatial visualization',
    'bokeh': 'Interactive visualization library',
    
    # Geocoding and web services
    'geopy': 'Geocoding and geodesic distance calculations',
    'geocoder': 'Geocoding library',
    'cenpy': 'US Census API access',
    'osmnx': 'OpenStreetMap data access and analysis',
    'geonames': 'GeoNames API access',
    'pykml': 'KML file handling',
    'geobr': 'Brazilian spatial data access',
    'geojson': 'GeoJSON format handling',
    'geojsonio': 'GeoJSON to web map conversion',
    'topojson': 'TopoJSON format handling',
    
    # Database and storage
    'geoalchemy2': 'SQLAlchemy extension for spatial databases',
    'postgis': 'PostGIS spatial database',
    'spatialite': 'Spatialite spatial database',
    'rtree': 'Spatial indexing',
    'h3': 'Hexagonal hierarchical geospatial indexing',
    's2': 'S2 hierarchical geospatial indexing',
    
    # Additional specialized GIS libraries and extensions
    'arcpy.sa': 'ArcGIS Spatial Analyst extension',
    'arcpy.na': 'ArcGIS Network Analyst extension',
    'arcpy.mp': 'ArcGIS mapping module',
    'arcpy.da': 'ArcGIS data access module',
    'arcgis.gis': 'ArcGIS Online/Enterprise module',
    'arcgis.features': 'ArcGIS feature layer management',
    'arcgis.geometry': 'ArcGIS geometry operations',
    'arcgis.raster': 'ArcGIS raster operations',
    'arcgis.network': 'ArcGIS network analysis',
    'arcgis.geoanalytics': 'ArcGIS GeoAnalytics',
    'arcgis.geocoding': 'ArcGIS geocoding services',
    
    # QGIS-specific modules
    'qgis.core': 'QGIS core functionality',
    'qgis.gui': 'QGIS GUI components',
    'qgis.analysis': 'QGIS analysis tools',
    'qgis.PyQt': 'QGIS PyQt integration',
    'qgis.utils': 'QGIS utility functions',
    
    # ArcPy tools
    'arcpy.ddd': '3D Analyst tools',
    'arcpy.defense': 'Defense tools',
    'arcpy.geoai': 'GeoAI toolbox',
    'arcpy.igpk': 'Intelligence tools',
    'arcpy.mtg': 'Maritime tools',
    'arcpy.ia': 'Image Analyst tools',
    'arcpy.datareviewer': 'Data Reviewer tools',
    'arcpy.production': 'Production Mapping tools',
    'arcpy.aerospace': 'Aerospace tools',
    
    # Python GIS interfaces for web mapping
    'arcgis.mapping': 'ArcGIS web mapping',
    'mapbox': 'Mapbox API interface',
    'flask_googlemaps': 'Flask extension for Google Maps',
    'django.contrib.gis': 'Django GIS extension',
    'geopandas_view': 'GeoJSON viewer for GeoPandas',
    'geemap': 'Google Earth Engine API interface',
    'kepler.gl': 'Kepler.gl mapping for Jupyter',
    'leafmap': 'Interactive mapping with Leaflet',
    'streamlit_folium': 'Folium integration for Streamlit',
    'maplibre': 'MapLibre API interface',
    
    # Advanced spatial algorithms
    'skgeom': 'Computational geometry algorithms',
    'pyhull': 'Convex hull algorithms',
    'triangle': 'Triangulation library',
    'tetgen': 'Tetrahedralization library',
    'pymesh': '3D mesh processing',
    'pygeos': 'Fast geometry operations',
    'pydelatin': 'Terrain mesh generation',
    'spacecurve': 'Curve algorithms for spatial data',
    
    # Domain-specific GIS tools
    'salem': 'Glacier and climate data analysis',
    'ulmo': 'Clean water data access',
    'hydrotools': 'Hydrological analysis',
    'pyvista': '3D plotting and mesh analysis',
    'verde': 'Spatial gridding and interpolation',
    'untangle': 'Spatial network untangling',
    'spnetwork': 'Spatial network analysis',
    'pysolar': 'Solar radiation calculation',
    'urbanaccess': 'Urban accessibility tools',
    'meteostatsepy': 'Weather data tools',
    'satpy': 'Satellite data processing',
    'pysheds': 'Watershed delineation',
    'climata': 'Climate data access',
    'pygplates': 'Plate tectonics modeling',
    'ecohydrolib': 'Ecohydrology tools',
    'landlab': 'Earth surface dynamics modeling',
    
    # New or alternative open source libraries
    'pygeoapi': 'OGC API implementation',
    'geostream': 'Geospatial data streaming',
    'geotorch': 'GIS deep learning',
    'geospatial': 'Common geospatial utilities',
    'pycrs': 'CRS handling',
    'pymap3d': '3D coordinate transformations',
    'pyrosm': 'OpenStreetMap data parser',
    'geoalchemy': 'Geospatial extension for SQLAlchemy',
    'python-geohash': 'Geohashing utilities',
    'quadkey': 'Quadkey tile indexing',
    'geocube': 'Geospatial data cubes'
}

from typing import Optional, Dict, List, Set

def is_gis_parameter(name: str, type_hint: Optional[str] = None) -> bool:
    """Check if a parameter appears to be related to GIS based on its name or type hint"""
    import re
    
    # Common GIS parameter name patterns
    gis_parameter_patterns = [
        # General GIS terms
        r'(?:feature|shape|geom|spatial|map|layer|raster|extent|envelope|coord|proj|crs)',
        r'(?:buffer|zone|distance|radius|area|volume|elevation|height|depth)',
        r'(?:latitude|longitude|lat|lon|lng|x_coord|y_coord|z_coord)',
        r'(?:boundary|region|district|zone|territory|locality)',
        
        # File and data types
        r'(?:shapefile|featureclass|feature_class|geodatabase|gdb|mxd|lyr|kmz|kml)',
        r'(?:geotiff|tiff?|dem|dtm|dsm|lidar|las|grid|mosaic|img)',
        r'(?:gpkg|geojson|topojson|gml|wkt|wkb|shp|dbf|prj|shx)',
        
        # GIS operations
        r'(?:clip|intersect|union|dissolve|merge|erase|split|join)',
        r'(?:buffer|overlay|reproject|transform|convert|extract)',
        r'(?:interpolate|contour|hillshade|aspect|slope|viewshed)',
        
        # Attributes and properties
        r'(?:attribute|field|column|property|value|statistic)',
        
        # Database and web terms
        r'(?:postgis|spatialite|wms|wfs|wcs|wmts|tile)',
        
        # Remote sensing
        r'(?:band|sensor|satellite|landsat|sentinel|modis|imagery)',
        
        # Output formats
        r'(?:export|output|result)'
    ]
    
    # Check parameter name
    for pattern in gis_parameter_patterns:
        if re.search(pattern, name, re.IGNORECASE):
            return True
    
    # File extensions that indicate GIS data
    gis_extensions = [
        '.shp', '.gdb', '.mxd', '.kml', '.kmz', '.tif', '.tiff',
        '.img', '.dem', '.asc', '.las', '.laz', '.gpkg', '.geojson',
        '.gml', '.json', '.dbf', '.prj', '.shx', '.xml', '.grd',
        '.nc', '.hdr', '.mbtiles', '.vrt', '.fit', '.fits',
        '.jp2', '.j2k', '.ntf', '.nitf', '.ecw', '.sid',
        '.sqlite', '.db', '.osm', '.pbf'
    ]
    
    # Check for GIS file extensions in name
    for ext in gis_extensions:
        if ext in name.lower():
            return True
    
    # Check type hint if available
    if type_hint:
        gis_type_patterns = [
            # Basic geometry types
            r'(?:Feature|Geometry|Point|Line|LineString|Polygon|MultiPolygon|MultiPoint|MultiLineString)',
            r'(?:GeometryCollection|LinearRing)',
            
            # GIS data structures
            r'(?:Layer|Map|Spatial|Geographic|Coordinate|Projection|CRS|SpatialReference)',
            r'(?:GeoSeries|GeoDataFrame|RasterLayer|GeoTIFF|Shapefile)',
            r'(?:FeatureClass|FeatureLayer|FeatureSet|RecordSet)',
            
            # Libraries
            r'(?:GeoSeries|GeoDataFrame|Shapely|Fiona|GDAL|OGR)',
            r'(?:arcpy|qgis|geopandas|rasterio|pyproj)',
            
            # Data types
            r'(?:Raster|Vector|Mosaic|Catalog|Dataset|Workspace)',
            r'(?:TIN|DEM|DTM|DSM|Terrain|Surface)',
            
            # ArcGIS specific
            r'(?:Workspace|FeatureWorkspace|CoverageWorkspace)',
            r'(?:TableView|MapDocument|Layer|NetworkDataset)',
            
            # QGIS specific
            r'(?:QgsVectorLayer|QgsRasterLayer|QgsMapLayer)',
            r'(?:QgsFeature|QgsGeometry|QgsPoint|QgsCoordinateReferenceSystem)'
        ]
        
        for pattern in gis_type_patterns:
            if re.search(pattern, type_hint, re.IGNORECASE):
                return True
    
    return False

def identify_gis_library(library_name: str) -> Dict[str, str]:
    """
    Identify if a library is a known GIS library and return its description
    
    Args:
        library_name (str): The name of the library to check
        
    Returns:
        Dict[str, str]: A dictionary with library name and description, or empty if not a GIS library
    """
    library_lower = library_name.lower()
    
    # Direct match
    if library_lower in GIS_LIBRARIES:
        return {
            "name": library_name,
            "description": GIS_LIBRARIES[library_lower],
            "is_gis_library": True
        }
    
    # Check for partial matches (e.g., 'import geopandas as gpd' should match 'geopandas')
    for gis_lib, description in GIS_LIBRARIES.items():
        if gis_lib in library_lower:
            return {
                "name": library_name,
                "description": description,
                "is_gis_library": True
            }
    
    # Not a known GIS library
    return {
        "name": library_name,
        "description": "",
        "is_gis_library": False
    }

def identify_gis_function(function_name: str) -> bool:
    """
    Identify if a function name appears to be related to GIS operations
    
    Args:
        function_name (str): The name of the function to check
        
    Returns:
        bool: True if the function appears to be GIS-related, False otherwise
    """
    import re
    
    gis_function_patterns = [
        r'(?:spatial|geo|map|layer|feature|shape|buffer|intersect|dissolve|union|clip)',
        r'(?:project|reproject|transform|convert|calculate|analyze|extract|overlay)',
        r'(?:raster|vector|point|line|polygon|coordinate|projection|crs)',
        r'(?:interpolate|classify|symbolize|render|display|visualize)'
    ]
    
    for pattern in gis_function_patterns:
        if re.search(pattern, function_name, re.IGNORECASE):
            return True
    
    return False