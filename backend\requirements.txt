# CodeScribe GIS Backend Dependencies
# This file is AUTO-GENERATED from pyproject.toml - DO NOT EDIT MANUALLY
# To update dependencies, edit pyproject.toml and run: python scripts/generate_requirements.py
# Or use: uv pip compile pyproject.toml -o requirements.txt

annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
beautifulsoup4==4.12.3
    # via codescribe-gis-backend (pyproject.toml)
brotli==1.1.0
    # via fonttools
cachecontrol==0.14.3
    # via firebase-admin
cachetools==5.5.2
    # via google-auth
certifi==2025.6.15
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   cryptography
    #   weasyprint
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
colorama==0.4.6
    # via
    #   click
    #   tqdm
    #   uvicorn
cryptography==45.0.4
    # via pyjwt
cssselect2==0.8.0
    # via weasyprint
distro==1.9.0
    # via openai
fastapi==0.115.12
    # via codescribe-gis-backend (pyproject.toml)
firebase-admin==6.5.0
    # via codescribe-gis-backend (pyproject.toml)
fonttools==4.58.4
    # via weasyprint
google-ai-generativelanguage==0.6.10
    # via google-generativeai
google-api-core==2.25.1
    # via
    #   firebase-admin
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
    #   google-generativeai
google-api-python-client==2.173.0
    # via
    #   firebase-admin
    #   google-generativeai
google-auth==2.40.3
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-cloud-core==2.4.3
    # via
    #   google-cloud-firestore
    #   google-cloud-storage
google-cloud-firestore==2.21.0
    # via firebase-admin
google-cloud-storage==3.1.1
    # via firebase-admin
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-generativeai==0.8.3
    # via codescribe-gis-backend (pyproject.toml)
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.73.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
html5lib==1.1
    # via weasyprint
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jiter==0.10.0
    # via openai
lxml==5.4.0
    # via python-docx
msgpack==1.1.1
    # via cachecontrol
openai==1.58.1
    # via codescribe-gis-backend (pyproject.toml)
packaging==25.0
    # via pytesseract
pillow==10.4.0
    # via
    #   codescribe-gis-backend (pyproject.toml)
    #   pytesseract
    #   weasyprint
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-firestore
protobuf==5.29.5
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-firestore
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   fastapi
    #   google-generativeai
    #   openai
pydantic-core==2.33.2
    # via pydantic
pydyf==0.11.0
    # via weasyprint
pyjwt==2.10.1
    # via firebase-admin
pyparsing==3.2.3
    # via httplib2
pypdf==5.1.0
    # via codescribe-gis-backend (pyproject.toml)
pyphen==0.17.2
    # via weasyprint
pytesseract==0.3.13
    # via codescribe-gis-backend (pyproject.toml)
python-docx==1.1.2
    # via codescribe-gis-backend (pyproject.toml)
python-dotenv==1.0.1
    # via
    #   codescribe-gis-backend (pyproject.toml)
    #   uvicorn
python-multipart==0.0.9
    # via codescribe-gis-backend (pyproject.toml)
pyyaml==6.0.2
    # via uvicorn
requests==2.32.3
    # via
    #   codescribe-gis-backend (pyproject.toml)
    #   cachecontrol
    #   google-api-core
    #   google-cloud-storage
rsa==4.9.1
    # via google-auth
six==1.17.0
    # via html5lib
sniffio==1.3.1
    # via
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
starlette==0.46.2
    # via fastapi
tinycss2==1.4.0
    # via
    #   cssselect2
    #   weasyprint
tqdm==4.67.1
    # via
    #   google-generativeai
    #   openai
typing-extensions==4.14.0
    # via
    #   fastapi
    #   google-generativeai
    #   openai
    #   pydantic
    #   pydantic-core
    #   python-docx
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
uritemplate==4.2.0
    # via google-api-python-client
urllib3==2.5.0
    # via requests
uvicorn==0.34.2
    # via codescribe-gis-backend (pyproject.toml)
watchfiles==1.1.0
    # via uvicorn
weasyprint==62.3
    # via codescribe-gis-backend (pyproject.toml)
webencodings==0.5.1
    # via
    #   cssselect2
    #   html5lib
    #   tinycss2
websockets==15.0.1
    # via uvicorn
zopfli==0.2.3.post1
    # via fonttools
