from fastapi import APIRouter, HTTPException, BackgroundTasks, Response, Request
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import json
import os
import base64
import uuid
import time
import re
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from app.auth import AuthorizedUser

router = APIRouter(prefix="/export")

# Local storage replacement for Databutton storage
class LocalStorage:
    """Local file storage replacement for Databutton storage"""

    def __init__(self, storage_dir: str = "storage"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)

    def put(self, key: str, data: bytes) -> None:
        """Store binary data with the given key"""
        file_path = self.storage_dir / key
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'wb') as f:
            f.write(data)

    def get(self, key: str) -> bytes:
        """Retrieve binary data by key"""
        file_path = self.storage_dir / key
        if not file_path.exists():
            raise FileNotFoundError(f"Storage key not found: {key}")
        with open(file_path, 'rb') as f:
            return f.read()

    def list(self) -> List[Dict[str, str]]:
        """List all stored files"""
        files = []
        for file_path in self.storage_dir.rglob('*'):
            if file_path.is_file():
                relative_path = file_path.relative_to(self.storage_dir)
                files.append({
                    'name': str(relative_path),
                    'path': str(file_path),
                    'size': file_path.stat().st_size
                })
        return files

    def exists(self, key: str) -> bool:
        """Check if a key exists in storage"""
        file_path = self.storage_dir / key
        return file_path.exists()

    def delete(self, key: str) -> bool:
        """Delete a file by key"""
        file_path = self.storage_dir / key
        if file_path.exists():
            file_path.unlink()
            return True
        return False

# Initialize local storage
storage = LocalStorage()

class DocumentExportRequest(BaseModel):
    document_id: str
    document_data: Dict[str, Any]  # The document object from the frontend
    format: str  # 'docx', 'pdf', 'txt', or 'md'

class DocumentExportResponse(BaseModel):
    document_id: str
    format: str
    export_id: str
    status: str
    download_url: Optional[str] = None
    error: Optional[str] = None

# Dictionary to keep track of export tasks
export_tasks = {}


# Download a file directly from storage by key
@router.get("/download/{storage_key}")
async def download_from_storage(storage_key: str, response: Response):
    """Download a file directly from storage using its key."""
    print(f"Direct storage download request for key: {storage_key}")
    
    try:
        # Make sure the storage_key only contains valid characters
        if not re.match(r'^[a-zA-Z0-9._-]+$', storage_key):
            raise HTTPException(
                status_code=400, 
                detail="Invalid storage key format. Only letters, numbers, and the symbols '._-' are allowed."
            )
            
        # Now try downloading directly from storage to bypass API routing
        try:
            # Get the file directly from storage if available
            document_bytes = storage.get(storage_key)
            print(f"Successfully retrieved document from storage key: {storage_key}")
        except Exception as storage_error:
            error_msg = f"Failed to retrieve document from storage: {storage_error}"
            print(f"Error: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)
        
        # Try to determine the format from the storage key
        format = 'bin'  # Default
        if '_pdf_' in storage_key:
            format = 'pdf'
        elif '_docx_' in storage_key:
            format = 'docx'
        elif '_txt_' in storage_key:
            format = 'txt'
        elif '_md_' in storage_key:
            format = 'md'
        
        # Map format to content type
        content_types = {
            "pdf": "application/pdf",
            "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "txt": "text/plain",
            "md": "text/markdown",
            "bin": "application/octet-stream"
        }
        
        # Don't attempt to decode binary data - return it directly
        # Set content type and filename
        response.headers["Content-Type"] = content_types.get(format, "application/octet-stream")
        response.headers["Content-Disposition"] = f'attachment; filename="document.{format}"'
        
        # Important: Return bytes directly, without any string conversion/decoding
        # This prevents the UnicodeDecodeError when handling binary data (PDF/DOCX)
        return Response(content=document_bytes, media_type=content_types.get(format, "application/octet-stream"))
        
    except Exception as e:
        error_msg = f"Failed to retrieve document from storage: {e}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

# Generate document in specified format
def generate_document(document_data: Dict[str, Any], format: str) -> bytes:
    """Generate a document in the specified format."""
    print(f"generate_document called with format: {format}")
    
    # MYA-15: Ensure project_context is not included in the export
    if isinstance(document_data, dict):
        if 'project_context' in document_data:
            removed_context = document_data.pop('project_context', None)
            if removed_context is not None:
                print(f"Successfully removed 'project_context' from document_data for export.")
            else:
                print(f"'project_context' key was present but could not be removed (was None), or pop returned None unexpectedly.")
        else:
            print(f"'project_context' key not found in document_data for export. Nothing to remove.")
    else:
        print(f"document_data is not a dict, cannot remove 'project_context'. Type: {type(document_data)}")

    
    # Extract document contents
    try:
        # Handle if document_data is a string or None
        if isinstance(document_data, str):
            try:
                document_data = json.loads(document_data)
                print("Converted document_data from string to dict")
            except json.JSONDecodeError:
                print("Failed to parse document_data as JSON, using default values")
                document_data = {}
        elif document_data is None:
            print("document_data is None, using empty dict")
            document_data = {}
        
        # Extract basic metadata with fallbacks
        title = document_data.get('title', 'Untitled Document') if isinstance(document_data, dict) else 'Untitled Document'
        description = document_data.get('description', '') if isinstance(document_data, dict) else ''
        language = document_data.get('language', 'Unknown') if isinstance(document_data, dict) else 'Unknown'
        
        # Ensure content exists
        if isinstance(document_data, dict) and not document_data.get('content'):
            print("Adding empty content field to document_data")
            document_data['content'] = {'originalCode': ''}
        
        # Handle different date formats
        created_at = None
        updated_at = None
        if 'createdAt' in document_data:
            if isinstance(document_data['createdAt'], str):
                created_at = document_data['createdAt']
            else:
                # Handle potential Firestore timestamp objects
                created_at = str(document_data['createdAt'])
                
        if 'updatedAt' in document_data:
            if isinstance(document_data['updatedAt'], str):
                updated_at = document_data['updatedAt']
            else:
                updated_at = str(document_data['updatedAt'])
                
        # Default values if dates are not available
        if not created_at:
            created_at = datetime.now().isoformat()
        if not updated_at:
            updated_at = datetime.now().isoformat()
        
        # Handle tags
        tags = []
        if 'tags' in document_data and isinstance(document_data['tags'], list):
            tags = document_data['tags']
            
        print(f"Successfully extracted metadata: {title}, {language}, {created_at}")
    except Exception as e:
        print(f"Error extracting metadata: {e}")
        # Use defaults for critical fields
        title = 'Untitled Document'
        description = ''
        language = 'Unknown'
        created_at = datetime.now().isoformat()
        updated_at = datetime.now().isoformat()
        tags = []
    
    # Get original code safely
    original_code = ''
    try:
        if 'content' in document_data and document_data['content'] is not None:
            if isinstance(document_data['content'], dict):
                original_code = document_data['content'].get('originalCode', '')
            elif isinstance(document_data['content'], str):
                # Try to parse content if it's a string
                try:
                    content_obj = json.loads(document_data['content'])
                    if isinstance(content_obj, dict):
                        original_code = content_obj.get('originalCode', '')
                except json.JSONDecodeError:
                    original_code = document_data['content']  # Use as-is if not JSON
        print(f"Successfully extracted original code (length: {len(original_code)})")
    except Exception as e:
        print(f"Error extracting original code: {e}")
    
    # Parse the generated documentation if it exists (safely)
    doc_data = None
    try:
        if 'content' in document_data and isinstance(document_data['content'], dict):
            gen_doc = document_data['content'].get('generatedDocumentation', None)
            if gen_doc:
                if isinstance(gen_doc, str):
                    doc_data = json.loads(gen_doc)
                elif isinstance(gen_doc, dict):
                    doc_data = gen_doc
        print(f"Successfully parsed documentation data: {bool(doc_data)}")
    except Exception as e:
        print(f"Error parsing documentation: {e}")
    
    # Get HTML content
    html_content = None
    try:
        if 'content' in document_data and isinstance(document_data['content'], dict):
            html_wrapper = document_data['content'].get('html_wrapper', None)
            if html_wrapper:
                print(f"HTML wrapper found, type: {type(html_wrapper)}")
                if isinstance(html_wrapper, str):
                    try:
                        wrapper_obj = json.loads(html_wrapper)
                        html_content = wrapper_obj.get('html', None)
                        print(f"Parsed HTML content from string, length: {len(html_content) if html_content else 0}")
                    except json.JSONDecodeError:
                        print(f"Error parsing html_wrapper: not valid JSON")
                elif isinstance(html_wrapper, dict):
                    html_content = html_wrapper.get('html', None)
                    print(f"Found HTML content in dict, length: {len(html_content) if html_content else 0}")
    except Exception as e:
        print(f"Error extracting HTML content: {e}")
    
    # If we couldn't extract HTML, generate a basic HTML document
    if not html_content:
        print("Generating fallback HTML document with basic template")
        html_content = f"""<!DOCTYPE html>
        <html lang="en" class="light-theme">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="color-scheme" content="light">
            <title>{title}</title>
            <style>
                :root {{
                    --background: #f8fafc !important;
                    --foreground: #1e293b !important;
                    --muted: #f1f5f9 !important;
                    --muted-foreground: #64748b !important;
                    --border: #e2e8f0 !important;
                    --card: rgba(255, 255, 255, 0.8) !important;
                    --primary: #2563eb !important;
                    --primary-foreground: #ffffff !important;
                    color-scheme: light !important;
                }}
                
                body {{
                    font-family: Arial, sans-serif;
                    margin: 40px;
                    line-height: 1.6;
                    background-color: var(--background) !important;
                    color: var(--foreground) !important;
                }}
                h1, h2, h3 {{
                    color: var(--foreground) !important;
                }}
                pre {{
                    background-color: var(--muted);
                    padding: 20px;
                    border-radius: 5px;
                    overflow-x: auto;
                    white-space: pre-wrap !important;
                    word-wrap: break-word !important;
                    overflow-wrap: break-word !important;
                    font-family: monospace !important;
                }}
                code {{
                    font-family: monospace;
                }}
                .metadata {{
                    color: var(--muted-foreground);
                }}
            </style>
        </head>
        <body>
            <h1>{title}</h1>
            <p>{description}</p>
            
            <h2>Metadata</h2>
            <div class="metadata">
                <p>Language: {language}</p>
                <p>Created: {created_at}</p>
                <p>Last Updated: {updated_at}</p>
                <p>Tags: {', '.join(tags)}</p>
            </div>
            
            <h2>Original Code</h2>
            <pre><code>{original_code}</code></pre>
        </body>
        </html>"""
    
    # HTML formatting improvements for PDF and other formats
    # PDF and DOCX use the HTML content as base
    if format in ['pdf', 'docx']:
        print(f"Preparing HTML for {format} export")
        
        # Ensure the html has the light-theme class
        if 'class="light-theme"' not in html_content:
            print("Adding light-theme class to HTML for consistent styling")
            html_content = html_content.replace('<html', '<html class="light-theme"')
        
        # Add meta color-scheme tag if not present
        if '<meta name="color-scheme" content="light">' not in html_content:
            print("Adding color-scheme meta tag for consistent styling")
            html_content = html_content.replace('<head>', '<head>\n    <meta name="color-scheme" content="light">')
            
        # Make sure content has proper styling for print media
        if '@page {' not in html_content:
            print("Adding print styles to HTML content")
            style_tag_end = html_content.find('</style>')
            if style_tag_end > 0:
                print_styles = """
                /* Print-specific styles */
                @page {
                    margin: 1cm; /* Reduced margins from 2cm to 1cm */
                    size: A4;
                    @bottom-center {
                        content: counter(page) " / " counter(pages);
                        font-family: monospace;
                        font-weight: bold;
                        font-size: 10pt;
                        background-color: #f3f4f6;
                        border: 2px solid #000;
                        padding: 2px 10px;
                        border-radius: 0;
                        clip-path: polygon(0 0, 100% 0, 90% 100%, 10% 100%);
                    }
                }
                @media print {
                    pre, code {
                        white-space: pre-wrap !important;
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                        page-break-inside: avoid;
                    }
                    h1, h2, h3, h4, h5, h6, table, figure, pre {
                        page-break-inside: avoid;
                    }
                    table {
                        max-width: 100%;
                        width: 100%;
                        border-collapse: collapse;
                        table-layout: fixed;
                    }
                    /* Make sure table cells wrap text properly */
                    table td, table th {
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                        white-space: normal !important;
                        max-width: 100%;
                        padding: 8px;
                    }
                    .parameter-description {
                        word-break: break-word;
                        max-width: 100%;
                    }
                }
                """
                html_content = html_content[:style_tag_end] + print_styles + html_content[style_tag_end:]
        
        # Force light theme CSS variables if not present
        if '--background:' not in html_content or '--foreground:' not in html_content:
            print("Adding light theme CSS variables")
            style_tag_pos = html_content.find('<style>')
            if style_tag_pos > 0:
                light_theme_css = """
                /* Force light theme */
                :root {
                    --background: #f8fafc !important;
                    --foreground: #1e293b !important;
                    --muted: #f1f5f9 !important;
                    --muted-foreground: #64748b !important;
                    --border: #e2e8f0 !important;
                    --card: rgba(255, 255, 255, 0.8) !important;
                    --primary: #2563eb !important;
                    --primary-foreground: #ffffff !important;
                    color-scheme: light !important;
                }
                
                body {
                    background-color: var(--background) !important;
                    color: var(--foreground) !important;
                }
                
                pre, code {
                    white-space: pre-wrap !important;
                    word-wrap: break-word !important;
                    overflow-wrap: break-word !important;
                }
                """
                html_content = html_content.replace('<style>', '<style>' + light_theme_css)

        # Debug HTML content
        print(f"Final HTML size: {len(html_content)} bytes")
        print(f"HTML contains metadata section: {'<h2>Metadata</h2>' in html_content}")
        print(f"HTML contains description section: {'<h2>Description</h2>' in html_content}")
        print(f"HTML contains parameters section: {'<h2>Parameters</h2>' in html_content}")
        print(f"HTML contains functions section: {'<h2>Functions' in html_content}")
        print(f"HTML contains workflow section: {'<h2>Workflow</h2>' in html_content}")
        print(f"HTML contains code section: {'<h2>Original Code</h2>' in html_content}")

    # Generate PDF from HTML
    if format == 'pdf':
        try:
            from weasyprint import HTML, CSS
            print("weasyprint is installed and available")
            
            # Generate PDF using WeasyPrint with the optimized HTML content
            print("Generating PDF from HTML content")
            pdf_bytes = HTML(string=html_content).write_pdf()
            print(f"Successfully generated PDF (size: {len(pdf_bytes)} bytes)")
            return pdf_bytes
        except Exception as e:
            print(f"Error generating PDF: {e}")
            raise RuntimeError(f"Failed to generate PDF: {e}")
    
    # Generate DOCX from HTML        
    elif format == 'docx':
        try:
            import io
            from docx import Document
            from docx.shared import Inches, Pt, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml.ns import qn
            from bs4 import BeautifulSoup
            print("Successfully imported all required docx modules")
            
            # Create a new Document from scratch instead of using html2docx
            print("Creating DOCX using BeautifulSoup and python-docx")
            doc = Document()
            
            # Get the HTML content using BeautifulSoup for parsing
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Add title
            doc.add_heading(title, 0)
            
            # Create sections based on h2 headings in HTML
            sections = [
                {'name': 'Description', 'tag': 'description'},
                {'name': 'Parameters', 'tag': 'parameters'},
                {'name': 'Functions', 'tag': 'functions'},
                {'name': 'Workflow', 'tag': 'workflow'},
                {'name': 'Metadata', 'tag': 'metadata'},
                {'name': 'Original Code', 'tag': 'original-code'}
            ]
            
            # Function to get section content
            def get_section_content(section_name):
                heading = soup.find(['h1', 'h2'], string=section_name)
                if not heading:
                    # Try with case-insensitive match
                    for h in soup.find_all(['h1', 'h2']):
                        if h.get_text().strip().lower() == section_name.lower():
                            heading = h
                            break
                
                if not heading:
                    print(f"Warning: Section {section_name} not found in HTML")
                    return None
                
                content = []
                for sibling in heading.find_next_siblings():
                    if sibling.name in ['h1', 'h2']:
                        break
                    content.append(sibling)
                return content
            
            # Add description section
            description_content = get_section_content('Description')
            if description_content:
                doc.add_heading('Description', 1)
                text = '\n'.join([elem.get_text().strip() for elem in description_content])
                doc.add_paragraph(text)
            elif description:  # Fallback to description from JSON
                doc.add_heading('Description', 1)
                doc.add_paragraph(description)
            
            # Add parameters section with table
            parameters_content = get_section_content('Parameters')
            if parameters_content:
                doc.add_heading('Parameters', 1)
                
                # Look for tables in the parameters section
                tables = []
                for elem in parameters_content:
                    if elem.name == 'table':
                        tables.append(elem)
                
                if tables:
                    for table_elem in tables:
                        # Get table headers
                        headers = [th.get_text().strip() for th in table_elem.find_all('th')]
                        if not headers:
                            headers = ['Name', 'Type', 'Description', 'Required', 'Constraints']
                        
                        # Create table in docx
                        rows = table_elem.find_all('tr')
                        if rows:
                            table = doc.add_table(rows=len(rows), cols=len(headers))
                            table.style = 'Table Grid'
                            
                            # Add headers
                            hdr_cells = table.rows[0].cells
                            for i, text in enumerate(headers):
                                hdr_cells[i].text = text
                                # Ensure there's a valid run before accessing it
                                if hdr_cells[i].paragraphs[0].runs:
                                    run = hdr_cells[i].paragraphs[0].runs[0]
                                    run.bold = True
                                else:
                                    # If no runs exist, create a new paragraph with bold text
                                    p = hdr_cells[i].paragraphs[0].clear()
                                    p = hdr_cells[i].add_paragraph(text)
                                    p.runs[0].bold = True
                            
                            # Add data rows
                            for i, row in enumerate(rows[1:], 1):
                                cells = row.find_all(['td', 'th'])
                                for j, cell in enumerate(cells):
                                    if j < len(headers):  # Ensure we don't go out of bounds
                                        table.cell(i, j).text = cell.get_text().strip()
                else:
                    # If no table found, just add the text content
                    text = '\n'.join([elem.get_text().strip() for elem in parameters_content])
                    doc.add_paragraph(text)
            
            # Add functions section
            functions_content = get_section_content('Functions')
            if functions_content:
                doc.add_heading('Functions', 1)
                for elem in functions_content:
                    if elem.name == 'h3':
                        # Add function name as subheading
                        doc.add_heading(elem.get_text().strip(), 2)
                    elif elem.name == 'pre':
                        # Add function code with monospace font
                        code_text = elem.get_text().strip()
                        p = doc.add_paragraph()
                        code_run = p.add_run(code_text)
                        code_run.font.name = 'Courier New'
                        code_run.font.size = Pt(9)
                    else:
                        # Add regular text
                        doc.add_paragraph(elem.get_text().strip())
            
            # Add workflow section
            workflow_content = get_section_content('Workflow')
            if workflow_content:
                doc.add_heading('Workflow', 1)
                for elem in workflow_content:
                    # For workflow diagrams, add a note that they're visible in HTML/PDF
                    if elem.name == 'div' and 'workflow-diagram' in elem.get('class', []):
                        doc.add_paragraph('Workflow diagram (best viewed in HTML or PDF format)')
                    elif elem.name in ['p', 'div']:
                        doc.add_paragraph(elem.get_text().strip())
                    elif elem.name in ['h3', 'h4']:
                        doc.add_heading(elem.get_text().strip(), level=2 if elem.name=='h3' else 3)
            
            # Add metadata section
            doc.add_heading('Metadata', 1)
            doc.add_paragraph(f"Language: {language}")
            doc.add_paragraph(f"Created: {created_at}")
            doc.add_paragraph(f"Last Updated: {updated_at}")
            doc.add_paragraph(f"Tags: {', '.join(tags) if tags else 'None'}")
            
            # Add original code section
            doc.add_heading('Original Code', 1)
            code_para = doc.add_paragraph()
            code_run = code_para.add_run(original_code)
            code_run.font.name = 'Courier New'
            code_run.font.size = Pt(9)
            
            # Add brutalist page numbers to footer
            sections = doc.sections
            for section in sections:
                footer = section.footer
                footer_para = footer.add_paragraph()
                footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                footer_para.style = 'Footer'
                run = footer_para.add_run("Page ")
                run.font.name = 'Courier New'
                run.font.bold = True
                
                # Add page number
                try:
                    run = footer_para.add_run()
                    # Check if run has add_field method before calling it
                    if hasattr(run, 'add_field'):
                        run.add_field("PAGE")
                        run.font.name = 'Courier New'
                        run.font.bold = True
                    else:
                        # Alternative approach if add_field not available
                        run.text = "#"
                        run.font.name = 'Courier New'
                        run.font.bold = True
                    
                    # Add total pages
                    run = footer_para.add_run(" of ")
                    run.font.name = 'Courier New'
                    run.font.bold = True
                    
                    run = footer_para.add_run()
                    # Check if run has add_field method before calling it
                    if hasattr(run, 'add_field'):
                        run.add_field("NUMPAGES")
                        run.font.name = 'Courier New'
                        run.font.bold = True
                    else:
                        # Alternative approach if add_field not available
                        run.text = "#"
                        run.font.name = 'Courier New'
                        run.font.bold = True
                except Exception as page_error:
                    print(f"Warning: Could not add page numbers to footer: {page_error}")
                    # Add simple text as fallback
                    try:
                        footer_para.text = "Document generated by CodeScribe GIS"
                    except:
                        pass
            
            # Apply custom styles
            # Set main document title to be larger
            for paragraph in doc.paragraphs:
                if paragraph.style.name == 'Title':
                    for run in paragraph.runs:
                        run.font.size = Pt(24)
                        run.font.color.rgb = RGBColor(0x25, 0x63, 0xEB)  # Primary blue color
                elif paragraph.style.name == 'Heading 1':
                    for run in paragraph.runs:
                        run.font.size = Pt(18)
                        run.font.color.rgb = RGBColor(0x1E, 0x29, 0x3B)  # Dark foreground color
                elif paragraph.style.name == 'Heading 2':
                    for run in paragraph.runs:
                        run.font.size = Pt(16)
            
            # Save to buffer
            buffer = io.BytesIO()
            doc.save(buffer)
            buffer.seek(0)
            docx_bytes = buffer.getvalue()
            
            print(f"Successfully generated DOCX document with all sections (size: {len(docx_bytes)} bytes)")
            return docx_bytes
            
        except Exception as e:
            print(f"Error generating DOCX: {e}")
            # Create a basic fallback DOCX document with at least the main sections
            try:
                print("Creating enhanced fallback DOCX document")
                doc = Document()
                doc.add_heading(title, 0)
                
                # Add description
                doc.add_heading('Description', 1)
                doc.add_paragraph(description or "No description available")
                
                # Add metadata
                doc.add_heading('Metadata', 1)
                doc.add_paragraph(f"Language: {language}")
                doc.add_paragraph(f"Created: {created_at}")
                doc.add_paragraph(f"Last Updated: {updated_at}")
                doc.add_paragraph(f"Tags: {', '.join(tags) if tags else 'None'}")
                
                # Try to parse parameters from data
                if doc_data and isinstance(doc_data, dict) and 'parameters' in doc_data:
                    doc.add_heading('Parameters', 1)
                    if isinstance(doc_data['parameters'], list) and doc_data['parameters']:
                        # Create table for parameters
                        table = doc.add_table(rows=len(doc_data['parameters'])+1, cols=5)
                        table.style = 'Table Grid'
                        
                        # Add headers
                        headers = ['Name', 'Type', 'Description', 'Required', 'Constraints']
                        hdr_cells = table.rows[0].cells
                        for i, text in enumerate(headers):
                            hdr_cells[i].text = text
                            # Ensure there's a valid run before accessing it
                            if hdr_cells[i].paragraphs[0].runs:
                                run = hdr_cells[i].paragraphs[0].runs[0]
                                run.bold = True
                            else:
                                # If no runs exist, create a new paragraph with bold text
                                p = hdr_cells[i].paragraphs[0].clear()
                                p = hdr_cells[i].add_paragraph(text)
                                p.runs[0].bold = True
                        
                        # Add parameter data
                        for i, param in enumerate(doc_data['parameters'], 1):
                            row_cells = table.rows[i].cells
                            row_cells[0].text = param.get('name', '')
                            row_cells[1].text = param.get('type', '')
                            row_cells[2].text = param.get('description', '')
                            row_cells[3].text = 'Yes' if param.get('required') else 'No'
                            row_cells[4].text = param.get('constraints', '')
                
                # Try to add functions section if available
                if doc_data and isinstance(doc_data, dict) and 'functions' in doc_data:
                    doc.add_heading('Functions', 1)
                    for func in doc_data['functions']:
                        if isinstance(func, dict):
                            doc.add_heading(func.get('name', 'Unknown Function'), 2)
                            doc.add_paragraph(func.get('description', 'No description available'))
                
                # Add original code
                doc.add_heading('Original Code', 1)
                code_para = doc.add_paragraph()
                code_run = code_para.add_run(original_code)
                code_run.font.name = 'Courier New'
                
                # Add brutalist page numbers to footer
                sections = doc.sections
                for section in sections:
                    footer = section.footer
                    footer_para = footer.add_paragraph()
                    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    run = footer_para.add_run("Page ")
                    run.font.name = 'Courier New'
                    run.font.bold = True
                    
                    try:
                        run = footer_para.add_run()
                        # Check if run has add_field method before calling it
                        if hasattr(run, 'add_field'):
                            run.add_field("PAGE")
                            run.font.bold = True
                        else:
                            # Alternative approach if add_field not available
                            run.text = "1"
                            run.font.bold = True
                        
                        run = footer_para.add_run(" of ")
                        run.font.name = 'Courier New'
                        run.font.bold = True
                        
                        run = footer_para.add_run()
                        # Check if run has add_field method before calling it
                        if hasattr(run, 'add_field'):
                            run.add_field("NUMPAGES")
                            run.font.bold = True
                        else:
                            # Alternative approach if add_field not available
                            run.text = "X"
                            run.font.bold = True
                    except Exception as page_error:
                        print(f"Warning: Could not add page numbers to fallback footer: {page_error}")
                        # Add simple text as fallback
                        try:
                            footer_para.text = "Document generated by CodeScribe GIS"
                        except:
                            pass
                
                buffer = io.BytesIO()
                doc.save(buffer)
                buffer.seek(0)
                fallback_bytes = buffer.getvalue()
                
                print(f"Successfully created enhanced fallback DOCX (size: {len(fallback_bytes)} bytes)")
                return fallback_bytes
            except Exception as fallback_error:
                print(f"Error creating fallback DOCX: {fallback_error}")
                raise RuntimeError(f"Failed to generate DOCX: {e}, fallback also failed: {fallback_error}")
    
    # Generate Plain Text
    elif format == 'txt':
        try:
            print("Generating plain text document")
            from bs4 import BeautifulSoup
            
            # Extract text from HTML if available
            if html_content:
                # Parse HTML to extract text content
                soup = BeautifulSoup(html_content, 'html.parser')
                text_parts = []
                
                # Add title
                text_parts.append(f"{title}\n{'='*len(title)}\n")
                
                # Add description if available
                if description:
                    text_parts.append(f"{description}\n\n")
                
                # Add metadata
                text_parts.append("METADATA\n--------\n")
                text_parts.append(f"Language: {language}\n")
                text_parts.append(f"Created: {created_at}\n")
                text_parts.append(f"Last Updated: {updated_at}\n")
                text_parts.append(f"Tags: {', '.join(tags) if tags else 'None'}\n\n")
                
                # Add sections from HTML content
                for heading in soup.find_all(['h2', 'h3']):
                    section_title = heading.get_text().strip()
                    if section_title not in ['Metadata', 'Original Code']:
                        text_parts.append(f"{section_title.upper()}\n{'-'*len(section_title)}\n")
                        section_content = []
                        for sibling in heading.find_next_siblings():
                            if sibling.name in ['h2', 'h3']:
                                break
                            section_content.append(sibling.get_text().strip())
                        text_parts.append('\n'.join(section_content) + '\n\n')
                
                # Add original code
                text_parts.append("ORIGINAL CODE\n------------\n")
                text_parts.append(f"{original_code}\n")
                
                # Combine all text parts
                text_content = '\n'.join(text_parts)
            else:
                # Create basic text if HTML not available
                text_content = f"{title}\n{'='*len(title)}\n\n"
                if description:
                    text_content += f"{description}\n\n"
                text_content += f"METADATA\n--------\n"
                text_content += f"Language: {language}\n"
                text_content += f"Created: {created_at}\n"
                text_content += f"Last Updated: {updated_at}\n"
                text_content += f"Tags: {', '.join(tags) if tags else 'None'}\n\n"
                text_content += f"ORIGINAL CODE\n------------\n"
                text_content += f"{original_code}\n"
            
            print(f"Successfully generated text document (size: {len(text_content)} bytes)")
            return text_content.encode('utf-8')
            
        except Exception as e:
            print(f"Error generating text document: {e}")
            # Simple fallback
            basic_text = f"{title}\n\n{description}\n\nCode:\n{original_code}"
            return basic_text.encode('utf-8')
    
    # Generate Markdown
    elif format == 'md':
        try:
            print("Generating markdown document")
            from bs4 import BeautifulSoup
            
            # Create markdown content
            md_parts = []
            
            # Add title and description
            md_parts.append(f"# {title}\n")
            if description:
                md_parts.append(f"{description}\n")
            
            # Add metadata section
            md_parts.append("## Metadata\n")
            md_parts.append(f"- **Language:** {language}")
            md_parts.append(f"- **Created:** {created_at}")
            md_parts.append(f"- **Last Updated:** {updated_at}")
            md_parts.append(f"- **Tags:** {', '.join(tags) if tags else 'None'}\n")
            
            # Try to extract structured content from HTML if available
            if html_content:
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Process sections (except metadata and original code which we handle separately)
                for heading in soup.find_all(['h2', 'h3']):
                    section_title = heading.get_text().strip()
                    if section_title not in ['Metadata', 'Original Code']:
                        md_parts.append(f"## {section_title}\n")
                        
                        # Special handling for parameters section (convert tables)
                        if section_title == 'Parameters' and heading.find_next('table'):
                            table = heading.find_next('table')
                            headers = [th.get_text().strip() for th in table.find_all('th')]
                            md_parts.append('| ' + ' | '.join(headers) + ' |')
                            md_parts.append('| ' + ' | '.join(['---' for _ in headers]) + ' |')
                            
                            for row in table.find_all('tr')[1:]:  # Skip header row
                                cells = [td.get_text().strip() for td in row.find_all('td')]
                                md_parts.append('| ' + ' | '.join(cells) + ' |')
                            md_parts.append('')  # Add empty line after table
                        else:
                            # Standard content extraction for other sections
                            section_content = []
                            for sibling in heading.find_next_siblings():
                                if sibling.name in ['h2', 'h3']:
                                    break
                                # Handle lists
                                if sibling.name == 'ul':
                                    for li in sibling.find_all('li'):
                                        section_content.append(f"- {li.get_text().strip()}")
                                # Handle paragraphs
                                elif sibling.name == 'p':
                                    section_content.append(sibling.get_text().strip())
                                # Handle divs (e.g. for workflow steps)
                                elif sibling.name == 'div' and 'workflow-step' in sibling.get('class', []):
                                    step_title = sibling.find(class_='step-title')
                                    step_desc = sibling.find(class_='step-description')
                                    if step_title:
                                        section_content.append(f"### {step_title.get_text().strip()}")
                                    if step_desc:
                                        section_content.append(step_desc.get_text().strip())
                            
                            md_parts.append('\n'.join(section_content) + '\n')
            
            # Add original code section
            md_parts.append("## Original Code\n")
            md_parts.append(f"```{language.lower()}\n{original_code}\n```\n")
            
            # Combine all markdown parts
            md_content = '\n'.join(md_parts)
            
            print(f"Successfully generated markdown document (size: {len(md_content)} bytes)")
            return md_content.encode('utf-8')
            
        except Exception as e:
            print(f"Error generating markdown: {e}")
            # Simple fallback markdown
            basic_md = f"# {title}\n\n{description}\n\n## Code\n```\n{original_code}\n```"
            return basic_md.encode('utf-8')
    
    else:
        raise ValueError(f"Unsupported format: {format}")


import hashlib # For creating a hash of document data for robust caching if needed

# Helper function to create a cache-aware storage key
def create_cache_storage_key(document_id: str, doc_format: str, document_data: Dict[str, Any]) -> str:
    # Use updatedAt for versioning if available, otherwise use a hash of essential content.
    # Fallback to a simple timestamp if neither is robustly available.
    updated_at_or_version_marker = ""
    if isinstance(document_data, dict):
        updated_at = document_data.get('updatedAt')
        if updated_at:
            # Ensure updatedAt is a string and suitable for a filename
            updated_at_or_version_marker = str(updated_at).replace(":", "-").replace(".", "-").replace(" ", "_").replace("+", "Z")
        else:
            # As a fallback, create a hash of the title and description if updatedAt is missing
            # This is a basic versioning. More sophisticated hashing of content might be needed.
            title = document_data.get('title', '')
            description = document_data.get('description', '')
            # Get original code safely for hashing
            original_code = ''
            if 'content' in document_data and document_data['content'] is not None:
                if isinstance(document_data['content'], dict):
                    original_code = document_data['content'].get('originalCode', '')
                elif isinstance(document_data['content'], str):
                    try:
                        content_obj = json.loads(document_data['content'])
                        if isinstance(content_obj, dict):
                            original_code = content_obj.get('originalCode', '')
                    except json.JSONDecodeError:
                        original_code = document_data['content']

            hash_input = f"{title}{description}{original_code}"
            updated_at_or_version_marker = hashlib.md5(hash_input.encode('utf-8')).hexdigest()[:8] # Short hash
    
    safe_document_id = re.sub(r'[^a-zA-Z0-9._-]', '', document_id)
    # Include a version marker in the key for cache invalidation
    return f"cached_exports_{safe_document_id}_{doc_format}_{updated_at_or_version_marker}"

@router.post("/document")
async def export_document(request: DocumentExportRequest, background_tasks: BackgroundTasks, user: AuthorizedUser): # Added AuthorizedUser
    """Export a document in the specified format, using cache if available."""
    """Export a document in the specified format."""
    print(f"Export document request received: {request.document_id}, format: {request.format}")
    
    # Check if format is supported
    supported_formats = ['pdf', 'docx', 'txt', 'md']
    if request.format not in supported_formats:
        error_msg = f"Unsupported format: {request.format}. Supported formats are: {', '.join(supported_formats)}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=400, detail=error_msg)
    

    # --- Caching Logic ---
    # Create a cache key based on document_id, format, and content version (e.g., updatedAt or hash)
    # The document_data from the request should represent the *current* state of the document.
    cache_storage_key = create_cache_storage_key(request.document_id, request.format, request.document_data)
    print(f"Attempting to use cache with storage key: {cache_storage_key}")

    try:
        # Check if the cached file exists in storage
        all_files = storage.list()
        found_file = next((f for f in all_files if f['name'] == cache_storage_key), None)
        if found_file:
            print(f"Cache hit: Found existing export with key {cache_storage_key}")
            # If found, return a completed response with the direct download URL
            # The export_id for a cached item can be a derivative of its storage key for uniqueness if needed
            # or a new one if the frontend/status endpoint expects unique export_ids for polling.
            # For simplicity, let's return a unique export_id even for cached items,
            # but immediately mark it as "completed" in a temporary task structure or directly return full info.
            
            # Option 1: Return immediately with full info (no polling needed from frontend for this)
            return DocumentExportResponse(
                document_id=request.document_id,
                format=request.format,
                export_id=f"cached_{str(uuid.uuid4())}", # Provide a unique ID for the "transaction"
                status="completed", # Indicate it's already done
                download_url=f"/api/export/download/{cache_storage_key}" 
            )
        else:
            print(f"Cache miss for key: {cache_storage_key}. Generating new export.")
    except Exception as e: 
        print(f"Cache check failed (assuming miss) for key {cache_storage_key}: {e}")
        # Proceed to generate if any error during cache check
        pass
    # --- End Caching Logic ---

    export_id = str(uuid.uuid4())
    # Use the cache_storage_key for storing the newly generated file.
    final_storage_key_for_generation = cache_storage_key

    export_tasks[export_id] = {
        "document_id": request.document_id,
        "format": request.format,
        "status": "pending",
        "created_at": time.time(),
        "storage_key": final_storage_key_for_generation, # Use the cache-aware key
    }

    def generate_and_store_document_task(): # Renamed for clarity
        try:
            print(f"Starting background task for export_id: {export_id}, storage_key: {final_storage_key_for_generation}")
            document_bytes = generate_document(request.document_data, request.format)
            print(f"Storing document in storage key: {final_storage_key_for_generation}")
            storage.put(final_storage_key_for_generation, document_bytes)
            export_tasks[export_id]["status"] = "completed"
            print(f"Export task completed: {export_id}")
        except Exception as e:
            print(f"Error in background task {export_id}: {e}")
            export_tasks[export_id]["status"] = "failed"
            export_tasks[export_id]["error"] = str(e)

    background_tasks.add_task(generate_and_store_document_task)

    return DocumentExportResponse(
        document_id=request.document_id,
        format=request.format,
        export_id=export_id,
        status="pending"
        # Download URL will be provided by the status endpoint once completed
    )


@router.get("/document/{export_id}/status")
async def get_export_status(export_id: str):
    """Get the status of an export task."""
    print(f"Status check for export_id: {export_id}")
    
    if export_id not in export_tasks:
        error_msg = f"Export task not found: {export_id}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=404, detail=error_msg)
    
    task = export_tasks[export_id]
    response = DocumentExportResponse(
        document_id=task["document_id"],
        format=task["format"],
        export_id=export_id,
        status=task["status"]
    )
    
    if task["status"] == "completed":
        # Generate a download URL for the document
        # Provide a direct route to storage for more reliable downloads
        storage_key = task.get("storage_key")
        if storage_key:
            # Use direct storage download endpoint
            response.download_url = f"/api/export/download/{storage_key}"
        else:
            # Fallback to document endpoint if no storage key is available
            response.download_url = f"/api/export/document/{export_id}/download"
    elif task["status"] == "failed" and "error" in task:
        response.error = task["error"]
    
    return response


@router.get("/document/{export_id}/download")
async def download_document(export_id: str, response: Response):
    """Download a previously generated document."""
    print(f"Download request for export_id: {export_id}")
    
    if export_id not in export_tasks:
        error_msg = f"Export task not found: {export_id}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=404, detail=error_msg)
    
    task = export_tasks[export_id]
    if task["status"] != "completed":
        error_msg = f"Export task not completed: {export_id}. Status: {task['status']}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=400, detail=error_msg)
    
    # Get the document from storage
    storage_key = task["storage_key"]
    try:
        document_bytes = storage.get(storage_key)
    except Exception as e:
        error_msg = f"Failed to retrieve document: {e}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)
    
    # Set appropriate content type and filename
    document_id = task["document_id"]
    format = task["format"]
    
    content_types = {
        "pdf": "application/pdf",
        "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "txt": "text/plain",
        "md": "text/markdown"
    }
    
    extensions = {
        "pdf": "pdf",
        "docx": "docx",
        "txt": "txt",
        "md": "md"
    }
    
    # Set content type and filename
    response.headers["Content-Type"] = content_types.get(format, "application/octet-stream")
    response.headers["Content-Disposition"] = f'attachment; filename="{document_id}.{extensions.get(format, "bin")}"'
    
    return document_bytes


@router.delete("/document/{export_id}")
async def delete_export(export_id: str):
    """Delete an export task and its associated document."""
    print(f"Delete request for export_id: {export_id}")
    
    if export_id not in export_tasks:
        error_msg = f"Export task not found: {export_id}"
        print(f"Error: {error_msg}")
        raise HTTPException(status_code=404, detail=error_msg)
    
    task = export_tasks[export_id]
    
    # Delete the document from storage if it exists
    if task["status"] == "completed":
        storage_key = task["storage_key"]
        try:
            # Check if the document exists before trying to delete it
            if storage.exists(storage_key):
                print(f"Deleting document from storage: {storage_key}")
                storage.delete(storage_key)
                print(f"Successfully deleted document: {storage_key}")
            else:
                print(f"Document not found in storage: {storage_key}")
        except Exception as e:
            print(f"Warning: Failed to delete document from storage: {e}")
    
    # Remove the task from our tracking dictionary
    del export_tasks[export_id]
    
    return {"success": True, "message": f"Export task {export_id} deleted successfully"}