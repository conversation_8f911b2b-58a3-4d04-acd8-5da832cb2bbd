import { auth } from "app";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Logout() {
  const navigate = useNavigate();
  
  useEffect(() => {
    const logout = async () => {
      await auth.signOut();
      // Navigate to home using direct window.location for reliable navigation
      setTimeout(() => {
        const basePath = window.location.pathname.endsWith('/') ? 
          window.location.pathname : 
          window.location.pathname + '/';
        window.location.href = basePath.split('/logout')[0] + '/';
      }, 2000);
    };
    
    logout();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-mono tracking-wide">Signing Out</CardTitle>
          <CardDescription>
            You have been logged out of your account
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">Redirecting you to the home page...</p>
          <Button 
            variant="outline" 
            className="font-mono tracking-wide"
            onClick={() => {
              const basePath = window.location.pathname.endsWith('/') ? 
                window.location.pathname : 
                window.location.pathname + '/';
              window.location.href = basePath.split('/logout')[0] + '/';
            }}
          >
            Return to home now
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
