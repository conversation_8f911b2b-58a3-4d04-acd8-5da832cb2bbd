import React from 'react';
import { Loader2 } from 'lucide-react';

interface Props {
  isLoading: boolean;
  message?: string;
  overlay?: boolean;
}

export function LoadingOverlay({ isLoading, message = 'Generating documentation...', overlay = true }: Props) {
  if (!isLoading) return null;
  
  return (
    <div className={`${overlay ? 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50' : 'relative'} flex flex-col items-center justify-center`}>
      <div className="relative p-6 bg-card/30 backdrop-blur-md border border-primary/20 shadow-lg rounded-lg overflow-hidden">
        {/* Hexagon pattern background */}
        <div className="absolute inset-0 -z-10 opacity-10">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <pattern id="hexagons" width="50" height="43.4" patternUnits="userSpaceOnUse" patternTransform="scale(2) rotate(0)">
              <polygon points="25,0 50,14.4 50,37.6 25,52 0,37.6 0,14.4" fill="none" stroke="currentColor" strokeWidth="1"/>
            </pattern>
            <rect width="100%" height="100%" fill="url(#hexagons)" />
          </svg>
        </div>

        <div className="flex flex-col items-center gap-4">
          {/* Animated loader */}
          <div className="relative h-16 w-16">
            <Loader2 className="h-16 w-16 animate-spin text-primary" />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-4 w-4 rounded-full bg-primary animate-pulse"></div>
            </div>
          </div>
          
          {/* Message */}
          <div className="text-center">
            <h3 className="font-mono text-lg tracking-wider">{message}</h3>
            <p className="text-muted-foreground text-sm mt-1">This process may take some time...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
