{"name": "html2pdf.js", "version": "0.10.3", "description": "Client-side HTML-to-PDF rendering using pure JS", "main": "dist/html2pdf.js", "files": ["/src", "/dist"], "repository": "**************:eKoopmans/html2pdf.js.git", "keywords": ["javascript", "pdf-generation", "html", "client-side", "canvas"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.erik-koopmans.com"}, "license": "MIT", "bugs": {"url": "https://github.com/eKoopmans/html2pdf.js/issues"}, "homepage": "https://ekoopmans.github.io/html2pdf.js/", "dependencies": {"es6-promise": "^4.2.5", "html2canvas": "^1.0.0", "jspdf": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.14.8", "@babel/preset-env": "^7.14.8", "babel-loader": "^8.2.2", "chai": "^4.2.0", "chai-spies": "^1.0.0", "core-js": "^3.16.0", "karma": "^6.3.4", "karma-chai": "^0.1.0", "karma-chai-spies": "^0.1.4", "karma-chrome-launcher": "^2.2.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.1.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^2.0.2", "karma-webpack-preprocessor": "ekoopmans/karma-webpack-preprocessor#update-2021", "mocha": "^6.1.4", "pdftest": "^0.3.0", "rimraf": "^2.6.2", "start-server-and-test": "^1.12.0", "webpack": "^5.45.1", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^4.7.2"}, "scripts": {"build": "npm run clean && webpack --env=prod", "build:analyze": "npm run clean && webpack --env=prod --env=analyzer", "clean": "rimraf dist/*", "dev": "webpack --env=dev", "dev:analyze": "webpack --env=dev --env=analyzer", "test": "start-server-and-test test:serve http://localhost:3000 test:run", "test:serve": "pdftest serve 3000 ./test/reference/snapshot", "test:run": "npx karma start karma.conf.js"}}