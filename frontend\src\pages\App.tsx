import React from "react";
import { toast } from "sonner";
import { UserProfileProvider } from "@/components/core/UserProfileProvider";
import { Button } from "@/components/ui/button";
import { Toaster } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/core/ThemeToggle";
import { Link, useNavigate } from "react-router-dom";
import { useCurrentUser } from "app";

export default function App() {
  const { user, loading } = useCurrentUser();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      const { firebaseAuth } = await import('app');
      await firebaseAuth.signOut();
      toast.success('Erfolgreich abgemeldet');
    } catch (error) {
      console.error('Logout failed:', error);
      toast.error('Abmeldung fehlgeschlagen. Bitte versuche es erneut.');
    }
  };

  return (
    <UserProfileProvider>
    <div className="min-h-screen bg-background bg-gis-grid overflow-x-hidden">
      <Toaster position="top-right" toastOptions={{ className: 'mt-14' }} />
      {/* Header with angled cut at bottom */}
      <header className="relative w-full bg-gradient-to-r from-secondary to-background border-b border-border/40 glassmorphic">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            {/* Hexagonal logo */}
            <div className="relative w-16 h-16 flex items-center justify-center">
              <div className="absolute bg-primary/10 w-12 h-12 rotate-45 rounded-sm backdrop-blur-md border border-border/30"></div>
              <span className="relative font-mono font-bold text-xl tracking-wider text-primary z-10">CSG</span>
            </div>
            <h1 className="ml-3 text-xl font-mono tracking-widest text-foreground">CodeScribe GIS</h1>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6 font-mono text-sm tracking-wide">
            <a href="#features" className="text-foreground hover:text-primary transition-colors">Features</a>
            <a href="#workflow" className="text-foreground hover:text-primary transition-colors">Workflow</a>
            <a href="#documentation" className="text-foreground hover:text-primary transition-colors">Documentation</a>
            <a href="#contact" className="text-foreground hover:text-primary transition-colors">Contact</a>
          </nav>
          
          <div className="flex space-x-3 items-center">
            <ThemeToggle />
            {/* Test Firebase Connection Button - Development only */}
            <Button 
              variant="outline" 
              size="sm" 
              className="hidden font-mono tracking-wide bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-700 dark:text-yellow-300 mr-2"
              onClick={() => {
                // Test Firebase connection
                try {
                  // Use proper ES module import
                  import('app').then(({ firebaseAuth }) => {
                    if (firebaseAuth) {
                      toast.success('Firebase connection successful!');
                    } else {
                      toast.error('Failed to connect to Firebase.');
                    }
                  }).catch(error => {
                    console.error('Firebase connection test failed:', error);
                    toast.error('Firebase connection test failed. See console for details.');
                  });
                } catch (error) {
                  console.error('Firebase connection test failed:', error);
                  toast.error('Firebase connection test failed. See console for details.');
                }
              }}
            >
              Test Connection
            </Button>
            
            {/* Mobile Menu Button */}
            <Button 
              variant="ghost" 
              size="icon" 
              className="md:hidden" 
              onClick={() => document.getElementById('mobile-menu')?.classList.toggle('hidden')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
              </svg>
            </Button>
            
            {/* Conditional Navigation for logged-in users */}
            {!loading && user ? (
              <>
                <Link to="documents">
                  <Button variant="outline" size="sm" className="hidden md:inline-flex font-mono tracking-wide">My Documents</Button>
                </Link>
                <Link to="profile">
                  <Button variant="outline" size="sm" className="hidden md:inline-flex font-mono tracking-wide">Profil</Button>
                </Link>
                <Button 
                  variant="glass" 
                  size="sm" 
                  className="font-mono tracking-wide bg-primary/20"
                  onClick={handleLogout}
                >
                  Log Out
                </Button>
              </>
            ) : (
              <Link to="register">
                <Button variant="glass" size="sm" className="font-mono tracking-wide bg-primary/20">Sign Up</Button>
              </Link>
            )}
          </div>
        </div>
        {/* Angled cut at bottom of header */}
        <div className="absolute -bottom-6 left-0 right-0 h-12 bg-background skew-y-1 z-0"></div>
        
        {/* Mobile Menu */}
        <div id="mobile-menu" className="hidden absolute top-full left-0 right-0 bg-background z-50 border-b border-border/40 backdrop-blur-md">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4 font-mono text-sm tracking-wide">
              <a href="#features" className="hover:text-primary transition-colors py-2 border-b border-border/20">Features</a>
              <a href="#workflow" className="hover:text-primary transition-colors py-2 border-b border-border/20">Workflow</a>
              <a href="#documentation" className="hover:text-primary transition-colors py-2 border-b border-border/20">Documentation</a>
              <a href="#contact" className="hover:text-primary transition-colors py-2 border-b border-border/20">Contact</a>
              
              {/* Conditional Mobile Navigation based on login status */}
              {!loading && user ? (
                <>
                  <Link to="documents" className="w-full py-2">
                    <Button variant="outline" size="sm" className="w-full font-mono tracking-wide">My Documents</Button>
                  </Link>
                  <Link to="profile" className="w-full py-2">
                    <Button variant="outline" size="sm" className="w-full font-mono tracking-wide">Profil</Button>
                  </Link>
                  <div className="w-full py-2">
                    <Button 
                      variant="glass" 
                      size="sm" 
                      className="w-full font-mono tracking-wide bg-primary/20"
                      onClick={handleLogout}
                    >
                      Log Out
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <Link to="login" className="w-full py-2">
                    <Button variant="outline" size="sm" className="w-full font-mono tracking-wide">Log In</Button>
                  </Link>
                  <Link to="login" className="w-full py-2">
                    <Button variant="glass" size="sm" className="w-full font-mono tracking-wide bg-primary/20">Sign Up</Button>
                  </Link>
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      <main className="relative container mx-auto px-4 pt-16 pb-24 z-10">
        {/* Hero Section with Split-pane Interface */}
        <section className="relative py-16">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="space-y-8">
              <h2 className="text-4xl md:text-5xl font-mono tracking-wider font-light leading-tight">
                Automate Your <span className="text-primary font-normal">GIS Script</span> Documentation
              </h2>
              <p className="text-lg text-muted-foreground">
                Upload your code, get comprehensive documentation. CodeScribe GIS automatically extracts parameters, workflows, and metadata from your Python scripts for GIS applications.  
              </p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-4">
                <Link to="editor?new=true" onClick={(e) => {
                  e.preventDefault();
                  
                  if (!user) {
                    toast.error('Sie müssen angemeldet sein, um ein Dokument zu erstellen');
                    // Using proper React Router navigation instead of direct window.location manipulation
                    navigate('/login');
                    return;
                  }
                  
                  // Check document limit before navigating
                  import('services/documentStore').then(async ({ useDocumentStore }) => {
                    try {
                      const { canCreate, currentCount, maxAllowed } = await useDocumentStore.getState().checkDocumentLimit(user.uid);
                      
                      if (!canCreate) {
                        toast.error(
                          <div className="space-y-2">
                            <p><strong>Dokumentenlimit erreicht!</strong></p>
                            <p>Sie haben das Limit von {maxAllowed} Dokumenten für Ihren aktuellen Plan erreicht.</p>
                            <p className="text-sm">Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente.</p>
                          </div>,
                          { duration: 8000 }
                        );
                      } else {
                        // Use proper React Router navigation
                        navigate('/editor?new=true');
                      }
                    } catch (err) {
                      console.error('Error checking document limit:', err);
                      // Navigate anyway on error to ensure users can proceed
                      navigate('/editor?new=true');
                    }
                  }).catch(err => {
                    console.error('Failed to import document store:', err);
                    // Navigate anyway on import error to ensure users can proceed
                    navigate('/editor?new=true');
                  });
                }}>
                <Button 
                  size="lg" 
                  className="relative overflow-hidden group transition-all duration-200 bg-gradient-to-r from-primary/80 to-primary hover:from-primary hover:to-primary/90 font-mono tracking-wide"
                >
                  {/* Hexagonal accent pattern for GIS-inspired design */}
                  <div className="absolute inset-0 opacity-10 pointer-events-none group-hover:opacity-20 transition-opacity duration-200 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+')]"></div>
                  
                  {/* Hexagonal decorative accents */}
                  <div className="absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10" 
                    style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }}></div>
                  <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10" 
                    style={{ clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)' }}></div>
                  
                  {/* Button content */}
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-cpu">
                      <rect x="4" y="4" width="16" height="16" rx="2"/>
                      <rect x="9" y="9" width="6" height="6"/>
                      <path d="M15 2v2"/>
                      <path d="M15 20v2"/>
                      <path d="M2 15h2"/>
                      <path d="M20 15h2"/>
                      <path d="M2 9h2"/>
                      <path d="M20 9h2"/>
                      <path d="M9 2v2"/>
                      <path d="M9 20v2"/>
                    </svg>
                    Start Documenting
                  </span>
                </Button>
              </Link>
                <Button variant="outline" size="lg" className="font-mono tracking-wide">
                  Learn More
                </Button>
              </div>
            </div>
            
            {/* Code Preview + Documentation Split Pane */}
            <div className="relative h-[400px] md:h-[450px] rounded-lg overflow-hidden border border-border/40 shadow-lg">
              <div className="absolute inset-0 bg-black/5 backdrop-blur-[2px]"></div>
              {/* Split Pane Effect */}
              <div className="absolute inset-0 flex flex-col md:flex-row">
                {/* Left Side: Code */}
                <div className="md:w-1/2 h-1/2 md:h-full border-b md:border-b-0 md:border-r border-border/40 bg-secondary/30 backdrop-blur-sm p-4 overflow-hidden">
                  <div className="font-mono text-xs text-foreground/80 mb-2">// GIS Python Script</div>
                  <pre className="text-xs font-code text-foreground overflow-hidden h-[360px]">
                    <code>{`import arcpy
import os

# Define parameters
def buffer_analysis(input_fc, output_fc, buffer_distance):
    """Create buffer zones around input features.
    
    Args:
        input_fc: Input feature class
        output_fc: Output feature class
        buffer_distance: Buffer distance in meters
    """
    # Validate inputs
    if not arcpy.Exists(input_fc):
        raise ValueError("Input feature class does not exist")
    
    # Create buffer
    arcpy.analysis.Buffer(
        input_fc,
        output_fc,
        buffer_distance,
        "FULL",
        "ROUND",
        "ALL"
    )
    
    print(f"Buffer created at {output_fc}")
    return output_fc`}</code>
                  </pre>
                </div>
                
                {/* Right Side: Documentation Preview */}
                <div className="md:w-1/2 h-1/2 md:h-full bg-card/70 p-4 overflow-hidden text-foreground">
                  <h3 className="text-sm font-semibold mb-3 font-mono tracking-wider">DOCUMENTATION PREVIEW</h3>
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-xs font-semibold text-muted-foreground">FUNCTION</h4>
                      <p className="text-sm">buffer_analysis</p>
                    </div>
                    <div>
                      <h4 className="text-xs font-semibold text-muted-foreground">DESCRIPTION</h4>
                      <p className="text-sm">Create buffer zones around input features.</p>
                    </div>
                    <div>
                      <h4 className="text-xs font-semibold text-muted-foreground">PARAMETERS</h4>
                      <div className="text-sm space-y-1">
                        <p><span className="font-medium">input_fc:</span> Input feature class</p>
                        <p><span className="font-medium">output_fc:</span> Output feature class</p>
                        <p><span className="font-medium">buffer_distance:</span> Buffer distance in meters</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Features Section with Hexagonal Elements */}
        <section id="features" className="py-24 relative">
          {/* Hexagonal background pattern */}
          <div className="absolute inset-0 z-0 opacity-5">
            {Array.from({ length: 6 }).map((_, i) => (
              <div 
                key={i} 
                className="absolute w-32 h-32 rotate-45 border border-primary" 
                style={{ 
                  left: `${Math.random() * 100}%`, 
                  top: `${Math.random() * 100}%`,
                  opacity: 0.1 + Math.random() * 0.2
                }}
              />
            ))}
          </div>
          
          <div className="relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-mono tracking-wider font-light mb-4">
                Key Features
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                CodeScribe GIS provides comprehensive documentation automation for your GIS scripts,
                saving you hours of manual documentation work.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  title: "Automatic Parameter Extraction", 
                  description: "Identifies input/output parameters, their types, and requirements directly from your code."
                },
                {
                  title: "Workflow Visualization", 
                  description: "Generates interactive diagrams that visualize your script's data flow and processing steps."
                },
                {
                  title: "Code Parsing & Analysis", 
                  description: "Extracts metadata, descriptions, and functional details from your GIS scripts."
                },
                {
                  title: "Split-Pane Interface", 
                  description: "View your code and generated documentation side-by-side for real-time feedback."
                },
                {
                  title: "Multiple Format Export", 
                  description: "Export your documentation in Markdown, HTML, or PDF formats for various use cases."
                },
                {
                  title: "Version Tracking", 
                  description: "Automatically tracks code versions and changes to provide historical documentation."
                }
              ].map((feature, i) => (
                <Card key={i} className="border-border/40 glassmorphic-hover transition-colors">
                  <CardHeader>
                    {/* Hexagonal number indicator */}
                    <div className="w-12 h-12 flex items-center justify-center mb-2">
                      <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
                      <span className="relative z-10 font-mono font-bold">{i+1}</span>
                    </div>
                    <CardTitle className="font-mono tracking-wide">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-muted-foreground">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        
        {/* Code Documentation Workflow Section */}
        <section id="workflow" className="py-24 relative">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-mono tracking-wider font-light mb-4">
              Documentation Workflow
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Simply upload your code and let CodeScribe GIS handle the rest.
              Our intuitive process makes documentation effortless.
            </p>
          </div>
          
          {/* Workflow Steps with Connecting Paths */}
          <div className="relative">
            {/* Connecting path */}
            <div className="absolute left-1/2 top-8 bottom-8 w-0.5 bg-border z-0 hidden md:block"></div>
            
            <div className="space-y-16 relative z-10">
              {[
                {
                  title: "Upload Your Code",
                  description: "Paste your code or upload script files directly. We support Python and other languages used in GIS applications."
                },
                {
                  title: "Automatic Analysis",
                  description: "Our system parses your code, identifying parameters, functions, workflows, and embedded documentation."
                },
                {
                  title: "Review & Edit",
                  description: "Review the generated documentation in the split-pane interface and make any necessary adjustments."
                },
                {
                  title: "Export & Share",
                  description: "Export your documentation in your preferred format and share it with your team or clients."
                }
              ].map((step, i) => (
                <div key={i} className="flex flex-col md:flex-row items-center gap-4 md:gap-12">
                  <div className={`md:w-1/2 ${i % 2 === 1 ? 'md:order-2' : ''}`}>
                    <Card className="border-border/40 glassmorphic">
                      <CardHeader>
                        {/* Step number in hexagonal shape */}
                        <div className="w-12 h-12 flex items-center justify-center mb-4">
                          <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
                          <span className="relative z-10 font-mono font-bold">{i+1}</span>
                        </div>
                        <CardTitle className="font-mono tracking-wide">{step.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">
                          {step.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Connection node */}
                  <div className="relative w-12 h-12 rounded-full border-2 border-border bg-card flex items-center justify-center md:mx-12">
                    <div className="w-4 h-4 rounded-sm rotate-45 bg-primary/80"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <section id="documentation" className="py-24">
          <div className="relative overflow-hidden rounded-lg border border-border/40 bg-gradient-to-br from-primary/5 to-secondary/20 glassmorphic p-12">
            {/* Hexagonal decorative elements */}
            {Array.from({ length: 4 }).map((_, i) => (
              <div 
                key={i} 
                className="absolute w-32 h-32 rotate-45 border border-primary/30" 
                style={{ 
                  left: `${Math.random() * 100}%`, 
                  top: `${Math.random() * 100}%`,
                  opacity: 0.05 + Math.random() * 0.1
                }}
              />
            ))}
            
            <div className="relative z-10 text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-mono tracking-wider font-light mb-6">
                Start Documenting Your GIS Code Today
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Save hours of manual documentation work and ensure your GIS scripts are well-documented 
                for your team, clients, and future reference.
              </p>
              <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <Button size="lg" className="font-mono tracking-wide">
                  Sign Up for Free
                </Button>
                <Button variant="glass" size="lg" className="font-mono tracking-wide bg-primary/20">
                  Watch Demo
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      {/* Contact Section */}
      <section id="contact" className="py-24 bg-card/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-mono tracking-wider font-light mb-4">
              Contact Us
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Have questions about CodeScribe GIS? We're here to help. Reach out to our team for more information.
            </p>
          </div>
          
          <div className="max-w-3xl mx-auto grid md:grid-cols-2 gap-8">
            <Card className="border-border/40 glassmorphic">
              <CardHeader>
                <div className="w-12 h-12 flex items-center justify-center mb-2">
                  <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
                  <span className="relative z-10 font-mono font-bold">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </span>
                </div>
                <CardTitle className="font-mono tracking-wide">Email Us</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  For general inquiries, support questions, or more information about our services.
                </p>
                <Button variant="outline" className="w-full font-mono tracking-wide">
                  <EMAIL>
                </Button>
              </CardContent>
            </Card>
            
            <Card className="border-border/40 bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <div className="w-12 h-12 flex items-center justify-center mb-2">
                  <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"></div>
                  <span className="relative z-10 font-mono font-bold">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                    </svg>
                  </span>
                </div>
                <CardTitle className="font-mono tracking-wide">Live Chat</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Chat with our support team in real-time for immediate assistance with your questions.
                </p>
                <Button className="w-full font-mono tracking-wide">
                  Start a Conversation
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="border-t border-border/40 bg-secondary/20 glassmorphic py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                {/* Hexagonal logo */}
                <div className="relative w-8 h-8 flex items-center justify-center">
                  <div className="absolute bg-primary/10 w-8 h-8 rotate-45 rounded-sm"></div>
                  <span className="relative font-mono font-bold text-sm tracking-wider text-primary z-10">CSG</span>
                </div>
                <h3 className="ml-2 text-sm font-mono tracking-widest">CodeScribe GIS</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Automated documentation generator for GIS scripts and applications.
              </p>
            </div>
            
            <div>
              <h4 className="font-mono tracking-wider text-sm font-medium mb-4">Product</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#features" className="hover:text-primary transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Release Notes</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-mono tracking-wider text-sm font-medium mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#" className="hover:text-primary transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Careers</a></li>
                <li><a href="#contact" className="hover:text-primary transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-mono tracking-wider text-sm font-medium mb-4">Connect</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#" className="hover:text-primary transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">GitHub</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">LinkedIn</a></li>
                <li><a href="#" className="hover:text-primary transition-colors">Discord Community</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-border/40 mt-12 pt-8 text-center text-sm text-muted-foreground">
            <p>© {new Date().getFullYear()} CodeScribe GIS. All rights reserved.</p>
          </div>
        </div>
      </footer>
      
      {/* Back to top button */}
      <a 
        href="#" 
        className="fixed bottom-6 right-6 w-12 h-12 flex items-center justify-center group z-50"
        onClick={(e) => {
          e.preventDefault();
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }}
      >
        <div className="absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm backdrop-blur-md border border-border/30 group-hover:bg-primary/20 transition-colors"></div>
        <span className="relative z-10 text-foreground">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </span>
      </a>
    </div>
    </UserProfileProvider>
  );
}
