import{m as U,M as L,d,_ as C}from"./index-3b130772.js";import{A as E,m as o,j as e,K as I,L as S,G as R,d as A,l as B}from"./vendor-8eb8bd34.js";import{C as _,a as D,b as z,d as F,c as H,B as r}from"./card-b935221e.js";import{L as j,I as w}from"./label-ef207e6f.js";import{u as n}from"./index-fcfcda09.js";const M=()=>{const i=U===L.DEV,p=E(),[f,c]=o.useState(!1);return o.useEffect(()=>{c(!0)},[]),{bypassAuth:(t="/editor")=>{if(i){const l={uid:"dev-user-1234",email:"<EMAIL>",displayName:"Development User",emailVerified:!0};localStorage.setItem("devBypassUser",JSON.stringify(l)),f?p(t,{replace:!0}):setTimeout(()=>{window.location.href=t},100)}},getBypassedUser:()=>{if(!i)return null;try{const t=localStorage.getItem("devBypassUser");return t?JSON.parse(t):null}catch(t){return console.error("Error parsing bypassed user:",t),null}},clearBypassedUser:()=>{i&&localStorage.removeItem("devBypassUser")},isDev:i}};function J(){const i=E(),{bypassAuth:p,isDev:f}=M(),[c,N]=o.useState(""),[m,b]=o.useState(""),[t,l]=o.useState(""),[u,x]=o.useState(!1),[k,v]=o.useState(!1),[h,y]=o.useState("");return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4",children:e.jsxs(_,{className:"w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(D,{className:"text-center",children:[e.jsxs("div",{className:"mx-auto w-12 h-12 flex items-center justify-center mb-2",children:[e.jsx("div",{className:"absolute w-10 h-10 bg-primary/10 rotate-45 rounded-sm"}),e.jsx("span",{className:"relative z-10 font-mono font-bold",children:"GIS"})]}),e.jsx(z,{className:"text-2xl font-mono tracking-wide",children:"Sign In"}),e.jsx(F,{children:"Log in to access your documentation and tools"})]}),e.jsxs(H,{children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(j,{htmlFor:"email",children:"Email"}),e.jsx(w,{id:"email",type:"email",placeholder:"<EMAIL>",value:c,onChange:s=>N(s.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(j,{htmlFor:"password",children:"Password"}),e.jsx(w,{id:"password",type:"password",placeholder:"••••••••",value:m,onChange:s=>b(s.target.value),className:"font-mono"})]}),t&&e.jsx("div",{className:"text-sm font-medium text-destructive p-2 border border-destructive/20 bg-destructive/5 rounded-md mb-2",children:t}),e.jsx(r,{type:"submit",className:"w-full justify-center",disabled:u||!c||!m,onClick:async()=>{try{x(!0),l(""),await I(d,c,m),n.success("Login successful!"),setTimeout(async()=>{const s=d.currentUser;if(s)try{const{useUserProfileStore:a}=await C(()=>import("./index-3b130772.js").then(g=>g.p),["assets/index-3b130772.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await a.getState().createProfileIfNotExists(s.uid,s.email===void 0?null:s.email,s.displayName===void 0?null:s.displayName,s.photoURL===void 0?null:s.photoURL)}catch(a){console.warn("Non-critical error updating profile:",a)}i("/")},500)}catch(s){console.error("Authentication failed:",s),s.code==="auth/user-not-found"||s.code==="auth/wrong-password"?l("Invalid email or password"):l("Authentication failed. Please try again.")}finally{x(!1)}},children:u?e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{children:"Signing in..."})]}):"Sign in"}),e.jsx("div",{className:"text-center",children:e.jsx(r,{variant:"link",className:"font-mono text-xs",asChild:!0,children:e.jsx(S,{to:"/register",children:"Need an account? Register"})})}),e.jsx("div",{className:"text-center mt-2",children:e.jsx(r,{variant:"ghost",className:"font-mono text-xs",onClick:()=>{v(!0),l("")},children:"Forgot password?"})}),e.jsxs("div",{className:"relative my-6",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("span",{className:"w-full border-t border-border/40"})}),e.jsx("div",{className:"relative flex justify-center text-xs",children:e.jsx("span",{className:"bg-card px-2 text-muted-foreground",children:"Or continue with"})})]}),e.jsxs(r,{variant:"outline",className:"w-full justify-center space-x-2",onClick:async()=>{try{const s=new R;s.addScope("profile"),s.addScope("email"),await A(d,s),n.success("Login successful!"),setTimeout(async()=>{try{const a=d.currentUser;if(a){const{useUserProfileStore:g}=await C(()=>import("./index-3b130772.js").then(P=>P.p),["assets/index-3b130772.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await g.getState().createProfileIfNotExists(a.uid,a.email===void 0?null:a.email,a.displayName===void 0?null:a.displayName,a.photoURL===void 0?null:a.photoURL)}}catch(a){console.warn("Non-critical error updating profile:",a)}i("/")},500)}catch(s){console.error("Authentication failed:",s),l("Google authentication failed")}},children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",className:"h-5 w-5",children:[e.jsx("path",{fill:"#FFC107",d:"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"}),e.jsx("path",{fill:"#FF3D00",d:"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"}),e.jsx("path",{fill:"#4CAF50",d:"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"}),e.jsx("path",{fill:"#1976D2",d:"M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"})]}),e.jsx("span",{children:"Sign in with Google"})]})]}),e.jsxs("div",{className:"mt-6 text-center",children:[k&&e.jsx("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"bg-card p-6 rounded-lg shadow-lg max-w-md w-full space-y-4 border border-border",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Reset Password"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter your email address and we'll send you a link to reset your password."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(j,{htmlFor:"resetEmail",children:"Email"}),e.jsx(w,{id:"resetEmail",type:"email",placeholder:"<EMAIL>",value:h,onChange:s=>y(s.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(r,{variant:"outline",onClick:()=>{v(!1),y("")},children:"Cancel"}),e.jsx(r,{onClick:async()=>{if(!h){n.error("Please enter your email address");return}try{x(!0),await B(d,h),n.success("Password reset email sent! Check your inbox."),v(!1),y("")}catch(s){console.error("Password reset failed:",s),s.code==="auth/user-not-found"?n.error("No account found with this email address"):n.error("Failed to send password reset email. Please try again.")}finally{x(!1)}},disabled:u||!h,children:u?"Sending...":"Send Reset Link"})]})]})}),f&&e.jsxs("div",{className:"mt-4 pt-4 border-t border-muted",children:[e.jsx("p",{className:"text-xs text-muted-foreground mb-2",children:"Development mode detected"}),e.jsx(r,{variant:"secondary",size:"sm",className:"font-mono tracking-wide bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-700 dark:text-yellow-300",onClick:()=>p(),children:"Bypass Login (Dev Only)"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx(r,{variant:"ghost",size:"sm",className:"font-mono text-xs",asChild:!0,children:e.jsx(S,{to:"/",children:"Back to Home"})})})]})]})]})})}export{J as default};
