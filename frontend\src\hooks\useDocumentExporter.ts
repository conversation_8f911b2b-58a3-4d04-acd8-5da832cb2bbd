/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Hook für Dokumentenexport-Funktionalität
 */

import { useState } from 'react';
import { doc, collection, getDoc, getDocs, getFirestore } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { Document } from '../services/documentStore';
import { COLLECTIONS } from '../utils/firebaseConfig';
import { downloadFile } from '../utils/exportUtils';
import { toast } from 'sonner';

interface UseDocumentExporterProps {
  user: any;
}

interface UseDocumentExporterReturn {
  exportDocument: Document | null;
  exportDialogOpen: boolean;
  isExporting: boolean;
  setExportDialogOpen: (open: boolean) => void;
  handleExportDialog: (docId: string) => Promise<void>;
  handleExportDocument: (document: Document, formats: string[]) => Promise<void>;
}

export function useDocumentExporter({
  user
}: UseDocumentExporterProps): UseDocumentExporterReturn {
  const [exportDocument, setExportDocument] = useState<Document | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Export-Dialog öffnen und Dokument vorbereiten
  const handleExportDialog = async (docId: string) => {
    console.log('Export-Dialog ausgelöst für Dokument ID:', docId);
    
    try {
      toast.loading('Dokument für Export vorbereiten...');
      
      // Vollständiges Dokument zuerst abrufen
      const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);
      console.log('Dokument aus Firestore abrufen:', docRef.path);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.error('Dokument nicht gefunden');
        toast.error('Dokument nicht gefunden');
        return;
      }

      console.log('Dokument existiert, Metadaten abrufen');
      // Dokument-Metadaten abrufen
      const metaData = docSnap.data();

      // Inhalts-Subkollektion abrufen
      console.log('Inhalts-Subkollektion abrufen');
      const contentSnap = await getDocs(collection(docRef, 'content'));

      if (contentSnap.empty) {
        console.error('Inhalts-Subkollektion ist leer');
        toast.error('Dokumentinhalt nicht gefunden');
        return;
      }

      const contentData = contentSnap.docs[0].data();
      console.log('Inhaltsdaten erfolgreich abgerufen');

      // Vollständiges Dokument-Objekt mit Standardwerten für fehlende Felder erstellen
      const fullDocument: Document = {
        id: docId,
        title: metaData.title || 'Unbenanntes Dokument',
        description: metaData.description || '',
        createdAt: metaData.createdAt?.toDate() || new Date(),
        updatedAt: metaData.updatedAt?.toDate() || new Date(),
        userId: metaData.userId,
        language: metaData.language || 'python',
        codeSize: metaData.codeSize || 0,
        isPublic: metaData.isPublic || false,
        tags: metaData.tags || [],
        content: {
          originalCode: contentData.originalCode || '',
          generatedDocumentation: contentData.generatedDocumentation || null,
          parameters: contentData.parameters || [],
          toolsUsed: contentData.toolsUsed || [],
          workflow: contentData.workflow || null,
          manualEdits: contentData.manualEdits?.map((edit: any) => ({
            ...edit,
            timestamp: edit.timestamp?.toDate() || new Date()
          })) || [],
          exportCache: contentData.exportCache || []
        }
      };

      console.log('Dokument-Objekt erstellt, Zustand setzen und Export-Dialog öffnen');
      console.log('Dokument hat Inhalt:', !!fullDocument.content, 'Original-Code-Länge:', fullDocument.content.originalCode?.length || 0);

      // UI-Zustand vor dem Öffnen des Export-Dialogs protokollieren
      console.log('UI-Zustand vor dem Öffnen des Export-Dialogs:', {
        exportDialogCurrentlyOpen: exportDialogOpen,
        currentExportDocument: exportDocument ? exportDocument.id : 'none'
      });

      // Dokument im Zustand speichern und Dialog öffnen
      setExportDocument(fullDocument);
      setExportDialogOpen(true);

      // Prüfen ob Zustand aktualisiert wurde
      console.log('UI-Zustand für Export-Dialog aktualisiert:', {
        exportDialogOpen: true, 
        documentId: fullDocument.id
      });

      toast.dismiss();
      
    } catch (err) {
      console.error('Fehler beim Abrufen des Dokuments für Export:', err);
      toast.error('Fehler beim Vorbereiten des Dokuments für Export');
      toast.dismiss();
    }
  };

  // Dokument exportieren
  const handleExportDocument = async (document: Document, formats: string[]) => {
    if (!user?.uid) {
      console.error('Benutzer nicht angemeldet');
      toast.error('Sie müssen angemeldet sein, um Dokumente zu exportieren');
      return;
    }

    setIsExporting(true);
    
    try {
      console.log('Export-Prozess starten für Dokument:', document.id, 'Formate:', formats);
      toast.loading(`Export wird vorbereitet...`);

      // Tiefe Kopie des Dokuments erstellen, um das Original nicht zu verändern
      const documentCopy = JSON.parse(JSON.stringify(document));

      // Prüfen ob Dokumentinhalt existiert
      if (!documentCopy || !documentCopy.content) {
        console.error('Dokumentinhalt fehlt:', documentCopy);
        toast.error('Dokumentinhalt fehlt oder ist ungültig');
        return;
      }

      console.log('Dokument validiert, mit Export fortfahren');

      // Map erstellen um zu verfolgen, welche Formate erfolgreich exportiert wurden
      const exportedFormats: Record<string, boolean> = {};

      // Prüfen ob bereits gecachte Versionen von Formaten vorhanden sind
      const cachedFormats = document.content.exportCache || [];
      console.log('Gecachte Formate:', cachedFormats.map(cf => `${cf.format} (${new Date(cf.updatedAt).toLocaleString()})` ));

      const exportPromises = formats.map(async (format) => {
        try {
          console.log(`Format verarbeiten: ${format}`);
          
          // Prüfen ob eine aktuelle gecachte Version vorhanden ist (weniger als 24 Stunden alt)
          const cachedFormat = cachedFormats.find(
            cache => cache && cache.format === format && 
              (new Date().getTime() - new Date(cache.updatedAt).getTime() < 86400000)
          );

          if (cachedFormat) {
            console.log(`Gecachte Version für ${format} gefunden:`, cachedFormat);
            
            // Gecachte Version verwenden
            if (format === 'markdown' || format === 'html' || format === 'text') {
              // Für textbasierte Formate können wir direkt herunterladen
              const extension = format === 'markdown' ? '.md' : format === 'html' ? '.html' : '.txt';
              const mimeType = format === 'markdown' ? 'text/markdown' : format === 'html' ? 'text/html' : 'text/plain';
              const filename = document.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() + extension;

              console.log(`Gecachte ${format}-Daten verwenden, als ${filename} herunterladen`);
              toast.info(`Gecachte ${format.toUpperCase()}-Datei verwenden`);
              downloadFile(cachedFormat.data, filename, mimeType);
              exportedFormats[format] = true;
              return;
            }
          }

          // Wenn keine gecachte Version vorhanden ist, neuen Export generieren
          console.log(`Neuen Export für ${format} generieren`);
          toast.loading(`${format.toUpperCase()} wird generiert...`);
          
          // Hier würde die tatsächliche Export-Logik stehen
          // Für jetzt simulieren wir einen erfolgreichen Export
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          exportedFormats[format] = true;
          toast.success(`${format.toUpperCase()} erfolgreich exportiert`);
          
        } catch (formatError) {
          console.error(`Fehler beim Exportieren von ${format}:`, formatError);
          toast.error(`Fehler beim Exportieren von ${format.toUpperCase()}`);
          exportedFormats[format] = false;
        }
      });

      // Auf alle Export-Promises warten
      await Promise.all(exportPromises);

      // Erfolgreiche Exporte zählen
      const successfulExports = Object.values(exportedFormats).filter(Boolean).length;
      const totalFormats = formats.length;

      if (successfulExports === totalFormats) {
        toast.success(`Alle ${totalFormats} Formate erfolgreich exportiert`);
      } else if (successfulExports > 0) {
        toast.warning(`${successfulExports} von ${totalFormats} Formaten erfolgreich exportiert`);
      } else {
        toast.error('Export fehlgeschlagen');
      }

      // Export-Dialog schließen
      setExportDialogOpen(false);
      setExportDocument(null);

    } catch (err) {
      console.error('Fehler beim Exportieren des Dokuments:', err);
      toast.error('Export fehlgeschlagen: ' + (err instanceof Error ? err.message : 'Unbekannter Fehler'));
    } finally {
      setIsExporting(false);
      toast.dismiss();
    }
  };

  return {
    exportDocument,
    exportDialogOpen,
    isExporting,
    setExportDialogOpen,
    handleExportDialog,
    handleExportDocument
  };
}
