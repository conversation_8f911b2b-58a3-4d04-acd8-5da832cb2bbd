{"version": 3, "sources": ["../../@radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,YAAuB;AA8BjB,yBAAA;AAvBN,IAAM,yBAAyB,OAAO,OAAO;;EAE3C,UAAU;EACV,QAAQ;EACR,OAAO;EACP,QAAQ;EACR,SAAS;EACT,QAAQ;EACR,UAAU;EACV,MAAM;EACN,YAAY;EACZ,UAAU;AACZ,CAAC;AAED,IAAM,OAAO;AAMb,IAAM,iBAAuB;EAC3B,CAAC,OAAO,iBAAiB;AACvB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO,EAAE,GAAG,wBAAwB,GAAG,MAAM,MAAM;MAAA;IACrD;EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,IAAM,OAAO;", "names": []}