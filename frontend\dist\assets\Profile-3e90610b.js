import{a as is,b as ds,A as _,P as X,_ as us}from"./index-********.js";import{m as n,j as e,M as Xe,A as ms,G as ps,Q as hs,S as Ke,r as ze,k as fs,T as xs}from"./vendor-8eb8bd34.js";import{e as M,u as W,g as gs,B as Z,C as te,a as se,b as oe,d as ne,c as re,h as be}from"./card-b935221e.js";import{L as ue,I as ve}from"./label-ef207e6f.js";import{P as E,u as B}from"./index-fcfcda09.js";import{c as We,i as Ze,u as Ce,d as Ie,a as T,P as vs,e as ws,f as Qe,A as js,j as ae,D as Ns,C as bs,h as ys,g as Ss,R as Cs}from"./index-89be2a26.js";import{b as Je,d as Ps,I as Is,c as Ts,h as Es,u as Rs,R as ks,F as _s,P as As,f as Ds,g as Ms,i as Ls,a as Os}from"./react-icons.esm-600f1a11.js";import{u as et}from"./index-a23265aa.js";import{V as Bs,c as Ye}from"./index-ee3cbf23.js";var Fs="Separator",qe="horizontal",Us=["horizontal","vertical"],tt=n.forwardRef((s,t)=>{const{decorative:o,orientation:r=qe,...a}=s,d=Vs(r)?r:qe,l=o?{role:"none"}:{"aria-orientation":d==="vertical"?d:void 0,role:"separator"};return e.jsx(E.div,{"data-orientation":d,...l,...a,ref:t})});tt.displayName=Fs;function Vs(s){return Us.includes(s)}var st=tt;const we=n.forwardRef(({className:s,orientation:t="horizontal",decorative:o=!0,...r},a)=>e.jsx(st,{ref:a,decorative:o,orientation:t,className:M("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",s),...r}));we.displayName=st.displayName;var Te="Tabs",[Hs,Fo]=We(Te,[Je]),ot=Je(),[Ws,$e]=Hs(Te),nt=n.forwardRef((s,t)=>{const{__scopeTabs:o,value:r,onValueChange:a,defaultValue:d,orientation:c="horizontal",dir:l,activationMode:i="automatic",...u}=s,h=Ze(l),[g,j]=Ce({prop:r,onChange:a,defaultProp:d??"",caller:Te});return e.jsx(Ws,{scope:o,baseId:Ie(),value:g,onValueChange:j,orientation:c,dir:h,activationMode:i,children:e.jsx(E.div,{dir:h,"data-orientation":c,...u,ref:t})})});nt.displayName=Te;var rt="TabsList",at=n.forwardRef((s,t)=>{const{__scopeTabs:o,loop:r=!0,...a}=s,d=$e(rt,o),c=ot(o);return e.jsx(Ps,{asChild:!0,...c,orientation:d.orientation,dir:d.dir,loop:r,children:e.jsx(E.div,{role:"tablist","aria-orientation":d.orientation,...a,ref:t})})});at.displayName=rt;var ct="TabsTrigger",lt=n.forwardRef((s,t)=>{const{__scopeTabs:o,value:r,disabled:a=!1,...d}=s,c=$e(ct,o),l=ot(o),i=ut(c.baseId,r),u=mt(c.baseId,r),h=r===c.value;return e.jsx(Is,{asChild:!0,...l,focusable:!a,active:h,children:e.jsx(E.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:i,...d,ref:t,onMouseDown:T(s.onMouseDown,g=>{!a&&g.button===0&&g.ctrlKey===!1?c.onValueChange(r):g.preventDefault()}),onKeyDown:T(s.onKeyDown,g=>{[" ","Enter"].includes(g.key)&&c.onValueChange(r)}),onFocus:T(s.onFocus,()=>{const g=c.activationMode!=="manual";!h&&!a&&g&&c.onValueChange(r)})})})});lt.displayName=ct;var it="TabsContent",dt=n.forwardRef((s,t)=>{const{__scopeTabs:o,value:r,forceMount:a,children:d,...c}=s,l=$e(it,o),i=ut(l.baseId,r),u=mt(l.baseId,r),h=r===l.value,g=n.useRef(h);return n.useEffect(()=>{const j=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(j)},[]),e.jsx(vs,{present:a||h,children:({present:j})=>e.jsx(E.div,{"data-state":h?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":i,hidden:!j,id:u,tabIndex:0,...c,ref:t,style:{...s.style,animationDuration:g.current?"0s":void 0},children:j&&d})})});dt.displayName=it;function ut(s,t){return`${s}-trigger-${t}`}function mt(s,t){return`${s}-content-${t}`}var $s=nt,pt=at,ht=lt,ft=dt;const Gs=$s,xt=n.forwardRef(({className:s,...t},o)=>e.jsx(pt,{ref:o,className:M("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...t}));xt.displayName=pt.displayName;const ye=n.forwardRef(({className:s,...t},o)=>e.jsx(ht,{ref:o,className:M("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",s),...t}));ye.displayName=ht.displayName;const Se=n.forwardRef(({className:s,...t},o)=>e.jsx(ft,{ref:o,className:M("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...t}));Se.displayName=ft.displayName;var Ee="Switch",[Ks,Uo]=We(Ee),[zs,Ys]=Ks(Ee),gt=n.forwardRef((s,t)=>{const{__scopeSwitch:o,name:r,checked:a,defaultChecked:d,required:c,disabled:l,value:i="on",onCheckedChange:u,form:h,...g}=s,[j,y]=n.useState(null),N=W(t,x=>y(x)),m=n.useRef(!1),w=j?h||!!j.closest("form"):!0,[b,f]=Ce({prop:a,defaultProp:d??!1,onChange:u,caller:Ee});return e.jsxs(zs,{scope:o,checked:b,disabled:l,children:[e.jsx(E.button,{type:"button",role:"switch","aria-checked":b,"aria-required":c,"data-state":Nt(b),"data-disabled":l?"":void 0,disabled:l,value:i,...g,ref:N,onClick:T(s.onClick,x=>{f(R=>!R),w&&(m.current=x.isPropagationStopped(),m.current||x.stopPropagation())})}),w&&e.jsx(jt,{control:j,bubbles:!m.current,name:r,value:i,checked:b,required:c,disabled:l,form:h,style:{transform:"translateX(-100%)"}})]})});gt.displayName=Ee;var vt="SwitchThumb",wt=n.forwardRef((s,t)=>{const{__scopeSwitch:o,...r}=s,a=Ys(vt,o);return e.jsx(E.span,{"data-state":Nt(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});wt.displayName=vt;var qs="SwitchBubbleInput",jt=n.forwardRef(({__scopeSwitch:s,control:t,checked:o,bubbles:r=!0,...a},d)=>{const c=n.useRef(null),l=W(c,d),i=et(o),u=ws(t);return n.useEffect(()=>{const h=c.current;if(!h)return;const g=window.HTMLInputElement.prototype,y=Object.getOwnPropertyDescriptor(g,"checked").set;if(i!==o&&y){const N=new Event("click",{bubbles:r});y.call(h,o),h.dispatchEvent(N)}},[i,o,r]),e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...a,tabIndex:-1,ref:l,style:{...a.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});jt.displayName=qs;function Nt(s){return s?"checked":"unchecked"}var bt=gt,Xs=wt;const yt=n.forwardRef(({className:s,...t},o)=>e.jsx(bt,{className:M("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...t,ref:o,children:e.jsx(Xs,{className:M("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));yt.displayName=bt.displayName;var Zs=[" ","Enter","ArrowUp","ArrowDown"],Qs=[" ","Enter"],me="Select",[Re,ke,Js]=Ts(me),[xe,Vo]=We(me,[Js,Qe]),_e=Qe(),[eo,ce]=xe(me),[to,so]=xe(me),St=s=>{const{__scopeSelect:t,children:o,open:r,defaultOpen:a,onOpenChange:d,value:c,defaultValue:l,onValueChange:i,dir:u,name:h,autoComplete:g,disabled:j,required:y,form:N}=s,m=_e(t),[w,b]=n.useState(null),[f,x]=n.useState(null),[R,D]=n.useState(!1),J=Ze(u),[k,F]=Ce({prop:r,defaultProp:a??!1,onChange:d,caller:me}),[$,Q]=Ce({prop:c,defaultProp:l,onChange:i,caller:me}),G=n.useRef(null),K=w?N||!!w.closest("form"):!0,[z,U]=n.useState(new Set),V=Array.from(z).map(A=>A.props.value).join(";");return e.jsx(Cs,{...m,children:e.jsxs(eo,{required:y,scope:t,trigger:w,onTriggerChange:b,valueNode:f,onValueNodeChange:x,valueNodeHasChildren:R,onValueNodeHasChildrenChange:D,contentId:Ie(),value:$,onValueChange:Q,open:k,onOpenChange:F,dir:J,triggerPointerDownPosRef:G,disabled:j,children:[e.jsx(Re.Provider,{scope:t,children:e.jsx(to,{scope:s.__scopeSelect,onNativeOptionAdd:n.useCallback(A=>{U(L=>new Set(L).add(A))},[]),onNativeOptionRemove:n.useCallback(A=>{U(L=>{const H=new Set(L);return H.delete(A),H})},[]),children:o})}),K?e.jsxs(Yt,{"aria-hidden":!0,required:y,tabIndex:-1,name:h,autoComplete:g,value:$,onChange:A=>Q(A.target.value),disabled:j,form:N,children:[$===void 0?e.jsx("option",{value:""}):null,Array.from(z)]},V):null]})})};St.displayName=me;var Ct="SelectTrigger",Pt=n.forwardRef((s,t)=>{const{__scopeSelect:o,disabled:r=!1,...a}=s,d=_e(o),c=ce(Ct,o),l=c.disabled||r,i=W(t,c.onTriggerChange),u=ke(o),h=n.useRef("touch"),[g,j,y]=Xt(m=>{const w=u().filter(x=>!x.disabled),b=w.find(x=>x.value===c.value),f=Zt(w,m,b);f!==void 0&&c.onValueChange(f.value)}),N=m=>{l||(c.onOpenChange(!0),y()),m&&(c.triggerPointerDownPosRef.current={x:Math.round(m.pageX),y:Math.round(m.pageY)})};return e.jsx(js,{asChild:!0,...d,children:e.jsx(E.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":qt(c.value)?"":void 0,...a,ref:i,onClick:T(a.onClick,m=>{m.currentTarget.focus(),h.current!=="mouse"&&N(m)}),onPointerDown:T(a.onPointerDown,m=>{h.current=m.pointerType;const w=m.target;w.hasPointerCapture(m.pointerId)&&w.releasePointerCapture(m.pointerId),m.button===0&&m.ctrlKey===!1&&m.pointerType==="mouse"&&(N(m),m.preventDefault())}),onKeyDown:T(a.onKeyDown,m=>{const w=g.current!=="";!(m.ctrlKey||m.altKey||m.metaKey)&&m.key.length===1&&j(m.key),!(w&&m.key===" ")&&Zs.includes(m.key)&&(N(),m.preventDefault())})})})});Pt.displayName=Ct;var It="SelectValue",Tt=n.forwardRef((s,t)=>{const{__scopeSelect:o,className:r,style:a,children:d,placeholder:c="",...l}=s,i=ce(It,o),{onValueNodeHasChildrenChange:u}=i,h=d!==void 0,g=W(t,i.onValueNodeChange);return ae(()=>{u(h)},[u,h]),e.jsx(E.span,{...l,ref:g,style:{pointerEvents:"none"},children:qt(i.value)?e.jsx(e.Fragment,{children:c}):d})});Tt.displayName=It;var oo="SelectIcon",Et=n.forwardRef((s,t)=>{const{__scopeSelect:o,children:r,...a}=s;return e.jsx(E.span,{"aria-hidden":!0,...a,ref:t,children:r||"▼"})});Et.displayName=oo;var no="SelectPortal",Rt=s=>e.jsx(As,{asChild:!0,...s});Rt.displayName=no;var pe="SelectContent",kt=n.forwardRef((s,t)=>{const o=ce(pe,s.__scopeSelect),[r,a]=n.useState();if(ae(()=>{a(new DocumentFragment)},[]),!o.open){const d=r;return d?Xe.createPortal(e.jsx(_t,{scope:s.__scopeSelect,children:e.jsx(Re.Slot,{scope:s.__scopeSelect,children:e.jsx("div",{children:s.children})})}),d):null}return e.jsx(At,{...s,ref:t})});kt.displayName=pe;var Y=10,[_t,le]=xe(pe),ro="SelectContentImpl",ao=gs("SelectContent.RemoveScroll"),At=n.forwardRef((s,t)=>{const{__scopeSelect:o,position:r="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:d,onPointerDownOutside:c,side:l,sideOffset:i,align:u,alignOffset:h,arrowPadding:g,collisionBoundary:j,collisionPadding:y,sticky:N,hideWhenDetached:m,avoidCollisions:w,...b}=s,f=ce(pe,o),[x,R]=n.useState(null),[D,J]=n.useState(null),k=W(t,v=>R(v)),[F,$]=n.useState(null),[Q,G]=n.useState(null),K=ke(o),[z,U]=n.useState(!1),V=n.useRef(!1);n.useEffect(()=>{if(x)return Es(x)},[x]),Rs();const A=n.useCallback(v=>{const[I,...O]=K().map(C=>C.ref.current),[P]=O.slice(-1),S=document.activeElement;for(const C of v)if(C===S||(C==null||C.scrollIntoView({block:"nearest"}),C===I&&D&&(D.scrollTop=0),C===P&&D&&(D.scrollTop=D.scrollHeight),C==null||C.focus(),document.activeElement!==S))return},[K,D]),L=n.useCallback(()=>A([F,x]),[A,F,x]);n.useEffect(()=>{z&&L()},[z,L]);const{onOpenChange:H,triggerPointerDownPosRef:p}=f;n.useEffect(()=>{if(x){let v={x:0,y:0};const I=P=>{var S,C;v={x:Math.abs(Math.round(P.pageX)-(((S=p.current)==null?void 0:S.x)??0)),y:Math.abs(Math.round(P.pageY)-(((C=p.current)==null?void 0:C.y)??0))}},O=P=>{v.x<=10&&v.y<=10?P.preventDefault():x.contains(P.target)||H(!1),document.removeEventListener("pointermove",I),p.current=null};return p.current!==null&&(document.addEventListener("pointermove",I),document.addEventListener("pointerup",O,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",I),document.removeEventListener("pointerup",O,{capture:!0})}}},[x,H,p]),n.useEffect(()=>{const v=()=>H(!1);return window.addEventListener("blur",v),window.addEventListener("resize",v),()=>{window.removeEventListener("blur",v),window.removeEventListener("resize",v)}},[H]);const[ee,q]=Xt(v=>{const I=K().filter(S=>!S.disabled),O=I.find(S=>S.ref.current===document.activeElement),P=Zt(I,v,O);P&&setTimeout(()=>P.ref.current.focus())}),Ae=n.useCallback((v,I,O)=>{const P=!V.current&&!O;(f.value!==void 0&&f.value===I||P)&&($(v),P&&(V.current=!0))},[f.value]),De=n.useCallback(()=>x==null?void 0:x.focus(),[x]),he=n.useCallback((v,I,O)=>{const P=!V.current&&!O;(f.value!==void 0&&f.value===I||P)&&G(v)},[f.value]),Ne=r==="popper"?Be:Dt,ge=Ne===Be?{side:l,sideOffset:i,align:u,alignOffset:h,arrowPadding:g,collisionBoundary:j,collisionPadding:y,sticky:N,hideWhenDetached:m,avoidCollisions:w}:{};return e.jsx(_t,{scope:o,content:x,viewport:D,onViewportChange:J,itemRefCallback:Ae,selectedItem:F,onItemLeave:De,itemTextRefCallback:he,focusSelectedItem:L,selectedItemText:Q,position:r,isPositioned:z,searchRef:ee,children:e.jsx(ks,{as:ao,allowPinchZoom:!0,children:e.jsx(_s,{asChild:!0,trapped:f.open,onMountAutoFocus:v=>{v.preventDefault()},onUnmountAutoFocus:T(a,v=>{var I;(I=f.trigger)==null||I.focus({preventScroll:!0}),v.preventDefault()}),children:e.jsx(Ns,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:v=>v.preventDefault(),onDismiss:()=>f.onOpenChange(!1),children:e.jsx(Ne,{role:"listbox",id:f.contentId,"data-state":f.open?"open":"closed",dir:f.dir,onContextMenu:v=>v.preventDefault(),...b,...ge,onPlaced:()=>U(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:T(b.onKeyDown,v=>{const I=v.ctrlKey||v.altKey||v.metaKey;if(v.key==="Tab"&&v.preventDefault(),!I&&v.key.length===1&&q(v.key),["ArrowUp","ArrowDown","Home","End"].includes(v.key)){let P=K().filter(S=>!S.disabled).map(S=>S.ref.current);if(["ArrowUp","End"].includes(v.key)&&(P=P.slice().reverse()),["ArrowUp","ArrowDown"].includes(v.key)){const S=v.target,C=P.indexOf(S);P=P.slice(C+1)}setTimeout(()=>A(P)),v.preventDefault()}})})})})})})});At.displayName=ro;var co="SelectItemAlignedPosition",Dt=n.forwardRef((s,t)=>{const{__scopeSelect:o,onPlaced:r,...a}=s,d=ce(pe,o),c=le(pe,o),[l,i]=n.useState(null),[u,h]=n.useState(null),g=W(t,k=>h(k)),j=ke(o),y=n.useRef(!1),N=n.useRef(!0),{viewport:m,selectedItem:w,selectedItemText:b,focusSelectedItem:f}=c,x=n.useCallback(()=>{if(d.trigger&&d.valueNode&&l&&u&&m&&w&&b){const k=d.trigger.getBoundingClientRect(),F=u.getBoundingClientRect(),$=d.valueNode.getBoundingClientRect(),Q=b.getBoundingClientRect();if(d.dir!=="rtl"){const S=Q.left-F.left,C=$.left-S,ie=k.left-C,de=k.width+ie,Me=Math.max(de,F.width),Le=window.innerWidth-Y,Oe=Ye(C,[Y,Math.max(Y,Le-Me)]);l.style.minWidth=de+"px",l.style.left=Oe+"px"}else{const S=F.right-Q.right,C=window.innerWidth-$.right-S,ie=window.innerWidth-k.right-C,de=k.width+ie,Me=Math.max(de,F.width),Le=window.innerWidth-Y,Oe=Ye(C,[Y,Math.max(Y,Le-Me)]);l.style.minWidth=de+"px",l.style.right=Oe+"px"}const G=j(),K=window.innerHeight-Y*2,z=m.scrollHeight,U=window.getComputedStyle(u),V=parseInt(U.borderTopWidth,10),A=parseInt(U.paddingTop,10),L=parseInt(U.borderBottomWidth,10),H=parseInt(U.paddingBottom,10),p=V+A+z+H+L,ee=Math.min(w.offsetHeight*5,p),q=window.getComputedStyle(m),Ae=parseInt(q.paddingTop,10),De=parseInt(q.paddingBottom,10),he=k.top+k.height/2-Y,Ne=K-he,ge=w.offsetHeight/2,v=w.offsetTop+ge,I=V+A+v,O=p-I;if(I<=he){const S=G.length>0&&w===G[G.length-1].ref.current;l.style.bottom="0px";const C=u.clientHeight-m.offsetTop-m.offsetHeight,ie=Math.max(Ne,ge+(S?De:0)+C+L),de=I+ie;l.style.height=de+"px"}else{const S=G.length>0&&w===G[0].ref.current;l.style.top="0px";const ie=Math.max(he,V+m.offsetTop+(S?Ae:0)+ge)+O;l.style.height=ie+"px",m.scrollTop=I-he+m.offsetTop}l.style.margin=`${Y}px 0`,l.style.minHeight=ee+"px",l.style.maxHeight=K+"px",r==null||r(),requestAnimationFrame(()=>y.current=!0)}},[j,d.trigger,d.valueNode,l,u,m,w,b,d.dir,r]);ae(()=>x(),[x]);const[R,D]=n.useState();ae(()=>{u&&D(window.getComputedStyle(u).zIndex)},[u]);const J=n.useCallback(k=>{k&&N.current===!0&&(x(),f==null||f(),N.current=!1)},[x,f]);return e.jsx(io,{scope:o,contentWrapper:l,shouldExpandOnScrollRef:y,onScrollButtonChange:J,children:e.jsx("div",{ref:i,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:e.jsx(E.div,{...a,ref:g,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Dt.displayName=co;var lo="SelectPopperPosition",Be=n.forwardRef((s,t)=>{const{__scopeSelect:o,align:r="start",collisionPadding:a=Y,...d}=s,c=_e(o);return e.jsx(bs,{...c,...d,ref:t,align:r,collisionPadding:a,style:{boxSizing:"border-box",...d.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Be.displayName=lo;var[io,Ge]=xe(pe,{}),Fe="SelectViewport",Mt=n.forwardRef((s,t)=>{const{__scopeSelect:o,nonce:r,...a}=s,d=le(Fe,o),c=Ge(Fe,o),l=W(t,d.onViewportChange),i=n.useRef(0);return e.jsxs(e.Fragment,{children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),e.jsx(Re.Slot,{scope:o,children:e.jsx(E.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:T(a.onScroll,u=>{const h=u.currentTarget,{contentWrapper:g,shouldExpandOnScrollRef:j}=c;if(j!=null&&j.current&&g){const y=Math.abs(i.current-h.scrollTop);if(y>0){const N=window.innerHeight-Y*2,m=parseFloat(g.style.minHeight),w=parseFloat(g.style.height),b=Math.max(m,w);if(b<N){const f=b+y,x=Math.min(N,f),R=f-x;g.style.height=x+"px",g.style.bottom==="0px"&&(h.scrollTop=R>0?R:0,g.style.justifyContent="flex-end")}}}i.current=h.scrollTop})})})]})});Mt.displayName=Fe;var Lt="SelectGroup",[uo,mo]=xe(Lt),po=n.forwardRef((s,t)=>{const{__scopeSelect:o,...r}=s,a=Ie();return e.jsx(uo,{scope:o,id:a,children:e.jsx(E.div,{role:"group","aria-labelledby":a,...r,ref:t})})});po.displayName=Lt;var Ot="SelectLabel",Bt=n.forwardRef((s,t)=>{const{__scopeSelect:o,...r}=s,a=mo(Ot,o);return e.jsx(E.div,{id:a.id,...r,ref:t})});Bt.displayName=Ot;var Pe="SelectItem",[ho,Ft]=xe(Pe),Ut=n.forwardRef((s,t)=>{const{__scopeSelect:o,value:r,disabled:a=!1,textValue:d,...c}=s,l=ce(Pe,o),i=le(Pe,o),u=l.value===r,[h,g]=n.useState(d??""),[j,y]=n.useState(!1),N=W(t,f=>{var x;return(x=i.itemRefCallback)==null?void 0:x.call(i,f,r,a)}),m=Ie(),w=n.useRef("touch"),b=()=>{a||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return e.jsx(ho,{scope:o,value:r,disabled:a,textId:m,isSelected:u,onItemTextChange:n.useCallback(f=>{g(x=>x||((f==null?void 0:f.textContent)??"").trim())},[]),children:e.jsx(Re.ItemSlot,{scope:o,value:r,disabled:a,textValue:h,children:e.jsx(E.div,{role:"option","aria-labelledby":m,"data-highlighted":j?"":void 0,"aria-selected":u&&j,"data-state":u?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...c,ref:N,onFocus:T(c.onFocus,()=>y(!0)),onBlur:T(c.onBlur,()=>y(!1)),onClick:T(c.onClick,()=>{w.current!=="mouse"&&b()}),onPointerUp:T(c.onPointerUp,()=>{w.current==="mouse"&&b()}),onPointerDown:T(c.onPointerDown,f=>{w.current=f.pointerType}),onPointerMove:T(c.onPointerMove,f=>{var x;w.current=f.pointerType,a?(x=i.onItemLeave)==null||x.call(i):w.current==="mouse"&&f.currentTarget.focus({preventScroll:!0})}),onPointerLeave:T(c.onPointerLeave,f=>{var x;f.currentTarget===document.activeElement&&((x=i.onItemLeave)==null||x.call(i))}),onKeyDown:T(c.onKeyDown,f=>{var R;((R=i.searchRef)==null?void 0:R.current)!==""&&f.key===" "||(Qs.includes(f.key)&&b(),f.key===" "&&f.preventDefault())})})})})});Ut.displayName=Pe;var je="SelectItemText",Vt=n.forwardRef((s,t)=>{const{__scopeSelect:o,className:r,style:a,...d}=s,c=ce(je,o),l=le(je,o),i=Ft(je,o),u=so(je,o),[h,g]=n.useState(null),j=W(t,b=>g(b),i.onItemTextChange,b=>{var f;return(f=l.itemTextRefCallback)==null?void 0:f.call(l,b,i.value,i.disabled)}),y=h==null?void 0:h.textContent,N=n.useMemo(()=>e.jsx("option",{value:i.value,disabled:i.disabled,children:y},i.value),[i.disabled,i.value,y]),{onNativeOptionAdd:m,onNativeOptionRemove:w}=u;return ae(()=>(m(N),()=>w(N)),[m,w,N]),e.jsxs(e.Fragment,{children:[e.jsx(E.span,{id:i.textId,...d,ref:j}),i.isSelected&&c.valueNode&&!c.valueNodeHasChildren?Xe.createPortal(d.children,c.valueNode):null]})});Vt.displayName=je;var Ht="SelectItemIndicator",Wt=n.forwardRef((s,t)=>{const{__scopeSelect:o,...r}=s;return Ft(Ht,o).isSelected?e.jsx(E.span,{"aria-hidden":!0,...r,ref:t}):null});Wt.displayName=Ht;var Ue="SelectScrollUpButton",$t=n.forwardRef((s,t)=>{const o=le(Ue,s.__scopeSelect),r=Ge(Ue,s.__scopeSelect),[a,d]=n.useState(!1),c=W(t,r.onScrollButtonChange);return ae(()=>{if(o.viewport&&o.isPositioned){let l=function(){const u=i.scrollTop>0;d(u)};const i=o.viewport;return l(),i.addEventListener("scroll",l),()=>i.removeEventListener("scroll",l)}},[o.viewport,o.isPositioned]),a?e.jsx(Kt,{...s,ref:c,onAutoScroll:()=>{const{viewport:l,selectedItem:i}=o;l&&i&&(l.scrollTop=l.scrollTop-i.offsetHeight)}}):null});$t.displayName=Ue;var Ve="SelectScrollDownButton",Gt=n.forwardRef((s,t)=>{const o=le(Ve,s.__scopeSelect),r=Ge(Ve,s.__scopeSelect),[a,d]=n.useState(!1),c=W(t,r.onScrollButtonChange);return ae(()=>{if(o.viewport&&o.isPositioned){let l=function(){const u=i.scrollHeight-i.clientHeight,h=Math.ceil(i.scrollTop)<u;d(h)};const i=o.viewport;return l(),i.addEventListener("scroll",l),()=>i.removeEventListener("scroll",l)}},[o.viewport,o.isPositioned]),a?e.jsx(Kt,{...s,ref:c,onAutoScroll:()=>{const{viewport:l,selectedItem:i}=o;l&&i&&(l.scrollTop=l.scrollTop+i.offsetHeight)}}):null});Gt.displayName=Ve;var Kt=n.forwardRef((s,t)=>{const{__scopeSelect:o,onAutoScroll:r,...a}=s,d=le("SelectScrollButton",o),c=n.useRef(null),l=ke(o),i=n.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return n.useEffect(()=>()=>i(),[i]),ae(()=>{var h;const u=l().find(g=>g.ref.current===document.activeElement);(h=u==null?void 0:u.ref.current)==null||h.scrollIntoView({block:"nearest"})},[l]),e.jsx(E.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:T(a.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(r,50))}),onPointerMove:T(a.onPointerMove,()=>{var u;(u=d.onItemLeave)==null||u.call(d),c.current===null&&(c.current=window.setInterval(r,50))}),onPointerLeave:T(a.onPointerLeave,()=>{i()})})}),fo="SelectSeparator",zt=n.forwardRef((s,t)=>{const{__scopeSelect:o,...r}=s;return e.jsx(E.div,{"aria-hidden":!0,...r,ref:t})});zt.displayName=fo;var He="SelectArrow",xo=n.forwardRef((s,t)=>{const{__scopeSelect:o,...r}=s,a=_e(o),d=ce(He,o),c=le(He,o);return d.open&&c.position==="popper"?e.jsx(ys,{...a,...r,ref:t}):null});xo.displayName=He;var go="SelectBubbleInput",Yt=n.forwardRef(({__scopeSelect:s,value:t,...o},r)=>{const a=n.useRef(null),d=W(r,a),c=et(t);return n.useEffect(()=>{const l=a.current;if(!l)return;const i=window.HTMLSelectElement.prototype,h=Object.getOwnPropertyDescriptor(i,"value").set;if(c!==t&&h){const g=new Event("change",{bubbles:!0});h.call(l,t),l.dispatchEvent(g)}},[c,t]),e.jsx(E.select,{...o,style:{...Bs,...o.style},ref:d,defaultValue:t})});Yt.displayName=go;function qt(s){return s===""||s===void 0}function Xt(s){const t=Ss(s),o=n.useRef(""),r=n.useRef(0),a=n.useCallback(c=>{const l=o.current+c;t(l),function i(u){o.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>i(""),1e3))}(l)},[t]),d=n.useCallback(()=>{o.current="",window.clearTimeout(r.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(r.current),[]),[o,a,d]}function Zt(s,t,o){const a=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,d=o?s.indexOf(o):-1;let c=vo(s,Math.max(d,0));a.length===1&&(c=c.filter(u=>u!==o));const i=c.find(u=>u.textValue.toLowerCase().startsWith(a.toLowerCase()));return i!==o?i:void 0}function vo(s,t){return s.map((o,r)=>s[(t+r)%s.length])}var wo=St,Qt=Pt,jo=Tt,No=Et,bo=Rt,Jt=kt,yo=Mt,es=Bt,ts=Ut,So=Vt,Co=Wt,ss=$t,os=Gt,ns=zt;const Po=wo,Io=jo,rs=n.forwardRef(({className:s,children:t,...o},r)=>e.jsxs(Qt,{ref:r,className:M("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...o,children:[t,e.jsx(No,{asChild:!0,children:e.jsx(Ds,{className:"h-4 w-4 opacity-50"})})]}));rs.displayName=Qt.displayName;const as=n.forwardRef(({className:s,...t},o)=>e.jsx(ss,{ref:o,className:M("flex cursor-default items-center justify-center py-1",s),...t,children:e.jsx(Ms,{})}));as.displayName=ss.displayName;const cs=n.forwardRef(({className:s,...t},o)=>e.jsx(os,{ref:o,className:M("flex cursor-default items-center justify-center py-1",s),...t,children:e.jsx(Ls,{})}));cs.displayName=os.displayName;const ls=n.forwardRef(({className:s,children:t,position:o="popper",...r},a)=>e.jsx(bo,{children:e.jsxs(Jt,{ref:a,className:M("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",o==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:o,...r,children:[e.jsx(as,{}),e.jsx(yo,{className:M("p-1",o==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),e.jsx(cs,{})]})}));ls.displayName=Jt.displayName;const To=n.forwardRef(({className:s,...t},o)=>e.jsx(es,{ref:o,className:M("px-2 py-1.5 text-sm font-semibold",s),...t}));To.displayName=es.displayName;const fe=n.forwardRef(({className:s,children:t,...o},r)=>e.jsxs(ts,{ref:r,className:M("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...o,children:[e.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(Co,{children:e.jsx(Os,{className:"h-4 w-4"})})}),e.jsx(So,{children:t})]}));fe.displayName=ts.displayName;const Eo=n.forwardRef(({className:s,...t},o)=>e.jsx(ns,{ref:o,className:M("-mx-1 my-1 h-px bg-muted",s),...t}));Eo.displayName=ns.displayName;function Ho(){var z,U,V,A,L,H;const{user:s}=is(),{profile:t,updateProfile:o}=ds(),r=ms(),[a,d]=n.useState((t==null?void 0:t.displayName)||""),[c,l]=n.useState(!1),[i,u]=n.useState(((z=t==null?void 0:t.preferences)==null?void 0:z.emailNotifications)||!1),[h,g]=n.useState(((U=t==null?void 0:t.preferences)==null?void 0:U.language)||"en"),[j,y]=n.useState(""),[N,m]=n.useState(""),[w,b]=n.useState(""),[f,x]=n.useState(!1),[R,D]=n.useState(""),[J,k]=n.useState(!1),[F,$]=n.useState(!1),Q=async()=>{try{l(!0),await o({displayName:a,preferences:{...t==null?void 0:t.preferences,emailNotifications:i,language:h}}),B.success("Profile updated successfully")}catch(p){console.error("Error updating profile:",p),B.error("Failed to update profile")}finally{l(!1)}},G=async()=>{if(N!==w){B.error("New passwords do not match");return}if(N.length<6){B.error("Password must be at least 6 characters");return}try{x(!0);const p=Ke.credential(s.email||"",j);await ze(s,p),await fs(s,N),B.success("Password updated successfully"),y(""),m(""),b("")}catch(p){console.error("Error changing password:",p),B.error("Failed to change password. Please check your current password.")}finally{x(!1)}},K=async()=>{try{if(k(!0),s.providerData.some(q=>q.providerId==="password")){if(!R){B.error("Please enter your password to confirm deletion"),k(!1);return}const q=Ke.credential(s.email||"",R);await ze(s,q)}const{useUserProfileStore:ee}=await us(()=>import("./index-********.js").then(q=>q.p),["assets/index-********.js","assets/vendor-8eb8bd34.js","assets/index-2ce5e6d7.css"]);await ee.getState().deleteUserProfile(s.uid),await xs(s),B.success("Account deleted successfully"),r("/")}catch(p){console.error("Error deleting account:",p),p.code==="auth/requires-recent-login"?B.error("For security, please log out and log in again before deleting your account."):B.error(p.message||"Failed to delete account. Please try again.")}finally{k(!1)}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm p-4 pt-16",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsx(Z,{variant:"ghost",onClick:()=>r("/"),className:"font-mono",children:"← Back to home"})}),e.jsx("h1",{className:"text-3xl font-mono tracking-wide mb-8 before:content-['#'] before:text-primary before:mr-2",children:"User Profile"}),e.jsxs(Gs,{defaultValue:"profile",className:"w-full mb-8",children:[e.jsxs(xt,{className:"grid w-full md:w-auto grid-cols-3 mb-6",children:[e.jsx(ye,{value:"profile",className:"font-mono",children:"Profile"}),e.jsx(ye,{value:"subscription",className:"font-mono",children:"Subscription"}),e.jsx(ye,{value:"security",className:"font-mono",children:"Security"})]}),e.jsx(Se,{value:"profile",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm col-span-1",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"User Info"}),e.jsx(ne,{children:"Your account information"})]}),e.jsxs(re,{className:"space-y-2",children:[e.jsx("div",{className:"w-20 h-20 mx-auto rounded-md overflow-hidden bg-muted flex items-center justify-center mb-4",children:t!=null&&t.photoURL?e.jsx("img",{src:t.photoURL,alt:"Profile",className:"w-full h-full object-cover"}):e.jsx("div",{className:"w-full h-full bg-primary/10 flex items-center justify-center",children:e.jsx("span",{className:"text-xl font-mono",children:((V=t==null?void 0:t.displayName)==null?void 0:V[0])||((A=s.email)==null?void 0:A[0])||"?"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Email"}),e.jsx("p",{className:"font-mono",children:s.email})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Account Plan"}),e.jsx("p",{className:"font-mono uppercase",children:(t==null?void 0:t.accountPlan)||_.FREE})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"User ID"}),e.jsx("p",{className:"font-mono text-xs truncate",children:s.uid})]})]})]}),e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm col-span-1 md:col-span-2",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"Edit Profile"}),e.jsx(ne,{children:"Update your profile information"})]}),e.jsxs(re,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"displayName",children:"Display Name"}),e.jsx(ve,{id:"displayName",value:a,onChange:p=>d(p.target.value),className:"font-mono"})]}),e.jsx(we,{className:"my-4"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(ue,{children:"Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive emails about account updates and new features"})]}),e.jsx(yt,{checked:i,onCheckedChange:u})]}),e.jsx(we,{className:"my-4"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"language",children:"Documentation Language"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Select the language for generated documentation"}),e.jsxs(Po,{value:h,onValueChange:g,children:[e.jsx(rs,{id:"language",className:"w-full font-mono",children:e.jsx(Io,{placeholder:"Select language"})}),e.jsxs(ls,{children:[e.jsx(fe,{value:"en",children:"English"}),e.jsx(fe,{value:"de",children:"German (Deutsch)"}),e.jsx(fe,{value:"fr",children:"French (Français)"}),e.jsx(fe,{value:"it",children:"Italian (Italiano)"}),e.jsx(fe,{value:"es",children:"Spanish (Español)"})]})]})]}),e.jsx(we,{className:"my-4"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Account Created"}),e.jsx("p",{className:"font-mono",children:t!=null&&t.createdAt?new Date(t.createdAt).toLocaleString():"Unknown"})]})]}),e.jsxs(be,{className:"justify-end space-x-2",children:[e.jsx(Z,{variant:"outline",onClick:()=>{var p,ee;d((t==null?void 0:t.displayName)||""),u(((p=t==null?void 0:t.preferences)==null?void 0:p.emailNotifications)||!1),g(((ee=t==null?void 0:t.preferences)==null?void 0:ee.language)||"en")},children:"Reset"}),e.jsx(Z,{onClick:Q,disabled:c||a===(t==null?void 0:t.displayName)&&i===((L=t==null?void 0:t.preferences)==null?void 0:L.emailNotifications)&&h===((H=t==null?void 0:t.preferences)==null?void 0:H.language),children:c?"Updating...":"Save Changes"})]})]})]})}),e.jsx(Se,{value:"subscription",className:"space-y-6",children:e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"Subscription Plan"}),e.jsx(ne,{children:"Manage your subscription and billing"})]}),e.jsxs(re,{className:"space-y-6",children:[e.jsxs("div",{className:"p-4 bg-primary/5 rounded-md border border-border/40",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold font-mono tracking-wide uppercase",children:[(t==null?void 0:t.accountPlan)||_.FREE," Plan"]}),(t==null?void 0:t.accountPlan)!==_.FREE&&(t==null?void 0:t.hasActiveSubscription)&&e.jsx("p",{className:"text-sm text-green-500",children:"Active"})]}),(t==null?void 0:t.accountPlan)!==_.FREE&&(t==null?void 0:t.hasActiveSubscription)&&e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Next Billing Date"}),e.jsx("p",{className:"font-mono",children:t!=null&&t.accountExpiryDate?new Date(t.accountExpiryDate).toLocaleDateString():"Unknown"})]})]}),e.jsx(we,{className:"my-4"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-semibold mb-2",children:"Plan Features:"}),e.jsxs("ul",{className:"space-y-1 text-sm",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[X[(t==null?void 0:t.accountPlan)||_.FREE].maxDocuments," Documents"]})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[(X[(t==null?void 0:t.accountPlan)||_.FREE].maxStorageBytes/(1024*1024)).toFixed(0)," MB Storage"]})]}),X[(t==null?void 0:t.accountPlan)||_.FREE].allowAdvancedAnalytics&&e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Advanced Analytics"})]}),X[(t==null?void 0:t.accountPlan)||_.FREE].allowBatchProcessing&&e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Batch Processing"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold font-mono tracking-wide mb-4",children:"Upgrade Your Plan"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[(t==null?void 0:t.accountPlan)!==_.PRO&&e.jsxs(te,{className:"border-border/40 hover:border-primary/40 transition-colors cursor-pointer",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"PRO"}),e.jsxs(ne,{className:"text-2xl font-bold",children:["$",X[_.PRO].price,e.jsx("span",{className:"text-sm font-normal",children:"/month"})]})]}),e.jsx(re,{className:"space-y-2 text-sm",children:e.jsxs("ul",{className:"space-y-1",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[X[_.PRO].maxDocuments," Documents"]})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[(X[_.PRO].maxStorageBytes/(1024*1024)).toFixed(0)," MB Storage"]})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Advanced Analytics"})]})]})}),e.jsx(be,{children:e.jsx(Z,{className:"w-full font-mono",disabled:(t==null?void 0:t.accountPlan)===_.PRO,children:"Upgrade to Pro"})})]}),(t==null?void 0:t.accountPlan)!==_.ENTERPRISE&&e.jsxs(te,{className:"border-border/40 hover:border-primary/40 transition-colors cursor-pointer",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"ENTERPRISE"}),e.jsxs(ne,{className:"text-2xl font-bold",children:["$",X[_.ENTERPRISE].price,e.jsx("span",{className:"text-sm font-normal",children:"/month"})]})]}),e.jsx(re,{className:"space-y-2 text-sm",children:e.jsxs("ul",{className:"space-y-1",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[X[_.ENTERPRISE].maxDocuments," Documents"]})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsxs("span",{children:[(X[_.ENTERPRISE].maxStorageBytes/(1024*1024*1024)).toFixed(0)," GB Storage"]})]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Team Collaboration"})]})]})}),e.jsx(be,{children:e.jsx(Z,{className:"w-full font-mono",disabled:(t==null?void 0:t.accountPlan)===_.ENTERPRISE,children:"Upgrade to Enterprise"})})]})]}),e.jsx("p",{className:"text-sm text-muted-foreground mt-4",children:"* Payment processing will be available soon. Contact support for manual upgrades."})]})]})]})}),e.jsxs(Se,{value:"security",className:"space-y-6",children:[e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"Authentication Methods"}),e.jsx(ne,{children:"Manage how you sign in to your account"})]}),e.jsxs(re,{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Email and Password"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[s.email,s.providerData.some(p=>p.providerId==="password")?" (Connected)":" (Not connected)"]})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Google"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.providerData.some(p=>p.providerId==="google.com")?"Connected to Google Account":"Not connected"})]}),e.jsx(Z,{variant:"outline",onClick:async()=>{if(s.providerData.some(p=>p.providerId==="google.com")){B.info("Google account already connected");return}try{const p=new ps;p.addScope("profile"),p.addScope("email"),await hs(s,p),B.success("Google account connected successfully"),window.location.reload()}catch(p){console.error("Error linking Google account:",p),B.error(p.message||"Failed to connect Google account")}},children:s.providerData.some(p=>p.providerId==="google.com")?"Connected":"Connect"})]})]})]}),s.providerData.some(p=>p.providerId==="password")&&e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg",children:"Password Management"}),e.jsx(ne,{children:"Update your password"})]}),e.jsxs(re,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"currentPassword",children:"Current Password"}),e.jsx(ve,{id:"currentPassword",type:"password",value:j,onChange:p=>y(p.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"newPassword",children:"New Password"}),e.jsx(ve,{id:"newPassword",type:"password",value:N,onChange:p=>m(p.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"confirmPassword",children:"Confirm New Password"}),e.jsx(ve,{id:"confirmPassword",type:"password",value:w,onChange:p=>b(p.target.value),className:"font-mono"})]})]}),e.jsx(be,{className:"justify-end",children:e.jsx(Z,{onClick:G,disabled:f||!j||!N||!w||N!==w,children:f?"Updating Password...":"Update Password"})})]}),e.jsxs(te,{className:"border-border/40 bg-card/80 backdrop-blur-sm border-destructive/20",children:[e.jsxs(se,{children:[e.jsx(oe,{className:"font-mono text-lg text-destructive",children:"Delete Account"}),e.jsx(ne,{children:"Permanently delete your account and all data"})]}),e.jsxs(re,{children:[e.jsx("p",{className:"text-sm mb-4",children:"This action cannot be undone. All your data will be permanently removed."}),F?e.jsxs("div",{className:"space-y-4",children:[s.providerData.some(p=>p.providerId==="password")&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(ue,{htmlFor:"deleteConfirmPassword",children:"Enter your password to confirm"}),e.jsx(ve,{id:"deleteConfirmPassword",type:"password",value:R,onChange:p=>D(p.target.value),className:"font-mono"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(Z,{variant:"outline",onClick:()=>{$(!1),D("")},children:"Cancel"}),e.jsx(Z,{variant:"destructive",onClick:K,disabled:J||s.providerData.some(p=>p.providerId==="password")&&!R,children:J?"Deleting...":"Confirm Delete"})]})]}):e.jsx(Z,{variant:"destructive",onClick:()=>$(!0),children:"Delete Account"})]})]})]})]})]})})}export{Ho as default};
