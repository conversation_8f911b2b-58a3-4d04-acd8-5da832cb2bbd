import { useState, useEffect } from "react";
import { <PERSON>ert<PERSON><PERSON><PERSON>, Clock, FileWarning, X, Map, Layers, Cpu } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Props {
  fileSize: number;
  onDismiss?: () => void;
}

export function FileSizeWarning({ fileSize, onDismiss, isGisCode }: Props & { isGisCode?: boolean }) {
  const [visible, setVisible] = useState(true);
  
  // Auto-dismiss after 10 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      if (onDismiss) onDismiss();
    }, 10000);
    
    return () => clearTimeout(timer);
  }, [onDismiss]);
  
  if (!visible) return null;
  
  // Convert bytes to appropriate unit
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} bytes`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };
  
  // Determine severity level
  const getSeverity = (bytes: number): 'info' | 'warning' | 'critical' => {
    if (bytes < 100 * 1024) return 'info'; // Under 100KB
    if (bytes < 1024 * 1024) return 'warning'; // Under 1MB
    return 'critical'; // 1MB or larger
  };
  
  // Estimate processing time based on file size with GIS-specific adjustments
  const getEstimatedTime = (bytes: number, isGis: boolean = false): string => {
    // GIS processing typically takes longer due to spatial operations
    const multiplier = isGis ? 1.5 : 1;
    
    // Very rough estimation with GIS factor
    if (bytes < 50 * 1024) return "less than 1 second";
    if (bytes < 200 * 1024) return `${isGis ? '1-3' : '1-2'} seconds`;
    if (bytes < 500 * 1024) return `${isGis ? '3-5' : '2-3'} seconds`;
    if (bytes < 1024 * 1024) return `${isGis ? '5-8' : '3-5'} seconds`;
    if (bytes < 2 * 1024 * 1024) return `${isGis ? '8-15' : '5-10'} seconds`;
    return `${isGis ? '15+' : '10+'} seconds`;
  };
  
  // Calculate a percentage for the progress bar
  const getSizePercentage = (bytes: number): number => {
    const MAX_RECOMMENDED = isGisCode ? 3 * 1024 * 1024 : 5 * 1024 * 1024; // Lower threshold for GIS files
    return Math.min(Math.round((bytes / MAX_RECOMMENDED) * 100), 100);
  };
  
  const severity = getSeverity(fileSize);
  const formattedSize = formatFileSize(fileSize);
  const sizePercentage = getSizePercentage(fileSize);
  const estimatedTime = getEstimatedTime(fileSize, isGisCode);
  
  // For timing visualizer
  const [progress, setProgress] = useState(0);
  
  // Animate the progress bar
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 1;
      });
    }, 100);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <Alert 
      className={`
        mb-4 border-l-4 transition-colors relative overflow-hidden backdrop-blur-[2px]
        ${severity === 'info' ? 'border-l-blue-500 bg-blue-500/10 dark:bg-blue-950/20' : ''}
        ${severity === 'warning' ? 'border-l-amber-500 bg-amber-500/10 dark:bg-amber-950/20' : ''}
        ${severity === 'critical' ? 'border-l-red-500 bg-red-500/10 dark:bg-red-950/20' : ''}
      `}
      style={{
        animation: 'fadeIn 0.3s ease-out forwards',
        boxShadow: severity === 'critical' ? '0 4px 12px rgba(239, 68, 68, 0.1)' : 'none',
      }}
    >
      {/* Hexagonal Grid Background for GIS style consistency */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none z-0">
        <div className="h-full w-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='49' viewBox='0 0 28 49'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='hexagons' fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M13.99 9.25l13 7.5v15l-13 7.5L1 31.75v-15l12.99-7.5zM3 17.9v12.7l10.99 6.34 11-6.35V17.9l-11-6.34L3 17.9zM0 15l12.98-7.5V0h-2v6.35L0 12.69v2.3zm0 18.5L12.98 41v8h-2v-6.85L0 35.81v-2.3zM15 0v7.5L27.99 15H28v-2.31h-.01L17 6.35V0h-2zm0 49v-8l12.99-7.5H28v2.31h-.01L17 42.15V49h-2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>
      
      {/* Brutalist angular overlays */}
      {severity === 'critical' && (
        <div className="absolute top-0 right-0 h-full w-16 bg-red-500/5 skew-x-[-15deg] -mr-5 pointer-events-none z-0"></div>
      )}
      {isGisCode && (
        <div className="absolute top-0 left-12 h-full w-12 bg-primary/5 skew-x-[30deg] -ml-5 pointer-events-none z-0"></div>
      )}
      
      <div className="flex justify-between items-start w-full">
        <div className="flex gap-2">
        {isGisCode ? (
          <div className="relative">
            <Map size={18} className={`
              ${severity === 'info' ? 'text-blue-500' : ''}
              ${severity === 'warning' ? 'text-amber-500' : ''}
              ${severity === 'critical' ? 'text-red-500' : ''}
            `} />
            <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-card flex items-center justify-center">
              <Layers size={8} className="text-primary" />
            </div>
          </div>
        ) : (
          <>
            {severity === 'info' && <AlertCircle size={18} className="text-blue-500" />}
            {severity === 'warning' && <Clock size={18} className="text-amber-500" />}
            {severity === 'critical' && <FileWarning size={18} className="text-red-500" />}
          </>
        )}
          <div className="flex-1">
            <AlertTitle className="text-sm font-mono tracking-wide flex items-center gap-2">
              {isGisCode ? 'GIS ' : ''}
              {severity === 'info' && 'File Size Info'}
              {severity === 'warning' && 'Large File Warning'}
              {severity === 'critical' && 'Very Large File Warning'}
              
              <span className="font-mono text-xs px-1.5 py-0.5 rounded-sm bg-muted/30 border border-muted/20 backdrop-blur-sm">
                {formattedSize}
              </span>
            </AlertTitle>
            
            <AlertDescription className="text-xs mt-1 space-y-2">
              <div>
                {severity === 'info' && (
                  <>{isGisCode ? 'Small GIS file, validation and spatial analysis will be quick.' : 'Small file, processing will be quick.'}</>
                )}
                {severity === 'warning' && (
                  <>{isGisCode ? 'Medium-sized GIS file, spatial analysis may take longer than standard code.' : 'Medium-sized file, processing may take a moment.'}</>
                )}
                {severity === 'critical' && (
                  <>{isGisCode ? 'Large GIS file, spatial operations and validation may be resource-intensive. Consider isolating core workflow logic.' : 'Large file, this may affect performance. Consider splitting into smaller files for better results.'}</>
                )}
              </div>
              
              {/* Size visualization */}
              <div className="space-y-1">
                <div className="flex items-center justify-between text-[10px] text-muted-foreground">
                  <span>File size</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-1 cursor-help">
                          <span className="whitespace-nowrap">Est. processing time: {estimatedTime}</span>
                          {isGisCode && <Cpu size={10} className="text-primary/70" />}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="max-w-[240px]">
                        <p className="text-xs">
                          {isGisCode 
                            ? 'Estimated time to validate and analyze this GIS code. Spatial operations typically require more processing time.'
                            : 'Estimated time to validate and analyze this code file'}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                <Progress 
                  value={sizePercentage} 
                  className={`h-1.5 ${sizePercentage > 80 ? 'bg-muted/30' : 'bg-muted/20'}`}
                  style={{
                    clipPath: 'polygon(0 0, 100% 0, 95% 100%, 5% 100%)', // Brutalist angular clip
                  }}
                />
                
                <div className="flex justify-between text-[10px] text-muted-foreground">
                  <span className="relative">
                    <span>0 KB</span>
                    {/* Brutalist angle element */}
                    {isGisCode && <span className="absolute -left-2 top-[4px] h-[1px] w-2 bg-primary/30 rotate-45"></span>}
                  </span>
                  <span className="relative px-1 bg-muted/10 -mx-1 backdrop-blur-sm rounded-sm">
                    {isGisCode ? 'Optimal for GIS (&lt;3 MB)' : 'Optimal (&lt;5 MB)'}
                  </span>
                  <span className="relative">
                    {isGisCode ? '3 MB+' : '5 MB+'}
                    {/* Brutalist angle element */}
                    {severity === 'critical' && <span className="absolute -right-2 top-[4px] h-[1px] w-2 bg-red-500/30 rotate-45"></span>}
                  </span>
                </div>
              </div>
              
              {severity === 'critical' && (
                <div className="text-[10px] border-l-2 border-red-500/30 pl-2 italic">
                  {isGisCode ? (
                    <>
                      <span className="font-semibold">GIS Performance Guidance:</span> Large spatial analysis code may affect browser performance. 
                      Consider isolating the core workflow components like parameter definition and main spatial functions. 
                      Complex models with many spatial operations benefit from focused documentation.
                    </>
                  ) : (
                    <>Very large files may cause browser performance issues during validation and analysis. 
                      For scripts over 3MB, consider focusing on core functionality sections.</>  
                  )}
                </div>
              )}
            </AlertDescription>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="icon"
          className="h-5 w-5 rounded-full"
          onClick={() => {
            setVisible(false);
            if (onDismiss) onDismiss();
          }}
        >
          <X size={12} />
        </Button>
      </div>
      
      <style jsx="true">{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        /* Brutalist angle decorations */
        .slashed-bg::before {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          width: 20%;
          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.05));
          clip-path: polygon(0 0, 100% 0, 100% 100%, 20% 100%);
        }
      `}</style>
    </Alert>
  );
}