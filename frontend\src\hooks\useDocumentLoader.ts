/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Hook für das Laden und Verwalten von Dokumenten
 */

import { useState, useEffect } from 'react';
import { doc, collection, getDoc, getDocs, getFirestore } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { Document } from '../services/documentStore';
import { COLLECTIONS } from '../utils/firebaseConfig';
import { fixAllDocumentPermissions } from '../utils/documentPermissions';
import { toast } from 'sonner';

interface UseDocumentLoaderProps {
  user: any;
  docId?: string;
  viewMode?: boolean;
  editMode?: boolean;
}

interface UseDocumentLoaderReturn {
  viewingDocument: Document | null;
  editingDocument: Document | null;
  setViewingDocument: (doc: Document | null) => void;
  setEditingDocument: (doc: Document | null) => void;
  loadDocument: (docId: string, mode: 'view' | 'edit') => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useDocumentLoader({
  user,
  docId,
  viewMode,
  editMode
}: UseDocumentLoaderProps): UseDocumentLoaderReturn {
  const [viewingDocument, setViewingDocument] = useState<Document | null>(null);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadDocument = async (documentId: string, mode: 'view' | 'edit') => {
    if (!user?.uid) {
      toast.error('Sie müssen angemeldet sein, um dieses Dokument zu laden');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      toast.loading('Dokument wird geladen...');

      // Dokument-Referenz abrufen
      const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, documentId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        throw new Error('Dokument nicht gefunden');
      }

      const metaData = docSnapshot.data();
      console.log('[Dokument Loader] MetaData:', metaData);
      console.log('[Dokument Loader] Benutzer UID:', user?.uid);
      console.log('[Dokument Loader] Dokument UserID:', metaData?.userId);

      // Berechtigungen prüfen
      if (metaData.userId !== user.uid && !metaData.isPublic) {
        console.log('[Dokument Loader] Berechtigungen werden repariert...');
        toast.loading('Dokumentberechtigungen werden repariert...');
        await fixAllDocumentPermissions(user.uid);

        // Erneut versuchen nach Berechtigungsreparatur
        const updatedSnapshot = await getDoc(docRef);
        if (!updatedSnapshot.exists() || 
            (updatedSnapshot.data().userId !== user.uid && !updatedSnapshot.data().isPublic)) {
          throw new Error('Fehlende oder unzureichende Berechtigungen');
        }
      }

      console.log('[Dokument Loader] Inhalts-Subkollektion wird abgerufen...');
      // Inhalts-Subkollektion abrufen
      const contentSnap = await getDocs(collection(docRef, 'content'));

      if (contentSnap.empty) {
        throw new Error('Dokumentinhalt nicht gefunden');
      }

      const contentData = contentSnap.docs[0].data();

      // Vollständiges Dokument-Objekt erstellen
      const document: Document = {
        id: documentId,
        title: metaData.title,
        description: metaData.description,
        createdAt: metaData.createdAt?.toDate(),
        updatedAt: metaData.updatedAt?.toDate(),
        userId: metaData.userId,
        language: metaData.language,
        codeSize: metaData.codeSize,
        isPublic: metaData.isPublic,
        tags: metaData.tags || [],
        project_context: metaData.project_context,
        content: {
          originalCode: contentData.originalCode,
          generatedDocumentation: contentData.generatedDocumentation,
          parameters: contentData.parameters,
          toolsUsed: contentData.toolsUsed,
          workflow: contentData.workflow,
          manualEdits: contentData.manualEdits?.map((edit: any) => ({
            ...edit,
            timestamp: edit.timestamp?.toDate()
          })),
          exportCache: contentData.exportCache || []
        }
      };

      console.log('[Dokument Loader] Dokument erfolgreich geladen:', document);

      // Dokument je nach Modus setzen
      if (mode === 'view') {
        setViewingDocument(document);
        toast.success(`Dokument wird angezeigt: ${document.title}`);
      } else if (mode === 'edit') {
        setEditingDocument(document);
        toast.success(`Dokument wird bearbeitet: ${document.title}`);
      }

    } catch (err) {
      console.error('Fehler beim Laden des Dokuments:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unbekannter Fehler';
      setError(errorMessage);
      
      if (errorMessage.includes('Berechtigungen')) {
        toast.error('Berechtigungsfehler: Versuchen Sie zuerst die Schaltfläche "Berechtigungen reparieren"');
      } else {
        toast.error(`Fehler beim Laden des Dokuments: ${errorMessage}`);
      }
    } finally {
      setIsLoading(false);
      toast.dismiss();
    }
  };

  // URL-Parameter beim Mount prüfen
  useEffect(() => {
    if (docId && user?.uid) {
      if (viewMode) {
        loadDocument(docId, 'view');
      } else if (editMode) {
        loadDocument(docId, 'edit');
      }
    }
  }, [docId, viewMode, editMode, user?.uid]);

  return {
    viewingDocument,
    editingDocument,
    setViewingDocument,
    setEditingDocument,
    loadDocument,
    isLoading,
    error
  };
}
