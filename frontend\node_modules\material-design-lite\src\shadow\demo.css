/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.demo-page--shadow .demo-preview-block {
  height: 100px;
}

.demo-shadow-card {
  background-color: #fff;
  border-radius: 2px;
  display: block;
  height: 72px;
  margin-bottom: 20px;
  margin-right: 32px;
  padding: 10px;
  text-align: center;
  float: left;
  color: #9E9E9E;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: opacity, transform;
  width: 72px;
}

@media screen and (max-width: 360px ) {
  .demo-shadow-card {
    width: 27px;
  }
}
