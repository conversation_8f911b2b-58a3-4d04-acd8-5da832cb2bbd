// This file contains utilities for bypassing authentication during development
// It should never be used in production

import { mode, Mode } from "app";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// Simple utility to bypass authentication in development mode
export const useDevBypass = () => {
  const isDev = mode === Mode.DEV;
  const navigate = useNavigate();
  const [isReady, setIsReady] = useState(false);
  
  // Initialize and check for any existing bypass on component mount
  useEffect(() => {
    setIsReady(true);
  }, []);
  
  // Function to bypass authentication
  // Only works in development mode
  const bypassAuth = (redirectTo: string = "/editor") => {
    if (isDev) {
      // Create a mock user object with minimal required properties
      const mockUser = {
        uid: "dev-user-1234",
        email: "<EMAIL>",
        displayName: "Development User",
        emailVerified: true,
      };
      
      // Store the mock user in localStorage to simulate being logged in
      localStorage.setItem("devBypassUser", JSON.stringify(mockUser));
      
      // Use React Router's navigate function for reliable navigation within React context
      if (isReady) {
        navigate(redirectTo, { replace: true });
      } else {
        // Fallback for when the hook is used outside React Router context
        setTimeout(() => {
          window.location.href = redirectTo;
        }, 100);
      }
    }
  };
  
  // Check if we have a bypassed user
  const getBypassedUser = () => {
    if (!isDev) return null;
    
    try {
      const userStr = localStorage.getItem("devBypassUser");
      return userStr ? JSON.parse(userStr) : null;
    } catch (e) {
      console.error("Error parsing bypassed user:", e);
      return null;
    }
  };
  
  // Clear the bypassed user
  const clearBypassedUser = () => {
    if (isDev) {
      localStorage.removeItem("devBypassUser");
    }
  };
  
  return {
    bypassAuth,
    getBypassedUser,
    clearBypassedUser,
    isDev
  };
};