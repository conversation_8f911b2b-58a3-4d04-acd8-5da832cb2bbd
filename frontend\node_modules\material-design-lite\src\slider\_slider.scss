/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import "../variables";

// Some CSS magic to target only IE.
_:-ms-input-placeholder, :root .mdl-slider.mdl-slider.is-upgraded {
  -ms-appearance: none;
  // The thumb can't overflow the track or the rest of the control in IE, so we
  // need to make it tall enough to contain the largest version of the thumb.
  height: 32px;
  margin: 0;
}

// Slider component (styled input[type=range]).
.mdl-slider {
  width: calc(100% - 40px);
  margin: 0 20px;

  &.is-upgraded {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 2px;
    background: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    outline: 0;
    padding: 0;
    color: $range-color;
    align-self: center;
    z-index: 1;
    cursor: pointer;


    // Disable default focus on Firefox.
    &::-moz-focus-outer {
      border: 0;
    }

    // Disable tooltip on IE.
    &::-ms-tooltip {
      display: none;
    }


    /**************************** Tracks ****************************/
    &::-webkit-slider-runnable-track {
      background: transparent;
    }

    &::-moz-range-track {
      background: transparent;
      border: none;
    }

    &::-ms-track {
      background: none;
      color: transparent;
      height: 2px;
      width: 100%;
      border: none;
    }

    &::-ms-fill-lower {
      padding: 0;
      // Margin on -ms-track doesn't work right, so we use gradients on the
      // fills.
      background: linear-gradient(to right,
      transparent,
      transparent 16px,
      $range-color 16px,
      $range-color 0);
    }

    &::-ms-fill-upper {
      padding: 0;
      // Margin on -ms-track doesn't work right, so we use gradients on the
      // fills.
      background: linear-gradient(to left,
      transparent,
      transparent 16px,
      $range-bg-color 16px,
      $range-bg-color 0);
    }


    /**************************** Thumbs ****************************/
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 12px;
      height: 12px;
      box-sizing: border-box;
      border-radius: 50%;
      background: $range-color;
      border: none;
      transition: transform 0.18s $animation-curve-default,
      border 0.18s $animation-curve-default,
      box-shadow 0.18s $animation-curve-default,
      background 0.28s $animation-curve-default;
    }

    &::-moz-range-thumb {
      -moz-appearance: none;
      width: 12px;
      height: 12px;
      box-sizing: border-box;
      border-radius: 50%;
      background-image: none;
      background: $range-color;
      border: none;
      // -moz-range-thumb doesn't currently support transitions.
    }

    &:focus:not(:active)::-webkit-slider-thumb {
      box-shadow: 0 0 0 10px $range-faded-color;
    }

    &:focus:not(:active)::-moz-range-thumb {
      box-shadow: 0 0 0 10px $range-faded-color;
    }

    &:active::-webkit-slider-thumb {
      background-image: none;
      background: $range-color;
      transform: scale(1.5);
    }

    &:active::-moz-range-thumb {
      background-image: none;
      background: $range-color;
      transform: scale(1.5);
    }

    &::-ms-thumb {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 50%;
      background: $range-color;
      transform: scale(0.375);
      // -ms-thumb doesn't currently support transitions, but leaving this here
      // in case support ever gets added.
      transition: transform 0.18s $animation-curve-default,
      background 0.28s $animation-curve-default;
    }

    &:focus:not(:active)::-ms-thumb {
      background: radial-gradient(circle closest-side,
      $range-color 0%,
      $range-color 37.5%,
      $range-faded-color 37.5%,
      $range-faded-color 100%);
      transform: scale(1);
    }

    &:active::-ms-thumb {
      background: $range-color;
      transform: scale(0.5625);
    }


    /**************************** 0-value ****************************/
    &.is-lowest-value::-webkit-slider-thumb {
      border: 2px solid $range-bg-color;
      background: transparent;
    }

    &.is-lowest-value::-moz-range-thumb {
      border: 2px solid $range-bg-color;
      background: transparent;
    }

    &.is-lowest-value +
        .mdl-slider__background-flex > .mdl-slider__background-upper {
      left: 6px;
    }

    &.is-lowest-value:focus:not(:active)::-webkit-slider-thumb {
      box-shadow: 0 0 0 10px $range-bg-focus-color;
      background: $range-bg-focus-color;
    }

    &.is-lowest-value:focus:not(:active)::-moz-range-thumb {
      box-shadow: 0 0 0 10px $range-bg-focus-color;
      background: $range-bg-focus-color;
    }

    &.is-lowest-value:active::-webkit-slider-thumb {
      border: 1.6px solid $range-bg-color;
      transform: scale(1.5);
    }

    &.is-lowest-value:active +
        .mdl-slider__background-flex > .mdl-slider__background-upper {
      left: 9px;
    }

    &.is-lowest-value:active::-moz-range-thumb {
      border: 1.5px solid $range-bg-color;
      transform: scale(1.5);
    }

    &.is-lowest-value::-ms-thumb {
      background: radial-gradient(circle closest-side,
      transparent 0%,
      transparent 66.67%,
      $range-bg-color 66.67%,
      $range-bg-color 100%);
    }

    &.is-lowest-value:focus:not(:active)::-ms-thumb {
      background: radial-gradient(circle closest-side,
      $range-bg-focus-color 0%,
      $range-bg-focus-color 25%,
      $range-bg-color 25%,
      $range-bg-color 37.5%,
      $range-bg-focus-color 37.5%,
      $range-bg-focus-color 100%);
      transform: scale(1);
    }

    &.is-lowest-value:active::-ms-thumb {
      transform: scale(0.5625);
      background: radial-gradient(circle closest-side,
      transparent 0%,
      transparent 77.78%,
      $range-bg-color 77.78%,
      $range-bg-color 100%);
    }

    &.is-lowest-value::-ms-fill-lower {
      background: transparent;
    }

    &.is-lowest-value::-ms-fill-upper {
      margin-left: 6px;
    }

    &.is-lowest-value:active::-ms-fill-upper {
      margin-left: 9px;
    }

    /**************************** Disabled ****************************/

    &:disabled:focus::-webkit-slider-thumb,
    &:disabled:active::-webkit-slider-thumb,
    &:disabled::-webkit-slider-thumb {
      transform: scale(0.667);
      background: $range-bg-color;
    }

    &:disabled:focus::-moz-range-thumb,
    &:disabled:active::-moz-range-thumb,
    &:disabled::-moz-range-thumb {
      transform: scale(0.667);
      background: $range-bg-color;
    }

    &:disabled +
        .mdl-slider__background-flex > .mdl-slider__background-lower {
      background-color: $range-bg-color;
      left: -6px;
    }

    &:disabled +
        .mdl-slider__background-flex > .mdl-slider__background-upper {
      left: 6px;
    }

    &.is-lowest-value:disabled:focus::-webkit-slider-thumb,
    &.is-lowest-value:disabled:active::-webkit-slider-thumb,
    &.is-lowest-value:disabled::-webkit-slider-thumb {
      border: 3px solid $range-bg-color;
      background: transparent;
      transform: scale(0.667);
    }

    &.is-lowest-value:disabled:focus::-moz-range-thumb,
    &.is-lowest-value:disabled:active::-moz-range-thumb,
    &.is-lowest-value:disabled::-moz-range-thumb {
      border: 3px solid $range-bg-color;
      background: transparent;
      transform: scale(0.667);
    }

    &.is-lowest-value:disabled:active +
        .mdl-slider__background-flex > .mdl-slider__background-upper {
      left: 6px;
    }

    &:disabled:focus::-ms-thumb,
    &:disabled:active::-ms-thumb,
    &:disabled::-ms-thumb {
      transform: scale(0.25);
      background: $range-bg-color;
    }

    &.is-lowest-value:disabled:focus::-ms-thumb,
    &.is-lowest-value:disabled:active::-ms-thumb,
    &.is-lowest-value:disabled::-ms-thumb {
      transform: scale(0.25);
      background: radial-gradient(circle closest-side,
      transparent 0%,
      transparent 50%,
      $range-bg-color 50%,
      $range-bg-color 100%);
    }

    &:disabled::-ms-fill-lower {
      margin-right: 6px;
      background: linear-gradient(to right,
      transparent,
      transparent 25px,
      $range-bg-color 25px,
      $range-bg-color 0);
    }

    &:disabled::-ms-fill-upper {
      margin-left: 6px;
    }

    &.is-lowest-value:disabled:active::-ms-fill-upper {
      margin-left: 6px;
    }
  }
}

  // Since we need to specify a height of 32px in IE, we add a class here for a
  // container that brings it back to a reasonable height.
  .mdl-slider__ie-container {
    height: 18px;
    overflow: visible;
    border: none;
    margin: none;
    padding: none;
  }

  // We use a set of divs behind the track to style it in all non-IE browsers.
  // This one contains both the background and the slider.
  .mdl-slider__container {
    height: 18px;
    position: relative;
    background: none;
    display: flex;
    flex-direction: row;
  }

  // This one sets up a flex box for the styled upper and lower portions of the
  // the slider track.
  .mdl-slider__background-flex {
    background: transparent;
    position: absolute;
    height: 2px;
    width: calc(100% - 52px);
    top: 50%;
    left: 0;
    margin: 0 26px;
    display: flex;
    overflow: hidden;
    border: 0;
    padding: 0;
    transform: translate(0, -1px);
  }

  // This one styles the lower part of the slider track.
  .mdl-slider__background-lower {
    background: $range-color;
    flex: 0;
    position: relative;
    border: 0;
    padding: 0;
  }

  // This one styles the upper part of the slider track.
  .mdl-slider__background-upper {
    background: $range-bg-color;
    flex: 0;
    position: relative;
    border: 0;
    padding: 0;
    transition: left 0.18s $animation-curve-default
  }
