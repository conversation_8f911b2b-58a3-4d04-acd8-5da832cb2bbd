import { doc, getFirestore, updateDoc } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { COLLECTIONS } from './firebaseConfig';
import { toast } from 'sonner';

/**
 * Soft delete a document by marking it as deleted in Firestore
 * This avoids permissions issues by updating the document instead of deleting it
 * @param docId Document ID to delete
 * @param userId User ID of the document owner (for validation)
 * @returns Promise that resolves when the document is marked as deleted
 */
export const softDeleteDocument = async (docId: string, userId: string): Promise<boolean> => {
  try {
    // Reference to the document
    const docRef = doc(getFirestore(firebaseApp), COLLECTIONS.DOCUMENTS, docId);
    
    // Mark the document as deleted (but don't actually delete it)
    await updateDoc(docRef, {
      isDeleted: true,
      deletedAt: new Date(),
      // We don't need to add userId since it's already in the document
    });
    
    // Return success
    return true;
  } catch (err) {
    console.error('Error soft-deleting document:', err);
    toast.error('Failed to delete document: ' + (err instanceof Error ? err.message : 'Unknown error'));
    return false;
  }
};
