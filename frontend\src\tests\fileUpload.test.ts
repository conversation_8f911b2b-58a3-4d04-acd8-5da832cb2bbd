/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Tests für File-Upload-Funktionalität
 */

import { TestAssertions, TestRunner, MockUtils } from './test-utils';

// Interface für File-Upload
interface UploadedFile {
  name: string;
  extension: string;
  type?: string;
  size: number;
  content: string;
}

interface FileValidationResult {
  isValid: boolean;
  errorMessage?: string;
  detectedLanguage?: string;
  isSupported: boolean;
}

// Test-Implementierung der File-Upload-Logik
class TestFileUploader {
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly SUPPORTED_EXTENSIONS = [
    '.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs', '.r', '.sql', '.txt'
  ];

  // Simuliere File-Upload-Validierung
  validateFile(file: UploadedFile): FileValidationResult {
    // Größen-Validierung
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        errorMessage: `Dateigröße überschreitet das 5MB-Limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)`,
        isSupported: false
      };
    }

    // Dateiname-Validierung
    if (!file.name || file.name.trim().length === 0) {
      return {
        isValid: false,
        errorMessage: 'Dateiname ist erforderlich',
        isSupported: false
      };
    }

    // Extension-Validierung
    const extension = this.getFileExtension(file.name);
    const isSupported = this.SUPPORTED_EXTENSIONS.includes(extension);
    
    if (!isSupported) {
      return {
        isValid: true, // Datei ist technisch valid, aber nicht unterstützt
        errorMessage: `Nicht unterstützter Dateityp: ${extension}`,
        isSupported: false
      };
    }

    // Content-Validierung
    if (!file.content || file.content.trim().length === 0) {
      return {
        isValid: false,
        errorMessage: 'Datei-Inhalt ist leer',
        isSupported: true
      };
    }

    // Sprach-Erkennung
    const detectedLanguage = this.detectLanguageFromExtension(extension);

    return {
      isValid: true,
      isSupported: true,
      detectedLanguage
    };
  }

  // Simuliere Datei-Verarbeitung
  processFile(file: UploadedFile): { success: boolean; processedContent?: string; language?: string; error?: string } {
    const validation = this.validateFile(file);
    
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errorMessage
      };
    }

    if (!validation.isSupported) {
      return {
        success: false,
        error: `Dateityp ${this.getFileExtension(file.name)} wird nicht unterstützt`
      };
    }

    // Simuliere Content-Verarbeitung
    const processedContent = this.preprocessContent(file.content, validation.detectedLanguage);

    return {
      success: true,
      processedContent,
      language: validation.detectedLanguage
    };
  }

  // Simuliere Drag & Drop Validierung
  validateDroppedFiles(files: UploadedFile[]): { validFiles: UploadedFile[]; errors: string[] } {
    const validFiles: UploadedFile[] = [];
    const errors: string[] = [];

    if (files.length === 0) {
      errors.push('Keine Dateien zum Upload ausgewählt');
      return { validFiles, errors };
    }

    if (files.length > 1) {
      errors.push('Nur eine Datei kann gleichzeitig hochgeladen werden');
      return { validFiles, errors };
    }

    const file = files[0];
    const validation = this.validateFile(file);

    if (validation.isValid && validation.isSupported) {
      validFiles.push(file);
    } else {
      errors.push(validation.errorMessage || 'Unbekannter Validierungsfehler');
    }

    return { validFiles, errors };
  }

  private getFileExtension(filename: string): string {
    const parts = filename.split('.');
    return parts.length > 1 ? '.' + parts[parts.length - 1].toLowerCase() : '';
  }

  private detectLanguageFromExtension(extension: string): string {
    const extensionMap: Record<string, string> = {
      '.py': 'Python',
      '.js': 'JavaScript',
      '.ts': 'TypeScript',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.r': 'R',
      '.sql': 'SQL',
      '.txt': 'plaintext'
    };

    return extensionMap[extension] || 'Unknown';
  }

  private preprocessContent(content: string, language?: string): string {
    // Simuliere Content-Preprocessing
    let processed = content;

    // Entferne BOM (Byte Order Mark) falls vorhanden
    if (processed.charCodeAt(0) === 0xFEFF) {
      processed = processed.slice(1);
    }

    // Normalisiere Zeilenendings
    processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Entferne trailing whitespace
    processed = processed.split('\n').map(line => line.trimEnd()).join('\n');

    return processed;
  }
}

// Test-Suite für File-Upload
export function createFileUploadTests(): TestRunner {
  const runner = new TestRunner();

  runner.addTest('File-Upload: Gültige Python-Datei', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'test.py',
      extension: '.py',
      type: 'text/plain',
      size: 1024,
      content: 'print("Hello World")\nprint("Test")'
    };

    const validation = uploader.validateFile(file);
    
    TestAssertions.assertTrue(validation.isValid, 'Gültige Python-Datei sollte valid sein');
    TestAssertions.assertTrue(validation.isSupported, 'Python-Dateien sollten unterstützt werden');
    TestAssertions.assertEqual(validation.detectedLanguage, 'Python', 'Sprache sollte als Python erkannt werden');
  });

  runner.addTest('File-Upload: Datei zu groß', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'large.py',
      extension: '.py',
      type: 'text/plain',
      size: 6 * 1024 * 1024, // 6MB - über dem Limit
      content: 'print("test")'
    };

    const validation = uploader.validateFile(file);
    
    TestAssertions.assertFalse(validation.isValid, 'Zu große Datei sollte invalid sein');
    TestAssertions.assertFalse(validation.isSupported, 'Zu große Datei sollte nicht unterstützt werden');
    TestAssertions.assertTrue(
      validation.errorMessage!.includes('5MB-Limit'),
      'Fehlermeldung sollte Größenlimit erwähnen'
    );
  });

  runner.addTest('File-Upload: Nicht unterstützter Dateityp', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'image.png',
      extension: '.png',
      type: 'image/png',
      size: 1024,
      content: 'binary data'
    };

    const validation = uploader.validateFile(file);
    
    TestAssertions.assertTrue(validation.isValid, 'Datei sollte technisch valid sein');
    TestAssertions.assertFalse(validation.isSupported, 'PNG-Dateien sollten nicht unterstützt werden');
    TestAssertions.assertTrue(
      validation.errorMessage!.includes('Nicht unterstützter Dateityp'),
      'Fehlermeldung sollte nicht unterstützten Typ erwähnen'
    );
  });

  runner.addTest('File-Upload: Leere Datei', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'empty.py',
      extension: '.py',
      type: 'text/plain',
      size: 0,
      content: ''
    };

    const validation = uploader.validateFile(file);
    
    TestAssertions.assertFalse(validation.isValid, 'Leere Datei sollte invalid sein');
    TestAssertions.assertEqual(validation.errorMessage, 'Datei-Inhalt ist leer', 'Korrekte Fehlermeldung für leere Datei');
  });

  runner.addTest('File-Upload: Datei-Verarbeitung erfolgreich', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'script.js',
      extension: '.js',
      type: 'text/javascript',
      size: 512,
      content: 'console.log("Hello World");\nconst x = 5;'
    };

    const result = uploader.processFile(file);
    
    TestAssertions.assertTrue(result.success, 'Datei-Verarbeitung sollte erfolgreich sein');
    TestAssertions.assertNotNull(result.processedContent, 'Verarbeiteter Inhalt sollte vorhanden sein');
    TestAssertions.assertEqual(result.language, 'JavaScript', 'Sprache sollte korrekt erkannt werden');
  });

  runner.addTest('File-Upload: Datei-Verarbeitung fehlgeschlagen', () => {
    const uploader = new TestFileUploader();
    
    const file: UploadedFile = {
      name: 'invalid.xyz',
      extension: '.xyz',
      type: 'application/octet-stream',
      size: 1024,
      content: 'some content'
    };

    const result = uploader.processFile(file);
    
    TestAssertions.assertFalse(result.success, 'Verarbeitung sollte fehlschlagen');
    TestAssertions.assertNotNull(result.error, 'Fehlermeldung sollte vorhanden sein');
    TestAssertions.assertTrue(
      result.error!.includes('wird nicht unterstützt'),
      'Fehlermeldung sollte nicht unterstützten Typ erwähnen'
    );
  });

  runner.addTest('Drag & Drop: Einzelne gültige Datei', () => {
    const uploader = new TestFileUploader();
    
    const files: UploadedFile[] = [{
      name: 'test.py',
      extension: '.py',
      type: 'text/plain',
      size: 1024,
      content: 'print("Hello")'
    }];

    const result = uploader.validateDroppedFiles(files);
    
    TestAssertions.assertArrayLength(result.validFiles, 1, 'Eine gültige Datei sollte akzeptiert werden');
    TestAssertions.assertArrayLength(result.errors, 0, 'Keine Fehler sollten auftreten');
  });

  runner.addTest('Drag & Drop: Mehrere Dateien (nicht erlaubt)', () => {
    const uploader = new TestFileUploader();
    
    const files: UploadedFile[] = [
      {
        name: 'test1.py',
        extension: '.py',
        type: 'text/plain',
        size: 1024,
        content: 'print("Hello")'
      },
      {
        name: 'test2.js',
        extension: '.js',
        type: 'text/javascript',
        size: 512,
        content: 'console.log("Hello")'
      }
    ];

    const result = uploader.validateDroppedFiles(files);
    
    TestAssertions.assertArrayLength(result.validFiles, 0, 'Keine Dateien sollten akzeptiert werden');
    TestAssertions.assertArrayLength(result.errors, 1, 'Ein Fehler sollte auftreten');
    TestAssertions.assertTrue(
      result.errors[0].includes('Nur eine Datei'),
      'Fehlermeldung sollte Multiple-Files-Problem erwähnen'
    );
  });

  runner.addTest('Drag & Drop: Keine Dateien', () => {
    const uploader = new TestFileUploader();
    
    const files: UploadedFile[] = [];

    const result = uploader.validateDroppedFiles(files);
    
    TestAssertions.assertArrayLength(result.validFiles, 0, 'Keine Dateien sollten akzeptiert werden');
    TestAssertions.assertArrayLength(result.errors, 1, 'Ein Fehler sollte auftreten');
    TestAssertions.assertTrue(
      result.errors[0].includes('Keine Dateien'),
      'Fehlermeldung sollte fehlende Dateien erwähnen'
    );
  });

  runner.addTest('Content-Preprocessing: BOM und Zeilenendings', () => {
    const uploader = new TestFileUploader();
    
    // Simuliere Datei mit BOM und gemischten Zeilenendings
    const file: UploadedFile = {
      name: 'test.py',
      extension: '.py',
      type: 'text/plain',
      size: 1024,
      content: '\uFEFFprint("Hello")\r\nprint("World")\r'
    };

    const result = uploader.processFile(file);
    
    TestAssertions.assertTrue(result.success, 'Verarbeitung sollte erfolgreich sein');
    TestAssertions.assertFalse(
      result.processedContent!.includes('\uFEFF'),
      'BOM sollte entfernt werden'
    );
    TestAssertions.assertFalse(
      result.processedContent!.includes('\r'),
      'Carriage Returns sollten entfernt werden'
    );
    TestAssertions.assertTrue(
      result.processedContent!.includes('\n'),
      'Newlines sollten normalisiert werden'
    );
  });

  return runner;
}
