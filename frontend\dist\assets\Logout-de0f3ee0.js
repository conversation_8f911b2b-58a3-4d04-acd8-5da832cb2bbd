import{A as e,m as n,j as t}from"./vendor-8eb8bd34.js";import{e as s}from"./index-416a436c.js";import{C as i,a as r,b as c,d as l,c as d,B as m}from"./card-b935221e.js";function p(){return e(),n.useEffect(()=>{(async()=>{await s.signOut(),setTimeout(()=>{const a=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href=a.split("/logout")[0]+"/"},2e3)})()},[]),t.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 to-secondary/20 backdrop-blur-sm flex items-center justify-center p-4",children:t.jsxs(i,{className:"w-full max-w-md border-border/40 bg-card/80 backdrop-blur-sm",children:[t.jsxs(r,{className:"text-center",children:[t.jsx(c,{className:"text-2xl font-mono tracking-wide",children:"Signing Out"}),t.jsx(l,{children:"You have been logged out of your account"})]}),t.jsxs(d,{className:"text-center",children:[t.jsx("p",{className:"mb-4",children:"Redirecting you to the home page..."}),t.jsx(m,{variant:"outline",className:"font-mono tracking-wide",onClick:()=>{const o=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href=o.split("/logout")[0]+"/"},children:"Return to home now"})]})]})})}export{p as default};
