from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union, Literal

# Create empty router for compatibility
router = APIRouter()

class Parameter(BaseModel):
    """Model for a script parameter"""
    name: str
    type: str = "Unknown"
    description: str = ""
    required: bool = True
    default_value: Optional[str] = None
    restrictions: Optional[str] = None
    is_hardcoded: bool = False
    line_number: Optional[int] = None

class Function(BaseModel):
    """Model for a function or method in the script"""
    name: str
    description: str = ""
    parameters: List[Parameter] = []
    return_type: str = "Unknown"
    return_description: str = ""
    line_number: int
    end_line_number: int
    is_gis_related: bool = False

class Library(BaseModel):
    """Model for an imported library or module"""
    name: str
    alias: Optional[str] = None
    is_gis_library: bool = False
    imported_items: List[str] = []

class CodeAnalysisRequest(BaseModel):
    """Request model for code analysis"""
    code: str
    language: str = "Python"

class Workflow(BaseModel):
    """Model for a workflow step extracted from the code"""
    step_number: int
    description: str
    input_data: List[str] = []  # References to input parameters
    output_data: List[str] = []  # References to output parameters
    processing: str = ""  # Description of the processing step
    function_calls: List[str] = []  # Function calls in this step

class CodeAnalysisResponse(BaseModel):
    """Response model for code analysis"""
    script_description: str = ""
    input_parameters: List[Parameter] = []
    output_parameters: List[Parameter] = []
    functions: List[Function] = []
    libraries: List[Library] = []
    workflow_steps: List[Workflow] = []  # Added workflow steps
    hardcoded_parameters: List[Parameter] = []  # Specifically track hardcoded parameters
    is_gis_code: bool = False
    is_valid: bool = True
    error_message: str = ""
    detected_language: str = "Python"
    supported_languages: List[str] = ["Python"]
    work_in_progress: Dict[str, str] = {
        "SQL": "Support for SQL code analysis coming soon",
        "R": "Support for R code analysis coming soon", 
        "PHP": "Support for PHP code analysis coming soon"
    }
    llm_provider_used: Optional[str] = None  # Which LLM was used for analysis, if any

class LLMAnalysisRequest(BaseModel):
    """Request model for LLM-based code analysis"""
    code: str
    language: str = "Python"
    provider: str = "auto"  # 'openai', 'deepseek', or 'auto'
    analysis_type: str = "general"  # Type of analysis to perform

class LLMAnalysisResponse(BaseModel):
    """Response model for LLM-based code analysis"""
    is_valid: bool = True
    detected_language: str = "Python"
    error_message: str = ""
    provider_used: Optional[str] = None
    analysis_result: Optional[Dict[str, Any]] = None
    analysis_type: str = "general"
    available_providers: List[str] = []
    
# Import documentation models directly from the documentation_models API
from app.apis.documentation_models import (
    DocumentationRequest,
    DocumentationWorkflowStep,
    DocumentationWorkflow,
    DocumentationParameter,
    DocumentationResponse,
    DocumentationGenerationResponse,
    DocumentationSection,
    DocumentationData,
    DocumentationFunction
)
