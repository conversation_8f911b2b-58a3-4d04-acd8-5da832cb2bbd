import brain from 'brain';
import { 
  DocumentationData, 
  Parameter,
  DocumentationResponse as BackendDocumentationResponse,
  ParameterTableItem, 
  FunctionTableItem, 
  WorkflowStep 
} from './documentationTypes';


/**
 * Frontend documentation response format
 */
export interface DocumentationResult {
  is_valid: boolean;
  error_message?: string;
  title: string;
  documentation: string;
  documentation_data?: DocumentationData;
  provider_used?: string | null;
  language_detected: string;
  format: string;
}

/**
 * Map backend document data to frontend DocumentationPreview compatible format
 */
/**
 * Maps documentation data to the format expected by DocumentationPreview component.
 * Handles both legacy format and new API format.
 * 
 * @param docData Documentation data from the backend
 * @returns Formatted documentation compatible with DocumentationPreview
 */
export function mapDocumentationToPreviewFormat(docData: DocumentationData | null) {
  if (!docData) return null;
  
  // If we're working with the new format from v2 API
  if ('textualDescription' in docData) {
    return docData;
  }
  
  // Legacy format mapping
  // Map parameters to the format expected by DocumentationPreview
  const allParameters = [
    ...(docData.parameters_input || []),
    ...(docData.parameters_output || []),
    ...(docData.parameters_hardcoded || [])
  ].map(param => ({
    name: param.name,
    type: param.type,
    description: param.description,
    required: param.required,
    constraints: param.restrictions || param.constraints
  }));

  // Map workflow steps to the format expected by DocumentationPreview
  const workflowSteps = (docData.workflow_steps || []).map(step => ({
    id: step.step_number,
    name: String(step.step_number) + (step.description ? ': ' + step.description.split('.')[0] : ''),
    description: step.description + 
      (step.processing ? `\n\nProcessing: ${step.processing}` : '') +
      (step.input_data && step.input_data.length > 0 ? `\n\nInput: ${step.input_data.join(', ')}` : '') +
      (step.output_data && step.output_data.length > 0 ? `\n\nOutput: ${step.output_data.join(', ')}` : '')
  }));

  // Map functions to simple tool names for the DocumentationPreview
  const toolsUsed = (docData.functions || []).map(func => func.name);

  return {
    textualDescription: docData.description || '',
    parameters: allParameters,
    toolsUsed: toolsUsed,
    workflow: {
      steps: workflowSteps
    },
    originalCode: docData.original_code
  };
}

/**
 * Extract formatting information from documentation response
 */
export function extractFormattingInfo(response: DocumentationResult) {
  return {
    title: response.title,
    isValid: response.is_valid,
    errorMessage: response.error_message || '',
    providerUsed: response.provider_used || 'none',
    languageDetected: response.language_detected,
    format: response.format
  };
}

/**
 * Generate documentation for provided code
 * @param code The source code to analyze
 * @param title Optional title for the documentation
 * @param language Programming language of the code
 * @param provider LLM provider to use: 'openai', 'deepseek', or 'auto'
 * @param format Output format
 * @returns Documentation response object
 */
/**
 * Generate documentation for provided code using the backend API
 * 
 * @param code The source code to analyze
 * @param title Optional title for the documentation
 * @param language Programming language of the code
 * @param provider LLM provider to use: 'openai', 'deepseek', or 'auto'
 * @param format Output format
 * @returns Documentation response object
 */
export async function generateDocumentation(
  code: string, 
  title?: string, 
  language: string = 'Python', 
  provider: string = 'auto', 
  format: string = 'markdown',
  documentationLanguage: string = 'en'
): Promise<DocumentationResult> {
  try {
    // Provide status message to the console for debugging
    console.log(`Generating documentation using ${provider} provider for ${language} code`);
    
    // Use the Brain client to call the documentation generation endpoint
    const response = await brain.generate_documentation({
      code,
      language,
      provider,
      format,
      documentationLanguage
    });
    
    const result = await response.json();
    
    // Map the response data to expected format
    return {
      is_valid: result.success,
      error_message: result.error_message,
      title: title || 'Documentation',
      documentation: '',  // This field is deprecated, we use the structured data instead
      documentation_data: result.documentation,
      provider_used: result.provider_used,
      language_detected: language,
      format: format
    };
  } catch (error) {
    console.error('Error generating documentation:', error);
    throw error;
  }
}

/**
 * Generate documentation with multiple LLM providers and compare results
 * This function will generate documentation using both OpenAI and DeepSeek
 * and return both results for comparison
 * 
 * @param code The source code to analyze
 * @param title Optional title for the documentation
 * @param language Programming language of the code
 * @returns Object containing results from both providers and analysis
 */
/**
 * Generate documentation with multiple LLM providers and compare results
 * This function will generate documentation using both OpenAI and DeepSeek
 * and return both results for comparison
 * 
 * @param code The source code to analyze
 * @param title Optional title for the documentation
 * @param language Programming language of the code
 * @returns Object containing results from both providers and analysis
 */
export async function generateComparativeDocumentation(
  code: string, 
  title?: string, 
  language: string = 'Python',
  documentationLanguage: string = 'en'
) {
  try {
    console.log('Starting comparative documentation generation with multiple providers');
    
    // Generate documentation with different providers
    const openaiResponse = await generateDocumentation(code, title, language, 'openai', 'markdown', documentationLanguage);
    console.log('OpenAI documentation generated successfully');
    
    const deepseekResponse = await generateDocumentation(code, title, language, 'deepseek', 'markdown', documentationLanguage);
    console.log('DeepSeek documentation generated successfully');
    
    // Return both results for comparison
    return {
      openai: openaiResponse,
      deepseek: deepseekResponse,
      // Add metadata about which provider performed better at specific tasks
      analysis: {
        descriptionQuality: compareDescriptionQuality(openaiResponse, deepseekResponse),
        parameterExtraction: compareParameterExtraction(openaiResponse, deepseekResponse),
        functionAnalysis: compareFunctionAnalysis(openaiResponse, deepseekResponse)
      }
    };
  } catch (error) {
    console.error('Error generating comparative documentation:', error);
    throw error;
  }
}

// Helper functions for comparative analysis
function compareDescriptionQuality(openai: DocumentationResult, deepseek: DocumentationResult) {
  // A very simple comparison based on description length
  // In a real implementation, this would use more sophisticated metrics
  const openaiDesc = openai.documentation_data?.description || '';
  const deepseekDesc = deepseek.documentation_data?.description || '';
  
  if (openaiDesc.length > deepseekDesc.length * 1.5) {
    return 'openai';
  } else if (deepseekDesc.length > openaiDesc.length * 1.5) {
    return 'deepseek';
  } else {
    return 'similar';
  }
}

function compareParameterExtraction(openai: DocumentationResult, deepseek: DocumentationResult) {
  const openaiParams = [
    ...(openai.documentation_data?.parameters_input || []),
    ...(openai.documentation_data?.parameters_output || []),
    ...(openai.documentation_data?.parameters_hardcoded || [])
  ];
  
  const deepseekParams = [
    ...(deepseek.documentation_data?.parameters_input || []),
    ...(deepseek.documentation_data?.parameters_output || []),
    ...(deepseek.documentation_data?.parameters_hardcoded || [])
  ];
  
  if (openaiParams.length > deepseekParams.length) {
    return 'openai';
  } else if (deepseekParams.length > openaiParams.length) {
    return 'deepseek';
  } else {
    return 'similar';
  }
}

function compareFunctionAnalysis(openai: DocumentationResult, deepseek: DocumentationResult) {
  const openaiFunc = openai.documentation_data?.functions || [];
  const deepseekFunc = deepseek.documentation_data?.functions || [];
  
  if (openaiFunc.length > deepseekFunc.length) {
    return 'openai';
  } else if (deepseekFunc.length > openaiFunc.length) {
    return 'deepseek';
  } else {
    // If same number of functions, compare the descriptions
    const openaiDescriptions = openaiFunc.reduce((acc, func) => acc + (func.description?.length || 0), 0);
    const deepseekDescriptions = deepseekFunc.reduce((acc, func) => acc + (func.description?.length || 0), 0);
    
    if (openaiDescriptions > deepseekDescriptions * 1.3) {
      return 'openai';
    } else if (deepseekDescriptions > openaiDescriptions * 1.3) {
      return 'deepseek';
    } else {
      return 'similar';
    }
  }
}
