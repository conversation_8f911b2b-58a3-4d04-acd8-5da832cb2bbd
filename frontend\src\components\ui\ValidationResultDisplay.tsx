import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ir<PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import type { SyntaxErrorDetail } from "types"; // Assuming brain.d.ts or a global types file

interface Props {
  fileName: string | null;
  isValid: boolean | null;
  errors: SyntaxErrorDetail[] | null;
  isSpecificGIS: boolean | null;
  isLoading: boolean;
  // onJumpToError?: (line: number, column: number) => void; // Removed for now
}

export const ValidationResultDisplay: React.FC<Props> = ({
  fileName,
  isValid,
  errors,
  isSpecificGIS,
  isLoading,
  // onJumpToError, // Removed for now
}) => {
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Validiere Code...</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Bitte warten, der Code wird analysiert.</p>
        </CardContent>
      </Card>
    );
  }

  if (fileName === null || isValid === null) {
    return null; // Don't display anything if no file or validation result yet
  }

  const hasSyntaxErrors = errors && errors.filter(e => e.severity === 'error').length > 0;

  return (
    <Card className="w-full mt-4">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          {hasSyntaxErrors ? (
            <AlertTriangle className="text-red-500 mr-2" />
          ) : (
            <CheckCircle className="text-green-500 mr-2" />
          )}
          Validierungsergebnis für: {fileName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isSpecificGIS === true && (
          <p className="text-sm text-blue-600 mb-2">
            Spezifischer GIS-Code erkannt (z.B. arcpy, pyqgis). Die grundlegende Syntax scheint valide zu sein.
          </p>
        )}
        {isSpecificGIS === false && (
          <p className="text-sm text-orange-600 mb-2">
            Kein spezifischer GIS-Code (arcpy, pyqgis) erkannt. Die Validierung konzentriert sich auf allgemeine Syntax.
          </p>
        )}

        {hasSyntaxErrors ? (
          <>
            <p className="text-red-500 mb-3">
              Es {errors.filter(e => e.severity === 'error').length === 1 ? "wurde 1 Syntaxfehler" : `wurden ${errors.filter(e => e.severity === 'error').length} Syntaxfehler`} gefunden:
            </p>
            <ScrollArea className="h-[150px] w-full rounded-md border p-4">
              {errors.map((error, index) => (
                error.severity === 'error' && (
                  <div key={index} className="mb-2 pb-2 border-b last:border-b-0 last:mb-0">
                    <p className="text-sm font-semibold text-red-700">Syntaxfehler #{index + 1}</p>
                    <p className="text-xs text-gray-700 whitespace-pre-wrap">
                      {error.message} 
                    </p>
                    {/* Jump to error location button removed as per new requirements */}
                    {/* 
                    {onJumpToError && error.line && error.column && (
                      <Button 
                        variant="link"
                        size="sm"
                        className="p-0 h-auto text-xs mt-1"
                        onClick={() => onJumpToError(error.line, error.column)}
                      >
                        Gehe zur Fehlerstelle
                      </Button>
                    )}
                    */}
                  </div>
                )
              ))}
            </ScrollArea>
          </>
        ) : (
          isValid && (
            <p className="text-green-600">
              Der Code scheint syntaktisch korrekt zu sein.
            </p>
          )
        )}
        {!isValid && !hasSyntaxErrors && (
            <p className="text-orange-500">
                Die Validierung war nicht erfolgreich, aber es wurden keine spezifischen Syntaxfehler vom LLM gemeldet.
            </p>
        )}
      </CardContent>
    </Card>
  );
};

