import json
import re
import openai # Keep openai for DeepSeek compatibility
from fastapi import APIRouter
from pydantic import BaseModel
from typing import Literal

import databutton as db

router = APIRouter(tags=["DEPRECATED - Use /code/llm-validate instead"])

class SyntaxErrorDetail(BaseModel):
    line: int
    column: int
    message: str
    severity: Literal["error", "warning", "info", "hint"] = "error"

class LlmValidateCodeSyntaxRequest(BaseModel):
    code: str
    language: str | None = "python"

class LlmValidateCodeSyntaxResponse(BaseModel):
    isValid: bool
    errors: list[SyntaxErrorDetail] = []
    detected_language: str | None = None
    raw_llm_response: str | None = None # For debugging

# DEPRECATED: Endpunkt wurde zu /code/llm-validate migriert - Funktion bleibt für Import verfügbar
async def validate_code_with_llm(code: str, language: str = "python"):
    language = request.language
    if not language:
        if "def " in request.code and ":" in request.code:
            language = "python"
        elif "SELECT " in request.code.upper() and "FROM " in request.code.upper():
            language = "sql"
        else:
            language = "plaintext"

    normalized_code = request.code.replace('\r\n', '\n').replace('\r', '\n')
    stripped_code_for_llm = normalized_code.strip()

    if not stripped_code_for_llm:
        return LlmValidateCodeSyntaxResponse(isValid=True, errors=[], detected_language=language)
    
    # Calculate the leading offset: number of lines before the first non-blank line in the original normalized code
    leading_offset = 0
    lines_in_normalized_code = normalized_code.split('\n')
    for i, line_content in enumerate(lines_in_normalized_code):
        if line_content.strip(): # Found the first line with actual content
            leading_offset = i
            break
    else: # All lines were blank or the code was empty (already handled, but good for completeness)
        if not normalized_code.strip(): # double check if all was blank
            leading_offset = 0
        # if there was content but it got stripped to nothing (e.g. just spaces), leading_offset would be count of lines
        elif not stripped_code_for_llm and normalized_code:
            leading_offset = len(lines_in_normalized_code)
            
    # Calculate actual lines based on the code that will be sent to the LLM (after stripping)
    actual_lines_in_stripped_code = len(stripped_code_for_llm.split('\n'))

    # Use DeepSeek API Key
    deepseek_api_key = db.secrets.get("Deepseek_API_KEY") # Corrected key name
    if not deepseek_api_key:
        print("ERROR: Deepseek_API_KEY secret not found.")
        return LlmValidateCodeSyntaxResponse(
            isValid=False,
            errors=[SyntaxErrorDetail(line=1, column=1, message="LLM configuration error: Deepseek API key missing.", severity="error")],
            detected_language=language,
            raw_llm_response="Deepseek API Key not found"
        )

    try:
        client = openai.OpenAI(
            api_key=deepseek_api_key,
            base_url="https://api.deepseek.com/v1" # DeepSeek API base URL
        )
        llm_model_name = "deepseek-chat" # Or other suitable DeepSeek model
    except Exception as e:
        print(f"Error configuring DeepSeek client: {e}")
        return LlmValidateCodeSyntaxResponse(
            isValid=False,
            errors=[SyntaxErrorDetail(line=1, column=1, message=f"LLM (DeepSeek) client config error: {e}", severity="error")],
            detected_language=language,
            raw_llm_response=f"DeepSeek client config error: {e}"
        )

    system_prompt = (
        "You are an expert multi-language code syntax validator. Your primary task is to identify ONLY fundamental syntax errors in the provided code snippet, regardless of the programming language. "
        "Pay EXTREMELY close attention to matching pairs of all types of brackets, including parentheses (), square brackets [], and curly braces {}. Unbalanced or mismatched brackets are critical syntax errors. "
        "You MUST ONLY return a SINGLE JSON object (NO other text, NO preamble, NO explanation, just the JSON object). "
        "This JSON object MUST have two top-level keys: `isValid` (boolean) and `errors` (an array of error objects). "
        "If `isValid` is true, `errors` MUST be an empty array. "
        "If `isValid` is false, `errors` MUST contain at least one error object. "
        "Each error object in the `errors` array MUST strictly contain `line` (integer, 1-indexed), `column` (integer, 1-indexed), `message` (string detailing the specific syntax error, e.g., \"Unmatched closing parenthesis ')'\" or \"Missing opening curly brace '{' for block\"), and `severity` (string, always \"error\"). "
        "Focus on pinpoint accuracy for `line` and `column` numbers. The first line of actual code content (after any initial blank lines have been conceptually removed) is line 1. Each newline character (\\n) signifies a new line. "
        "Do NOT report style issues, best practice violations, potential runtime errors, or logical errors. Only report pure syntax errors that would prevent a parser or interpreter from understanding the code structure. "
        "Be vigilant for common errors like missing semicolons (where appropriate for the language), incorrect use of operators, or malformed statements."
    )

    user_prompt = (
        f"""Please analyze the following {language} code for fundamental syntax errors.

Code to analyze:
```{language}
{stripped_code_for_llm}
```

Your response MUST be a single JSON object conforming to the schema previously described.
Example for SQL missing semicolon:
Input SQL:
```sql
CREATE TABLE Test (\n    ID INT\n)\n```
Expected JSON Output:
```json
{{
  "isValid": false,
  "errors": [
    {{
      "line": 3,
      "column": 2,
      "message": "Missing semicolon at the end of the CREATE TABLE statement.",
      "severity": "error"
    }}
  ]
}}
```
Example for a valid SQL snippet:
Input SQL:
```sql
SELECT * FROM Users WHERE Age > 30;\n```
Expected JSON Output:
```json
{{
  "isValid": true,
  "errors": []
}}
```
"""
    )

    raw_llm_response_content = ""
    try:
        print(f"Code sent to DeepSeek for validation (stripped):\n>>>\n{stripped_code_for_llm}\n<<<" )
        print(f"Sending to DeepSeek LLM ({llm_model_name}) for language: {language}")
        
        completion = client.chat.completions.create(
            model=llm_model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.0, # For deterministic output
            response_format={ "type": "json_object" } # Request JSON output if supported, else parse from text
        )
        
        if completion.choices and completion.choices[0].message and completion.choices[0].message.content:
            raw_llm_response_content = completion.choices[0].message.content
        else:
            raw_llm_response_content = json.dumps({"isValid": False, "errors": [{"line":1, "column":1, "message":"LLM response structure not as expected or empty.", "severity":"error"}]})
            print(f"LLM (DeepSeek) did not return content in expected part. Full response object: {completion}")

        print(f"LLM (DeepSeek - {llm_model_name}) raw response: {raw_llm_response_content}")

        # ... (rest of the parsing and validation logic - should be mostly compatible)
        try:
            parsed_response_data = json.loads(raw_llm_response_content)
            if not isinstance(parsed_response_data, dict):
                 raise ValueError("LLM response is not a JSON object")
        except json.JSONDecodeError as e:
            print(f"LLM response was not valid JSON: {e}")
            match = re.search(r'```json\s*(\{.*?\})\s*```', raw_llm_response_content, re.DOTALL)
            if not match:
                 match = re.search(r'(\{.*?\})', raw_llm_response_content, re.DOTALL)

            if match:
                json_block = match.group(1)
                try:
                    parsed_response_data = json.loads(json_block)
                    if not isinstance(parsed_response_data, dict):
                        raise ValueError("LLM response (extracted block) is not a JSON object")
                    print("Successfully parsed JSON block extracted from LLM response.")
                except json.JSONDecodeError as e_inner:
                    print(f"Could not parse extracted JSON block: {e_inner}")
                    return LlmValidateCodeSyntaxResponse(
                        isValid=False,
                        errors=[SyntaxErrorDetail(line=1, column=1, message=f"LLM response parsing error after extraction. Raw: {raw_llm_response_content[:200]}...", severity="error")],
                        detected_language=language,
                        raw_llm_response=raw_llm_response_content
                    )
            else:
                print("No JSON block found in LLM response.")
                return LlmValidateCodeSyntaxResponse(
                    isValid=False,
                    errors=[SyntaxErrorDetail(line=1, column=1, message=f"LLM did not return a recognizable JSON object. Raw: {raw_llm_response_content[:200]}...", severity="error")],
                    detected_language=language,
                    raw_llm_response=raw_llm_response_content
                )

        if "isValid" not in parsed_response_data or not isinstance(parsed_response_data["isValid"], bool):
            raise ValueError("'isValid' key missing or not a boolean in LLM JSON response.")
        
        llm_is_valid = parsed_response_data.get("isValid", False)
        llm_errors_raw = parsed_response_data.get("errors", [])

        if not isinstance(llm_errors_raw, list):
            raise ValueError("'errors' key is not a list in LLM JSON response.")

        if llm_is_valid and len(llm_errors_raw) > 0:
            print("LLM Warning: isValid is true, but errors array is not empty. Overriding isValid to false.")
            llm_is_valid = False
        
        if not llm_is_valid and not llm_errors_raw:
            print("LLM Warning: isValid is false, but errors array is empty. Adding a generic error.")
            llm_errors_raw.append({"line": 1, "column": 1, "message": "LLM indicated an unspecified syntax error."})

        validated_errors: list[SyntaxErrorDetail] = []
        for err in llm_errors_raw:
            if not isinstance(err, dict) or \
               not all(k in err for k in ["line", "column", "message"]) or \
               not isinstance(err["line"], int) or \
               not isinstance(err["column"], int) or \
               not isinstance(err["message"], str):
                print(f"LLM Warning: Invalid error object structure from LLM: {err}. Skipping this error.")
                continue

            original_llm_line = err["line"] # Line number from LLM relative to stripped code

            # Step 1: Clamp the LLM's line number to the bounds of the stripped code
            llm_relative_clamped_line = max(1, min(original_llm_line, actual_lines_in_stripped_code))
            
            # Step 2: Adjust this clamped line number by the leading_offset to match editor lines
            final_adjusted_line_for_editor = llm_relative_clamped_line + leading_offset
            
            error_message = err["message"]
            # Optionally, report if LLM's original line was out of stripped code bounds
            if original_llm_line != llm_relative_clamped_line:
                prefix = f"[LLM line {original_llm_line} (for stripped code) clamped to {llm_relative_clamped_line}] "
                error_message = prefix + error_message
                print(f"LLM Warning: Clamped LLM line from {original_llm_line} to {llm_relative_clamped_line} (bounds of stripped code) for error: {err['message']}")

            validated_errors.append(SyntaxErrorDetail(
                line=final_adjusted_line_for_editor,
                column=max(1, err["column"]),
                message=error_message,
                severity=err.get("severity", "error")
            ))
        
        if llm_is_valid:
            validated_errors = [] # Ensure errors are empty if LLM says valid

        return LlmValidateCodeSyntaxResponse(
            isValid=llm_is_valid,
            errors=validated_errors,
            detected_language=language,
            raw_llm_response=raw_llm_response_content
        )

    except openai.APIConnectionError as e:
        print(f"DeepSeek API connection error: {e}")
        errors = [SyntaxErrorDetail(line=1, column=1, message=f"LLM (DeepSeek) API connection error: {e}", severity="error")]
    except openai.RateLimitError as e:
        print(f"DeepSeek API rate limit exceeded: {e}")
        errors = [SyntaxErrorDetail(line=1, column=1, message="LLM (DeepSeek) rate limit exceeded. Please try again later.", severity="error")]
    except openai.APIStatusError as e:
        print(f"DeepSeek API status error: {e}")
        errors = [SyntaxErrorDetail(line=1, column=1, message=f"LLM (DeepSeek) API error: Status {e.status_code} - {e.response}", severity="error")]
    except Exception as e:
        print(f"Error during DeepSeek LLM validation: {e.__class__.__name__} - {e}")
        error_message = f"LLM (DeepSeek) error or unexpected issue: {e.__class__.__name__} - {str(e)[:100]}..."
        errors = [SyntaxErrorDetail(line=1, column=1, message=error_message, severity="error")]

    return LlmValidateCodeSyntaxResponse(
        isValid=False,
        errors=errors,
        detected_language=language,
        raw_llm_response=raw_llm_response_content # Ensure raw_llm_response_content is defined in error paths too
    )
