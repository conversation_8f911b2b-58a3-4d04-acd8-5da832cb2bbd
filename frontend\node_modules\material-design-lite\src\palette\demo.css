/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.demo-page--palette.demo-preview-block {
  display: inline-block;
}

.demo-palette {
  margin-top: 15px;
  width: 360px;
  float: left;
}

@media screen and (max-width: 360px ) {
  .demo-palette {
    width: 272px;
  }
}

.demo-palette-color, .demo-palette-heading {
  max-width: 300px;
  padding: 15px;
  color: white;
}

.demo-palette .demo-palette--dark-text,
.demo-palette-heading.demo-palette--dark-text .demo-palette-name {
  color: #000;
}

.demo-palette-name {
  padding: 0 0 60px 0;
}

.demo-palette-single {
  padding: 15px 15px 25px;
}

.demo-palette-single .demo-palette-name {
  padding: 0;
  float: left;
}
