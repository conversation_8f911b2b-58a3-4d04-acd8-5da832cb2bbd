import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface DeleteDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export function DeleteDocumentDialog({ open, onOpenChange, onConfirm }: DeleteDocumentDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md border-border/40 bg-card/80 backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="font-mono">Dokument löschen</DialogTitle>
          <DialogDescription>
            Sind Sie sicher, dass Sie dieses Dokument löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="secondary" onClick={() => onOpenChange(false)}>Abbrechen</Button>
          <Button variant="destructive" onClick={() => {
            onConfirm();
            onOpenChange(false);
          }}>Löschen</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
