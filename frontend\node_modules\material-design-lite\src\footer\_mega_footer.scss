/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import "../variables";
@import "../mixins";

.mdl-mega-footer {
  padding: $footer-min-padding $footer-padding-sides;

  color: $footer-color;
  background-color: $footer-bg-color;
}


.mdl-mega-footer--top-section:after,
.mdl-mega-footer--middle-section:after,
.mdl-mega-footer--bottom-section:after,
.mdl-mega-footer__top-section:after,
.mdl-mega-footer__middle-section:after,
.mdl-mega-footer__bottom-section:after {
  content: '';
  display: block;
  clear: both;
}

.mdl-mega-footer--left-section,
.mdl-mega-footer__left-section {
  margin-bottom: $footer-min-padding;
}

.mdl-mega-footer--right-section,
.mdl-mega-footer__right-section {
  margin-bottom: $footer-min-padding;
}

.mdl-mega-footer--right-section a,
.mdl-mega-footer__right-section a {
  display: block;

  margin-bottom: $footer-min-padding;

  color: inherit;
  text-decoration: none;
}

@media screen and (min-width: 760px) {
  .mdl-mega-footer--left-section,
  .mdl-mega-footer__left-section {
    float: left;
  }

  .mdl-mega-footer--right-section,
  .mdl-mega-footer__right-section {
    float: right;
  }

  .mdl-mega-footer--right-section a,
  .mdl-mega-footer__right-section a {
    display: inline-block;

    margin-left: $footer-min-padding;

    line-height: $footer-btn-size;
    vertical-align: middle;
  }
}

.mdl-mega-footer--social-btn,
.mdl-mega-footer__social-btn {
  width: $footer-btn-size;
  height: $footer-btn-size;

  padding: 0;
  margin: 0;

  background-color: $footer-button-fill-color;

  border: none;
}

.mdl-mega-footer--drop-down-section,
.mdl-mega-footer__drop-down-section {
  display: block;

  position: relative;
}

@media screen and (min-width: 760px) {
  .mdl-mega-footer--drop-down-section,
  .mdl-mega-footer__drop-down-section {
    width: 33%;
  }

  .mdl-mega-footer--drop-down-section:nth-child(1),
  .mdl-mega-footer--drop-down-section:nth-child(2),
  .mdl-mega-footer__drop-down-section:nth-child(1),
  .mdl-mega-footer__drop-down-section:nth-child(2) {
    float: left;
  }

  .mdl-mega-footer--drop-down-section:nth-child(3),
  .mdl-mega-footer__drop-down-section:nth-child(3) {
    float: right;

    &:after {
      clear: right;
    }
  }

  .mdl-mega-footer--drop-down-section:nth-child(4),
  .mdl-mega-footer__drop-down-section:nth-child(4) {
    clear: right;
    float: right;
  }

  .mdl-mega-footer--middle-section:after,
  .mdl-mega-footer__middle-section:after {
    content: '';

    display: block;

    clear: both;
  }

  .mdl-mega-footer--bottom-section,
  .mdl-mega-footer__bottom-section {
    padding-top: 0;
  }
}

@media screen and (min-width: 1024px) {
  .mdl-mega-footer--drop-down-section,
  .mdl-mega-footer--drop-down-section:nth-child(3),
  .mdl-mega-footer--drop-down-section:nth-child(4),
  .mdl-mega-footer__drop-down-section,
  .mdl-mega-footer__drop-down-section:nth-child(3),
  .mdl-mega-footer__drop-down-section:nth-child(4) {
    width: 24%;

    float: left;
  }
}

.mdl-mega-footer--heading-checkbox,
.mdl-mega-footer__heading-checkbox {
  position: absolute;
  width: 100%;
  height: $footer-heading-line-height + ($footer-min-padding * 2);

  padding: ($footer-min-padding * 2);
  margin: 0;
  margin-top: -$footer-min-padding;

  cursor: pointer;

  z-index: 1;
  opacity: 0;

  & + .mdl-mega-footer--heading:after,
  & + .mdl-mega-footer__heading:after {
    font-family: 'Material Icons';
    content: '\E5CE'
  }
}

.mdl-mega-footer--heading-checkbox:checked,
.mdl-mega-footer__heading-checkbox:checked {
  // WebViews in iOS 9 break the "~" operator, and WebViews in OS X 10.10
  // break consecutive "+" operators in some cases. Therefore, we need to use
  // both here to cover all the bases.
  & ~ .mdl-mega-footer--link-list,
  & ~ .mdl-mega-footer__link-list,
  & + .mdl-mega-footer--heading + .mdl-mega-footer--link-list,
  & + .mdl-mega-footer__heading + .mdl-mega-footer__link-list {
    display: none;
  }

  & + .mdl-mega-footer--heading:after,
  & + .mdl-mega-footer__heading:after {
    font-family: 'Material Icons';
    content: '\E5CF'
  }
}

.mdl-mega-footer--heading,
.mdl-mega-footer__heading {
  position: relative;
  width: 100%;

  padding-right: $footer-heading-line-height + $footer-min-padding;
  margin-bottom: $footer-min-padding;

  box-sizing:border-box;

  font-size: $footer-heading-font-size;
  line-height: $footer-heading-line-height;

  font-weight: 500;

  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  color: $footer-heading-color;
}

.mdl-mega-footer--heading:after,
.mdl-mega-footer__heading:after {
  content: '';

  position: absolute;
  top: 0;
  right: 0;

  display: block;

  width: $footer-heading-line-height;
  height: $footer-heading-line-height;

  background-size: cover;
}

.mdl-mega-footer--link-list,
.mdl-mega-footer__link-list {
  list-style: none;

  margin: 0;
  padding: 0;

  margin-bottom: $footer-min-padding * 2;
  &:after {
    clear: both;
    display: block;
    content: '';
  }
}

.mdl-mega-footer--link-list li,
.mdl-mega-footer__link-list li {
  @include typo-body-1();
  line-height: 20px;
}

.mdl-mega-footer--link-list a,
.mdl-mega-footer__link-list a {
  color: inherit;
  text-decoration: none;
  white-space: nowrap;
}

@media screen and (min-width: 760px) {
  .mdl-mega-footer--heading-checkbox,
  .mdl-mega-footer__heading-checkbox {
    display: none;

    & + .mdl-mega-footer--heading:after,
    & + .mdl-mega-footer__heading:after {
      content: '';
    }
  }
  .mdl-mega-footer--heading-checkbox:checked,
  .mdl-mega-footer__heading-checkbox:checked {
    // WebViews in iOS 9 break the "~" operator, and WebViews in OS X 10.10
    // break consecutive "+" operators in some cases. Therefore, we need to use
    // both here to cover all the bases.
    & ~ .mdl-mega-footer--link-list,
    & ~ .mdl-mega-footer__link-list,
    & + .mdl-mega-footer__heading + .mdl-mega-footer__link-list,
    & + .mdl-mega-footer--heading + .mdl-mega-footer--link-list {
      display: block;
    }

    & + .mdl-mega-footer--heading:after,
    & + .mdl-mega-footer__heading:after {
      content: '';
    }
  }
}

.mdl-mega-footer--bottom-section,
.mdl-mega-footer__bottom-section {
  padding-top: $footer-min-padding;
  margin-bottom: $footer-min-padding;
}

.mdl-logo {
  margin-bottom: $footer-min-padding;
  color: white;
}

.mdl-mega-footer--bottom-section .mdl-mega-footer--link-list li,
.mdl-mega-footer__bottom-section .mdl-mega-footer__link-list li {
  float: left;

  margin-bottom: 0;
  margin-right: $footer-min-padding;
}



@media screen and (min-width: 760px) {
  .mdl-logo {
    float: left;

    margin-bottom: 0;
    margin-right: $footer-min-padding;
  }
}
