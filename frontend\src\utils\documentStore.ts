import { create } from 'zustand';
import { getAuth } from 'firebase/auth';
import { collection, query, where, getDocs, getDoc, addDoc, updateDoc, deleteDoc, doc, serverTimestamp, Timestamp, orderBy } from 'firebase/firestore';
import { getFirestore } from 'firebase/firestore';
import { firebaseApp } from 'app';
import { COLLECTIONS, PLAN_FEATURES, AccountPlan } from './firebaseConfig';
import { useUserProfileStore } from './userProfileStore';

// Initialize Firestore
const db = getFirestore(firebaseApp);

// Define document types
export interface DocumentMeta {
  id: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  language: string; // Programming language
  language_used?: string; // Language documentation was generated in
  codeSize: number;
  isPublic: boolean;
  tags: string[];
  project_context?: string; // Additional context for documentation generation
}

export interface DocumentExportCache {
  format: string; // 'markdown', 'html', 'text', 'docx', 'pdf'
  data: string; // The cached content or a URL to the stored file
  updatedAt: Date; // When this format was last generated
}

export interface DocumentContent {
  originalCode: string;
  generatedDocumentation?: string;
  parameters?: Record<string, any>;
  toolsUsed?: string[];
  workflow?: any; // Will be a more specific type later
  manualEdits?: {
    timestamp: Date;
    section: string;
    content: string;
  }[];
  exportCache?: DocumentExportCache[]; // Cache of previously exported formats
}

export interface Document extends DocumentMeta {
  content: DocumentContent;
}

// Define the store interface
interface DocumentStore {
  // Document state
  documents: DocumentMeta[];
  currentDocument: Document | null;
  isLoading: boolean;
  error: Error | null;
  
  // Document actions
  fetchUserDocuments: (userId: string) => Promise<void>;
  getDocumentById: (docId: string) => Promise<void>;
  createDocument: (doc: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateDocument: (docId: string, updates: Partial<Document>) => Promise<void>;
  deleteDocument: (docId: string) => Promise<void>;
  clearCurrentDocument: () => void;
  checkDocumentLimit: (userId: string) => Promise<{ canCreate: boolean; currentCount: number; maxAllowed: number; }>;
}

export const useDocumentStore = create<DocumentStore>((set, get) => ({
  // Initial state
  documents: [],
  currentDocument: null,
  isLoading: false,
  error: null,
  
  // Fetch all documents for a user
  fetchUserDocuments: async (userId: string) => {
    try {
      set({ isLoading: true, error: null });
      console.log('Fetching documents for user:', userId);
      
      // Query for non-deleted documents only
      const docsQuery = query(
        collection(db, COLLECTIONS.DOCUMENTS),
        where('userId', '==', userId),
        where('isDeleted', '==', false)
      );
      
      console.log('Executing Firestore query...');
      const querySnapshot = await getDocs(docsQuery);
      console.log(`Retrieved ${querySnapshot.size} documents`);
      
      const docs: DocumentMeta[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        docs.push({
          id: doc.id,
          title: data.title,
          description: data.description,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate(),
          userId: data.userId,
          language: data.language,
          codeSize: data.codeSize,
          isPublic: data.isPublic,
          tags: data.tags || []
        });
      });
      
      // Sort docs by updatedAt manually instead of using Firestore orderBy
      docs.sort((a, b) => {
        if (a.updatedAt && b.updatedAt) {
          return b.updatedAt.getTime() - a.updatedAt.getTime();
        }
        return 0;
      });
      
      console.log(`Sorted and processed ${docs.length} documents`);
      set({ documents: docs, isLoading: false });
    } catch (error) {
      console.error('Error fetching documents:', error);
      set({ error: error as Error, isLoading: false });
    }
  },
  
  // Get a specific document by ID
  getDocumentById: async (docId: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get the document reference
      const docRef = doc(db, COLLECTIONS.DOCUMENTS, docId);
      const docSnapshot = await getDoc(docRef);
      
      if (!docSnapshot.exists()) {
        throw new Error('Document not found');
      }
      
      const metaData = docSnapshot.data();
      
      // Get content subcollection
      const contentSnap = await getDocs(collection(docRef, 'content'));
      
      if (contentSnap.empty) {
        throw new Error('Document content not found');
      }
      
      const contentData = contentSnap.docs[0].data();
      
      const document: Document = {
        id: docId,
        title: metaData.title,
        description: metaData.description,
        createdAt: metaData.createdAt?.toDate(),
        updatedAt: metaData.updatedAt?.toDate(),
        userId: metaData.userId,
        language: metaData.language,
        codeSize: metaData.codeSize,
        isPublic: metaData.isPublic,
        tags: metaData.tags || [],
        content: {
          originalCode: contentData.originalCode,
          generatedDocumentation: contentData.generatedDocumentation,
          parameters: contentData.parameters,
          toolsUsed: contentData.toolsUsed,
          workflow: contentData.workflow,
          manualEdits: contentData.manualEdits?.map((edit: any) => ({
            ...edit,
            timestamp: edit.timestamp?.toDate()
          }))
        }
      };
      
      set({ currentDocument: document, isLoading: false });
    } catch (error) {
      console.error('Error fetching document:', error);
      set({ error: error as Error, isLoading: false, currentDocument: null });
    }
  },
  
  // Create a new document
  createDocument: async (doc) => {
    try {
      set({ isLoading: true, error: null });
      
      // First, check if user is authenticated
      const auth = getAuth(firebaseApp);
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        console.error('Authentication error: No current user found');
        throw new Error('User must be authenticated to create documents');
      }
      
      // Verify user ID matches the authenticated user
      if (doc.userId !== currentUser.uid) {
        console.error(`User ID mismatch: Document user ID (${doc.userId}) does not match current user (${currentUser.uid})`);
        throw new Error('Document user ID must match authenticated user');
      }
      
      const timestamp = serverTimestamp();
      
      // Create the document metadata
      const docRef = await addDoc(collection(db, COLLECTIONS.DOCUMENTS), {
        title: doc.title,
        description: doc.description || '',
        createdAt: timestamp,
        updatedAt: timestamp,
        userId: currentUser.uid, // Always use the current user's UID from auth
        language: doc.language,
        codeSize: doc.content.originalCode.length,
        isPublic: doc.isPublic,
        tags: doc.tags || []
      });
      
      // Create the document content as a subcollection
      await addDoc(collection(db, COLLECTIONS.DOCUMENTS, docRef.id, 'content'), {
        originalCode: doc.content.originalCode,
        generatedDocumentation: doc.content.generatedDocumentation || '',
        parameters: doc.content.parameters || {},
        toolsUsed: doc.content.toolsUsed || [],
        workflow: doc.content.workflow || null,
        manualEdits: doc.content.manualEdits || []
      });
      
      // Refetch the documents to update the list
      await get().fetchUserDocuments(doc.userId);
      
      set({ isLoading: false });
      return docRef.id;
    } catch (error) {
      console.error('Error creating document:', error);
      set({ error: error as Error, isLoading: false });
      throw error;
    }
  },
  
  // Update an existing document
  updateDocument: async (docId, updates) => {
    try {
      set({ isLoading: true, error: null });
      
      // First, check if user is authenticated
      const auth = getAuth(firebaseApp);
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        throw new Error('User must be authenticated to update documents');
      }
      
      const timestamp = serverTimestamp();
      const docRef = doc(db, COLLECTIONS.DOCUMENTS, docId);
      
      // Update metadata
      const metaUpdates: any = { updatedAt: timestamp };
      
      if (updates.title) metaUpdates.title = updates.title;
      if (updates.description !== undefined) metaUpdates.description = updates.description;
      if (updates.isPublic !== undefined) metaUpdates.isPublic = updates.isPublic;
      if (updates.tags) metaUpdates.tags = updates.tags;
      
      await updateDoc(docRef, metaUpdates);
      
      // Update content if needed
      if (updates.content) {
        const contentQuery = query(collection(docRef, 'content'));
        const contentSnap = await getDocs(contentQuery);
        
        if (!contentSnap.empty) {
          const contentRef = contentSnap.docs[0].ref;
          const contentUpdates: any = {};
          
          if (updates.content.originalCode !== undefined) {
            contentUpdates.originalCode = updates.content.originalCode;
            metaUpdates.codeSize = updates.content.originalCode.length;
            await updateDoc(docRef, { codeSize: updates.content.originalCode.length });
          }
          
          if (updates.content.generatedDocumentation !== undefined) {
            contentUpdates.generatedDocumentation = updates.content.generatedDocumentation;
          }
          
          if (updates.content.parameters !== undefined) {
            contentUpdates.parameters = updates.content.parameters;
          }
          
          if (updates.content.toolsUsed !== undefined) {
            contentUpdates.toolsUsed = updates.content.toolsUsed;
          }
          
          if (updates.content.workflow !== undefined) {
            contentUpdates.workflow = updates.content.workflow;
          }
          
          if (updates.content.manualEdits !== undefined) {
            contentUpdates.manualEdits = updates.content.manualEdits;
          }
          
          await updateDoc(contentRef, contentUpdates);
        }
      }
      
      // Refresh the current document if it's the one being updated
      if (get().currentDocument?.id === docId) {
        await get().getDocumentById(docId);
      }
      
      // Refresh the document list
      const currentUserId = get().currentDocument?.userId;
      if (currentUserId) {
        await get().fetchUserDocuments(currentUserId);
      }
      
      set({ isLoading: false });
    } catch (error) {
      console.error('Error updating document:', error);
      set({ error: error as Error, isLoading: false });
    }
  },
  
  // Delete a document
  deleteDocument: async (docId) => {
    try {
      set({ isLoading: true, error: null });
      
      // First, check if user is authenticated
      const auth = getAuth(firebaseApp);
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        console.error('Authentication error: No current user found for deletion');
        throw new Error('User must be authenticated to delete documents');
      }
      
      // Get the document to check ownership
      const docRef = doc(db, COLLECTIONS.DOCUMENTS, docId);
      const docSnapshot = await getDoc(docRef);
      
      if (!docSnapshot.exists()) {
        throw new Error('Document not found');
      }
      
      const documentData = docSnapshot.data();
      
      // Verify the user owns this document
      if (documentData.userId !== currentUser.uid) {
        console.error(`Permission error: User ${currentUser.uid} does not own document ${docId} (owned by ${documentData.userId})`);
        throw new Error('Missing or insufficient permissions to delete this document');
      }
      
      // Use soft delete instead of hard delete to avoid permission issues
      // Mark the document as deleted instead of actually deleting it
      await updateDoc(docRef, {
        isDeleted: true,
        deletedAt: new Date()
      });
      
      // Clear the current document if it's the one being deleted
      if (get().currentDocument?.id === docId) {
        set({ currentDocument: null });
      }
      
      // Refresh document list
      await get().fetchUserDocuments(currentUser.uid);
      
      set({ isLoading: false });
    } catch (error) {
      console.error('Error deleting document:', error);
      set({ error: error as Error, isLoading: false });
      throw error; // Re-throw the error to allow handling in the UI
    }
  },
  
  // Clear the current document
  clearCurrentDocument: () => {
    set({ currentDocument: null });
  },
  
  // Check if the user has reached their document limit
  checkDocumentLimit: async (userId: string) => {
    try {
      // Get user profile to determine account plan
      const userProfile = useUserProfileStore.getState().profile;
      const accountPlan = userProfile?.accountPlan || AccountPlan.FREE;
      
      // Get the limit for the user's plan
      const planFeatures = PLAN_FEATURES[accountPlan];
      const maxDocuments = planFeatures.maxDocuments;
      
      // Get current document count
      const docsQuery = query(
        collection(db, COLLECTIONS.DOCUMENTS),
        where('userId', '==', userId),
        where('isDeleted', '==', false)
      );
      
      const querySnapshot = await getDocs(docsQuery);
      const currentCount = querySnapshot.size;
      
      return {
        canCreate: currentCount < maxDocuments,
        currentCount,
        maxAllowed: maxDocuments
      };
    } catch (error) {
      console.error('Error checking document limit:', error);
      // Always allow document creation if there's an error checking
      // This ensures users can still create documents even if the limit check fails
      return { canCreate: true, currentCount: 0, maxAllowed: 5 };
    }
  }
}));
