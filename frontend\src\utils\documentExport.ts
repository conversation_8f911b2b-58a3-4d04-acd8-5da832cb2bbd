/**
 * Document export utilities for CodeScribe GIS
 * Handles exporting documents in various formats (Markdown, HTML, etc.)
 */
import { DocumentMeta, DocumentationData } from "./documentStore";

/**
 * Export format options
 */
export enum ExportFormat {
  MARKDOWN = "markdown",
  HTML = "html",
  TEXT = "text"
}

/**
 * Options for document export
 */
export interface ExportOptions {
  format: ExportFormat;
  includeCode: boolean;
  includeMetadata: boolean;
}

/**
 * Default export options
 */
export const defaultExportOptions: ExportOptions = {
  format: ExportFormat.MARKDOWN,
  includeCode: true,
  includeMetadata: true
};

/**
 * Export a document to a specific format
 */
export async function exportDocument(
  docMeta: DocumentMeta,
  docData: DocumentationData,
  options: Partial<ExportOptions> = {}
): Promise<{ content: string; filename: string }> {
  // Merge with default options
  const exportOptions = { ...defaultExportOptions, ...options };
  
  // Format the output based on the selected format
  let content = "";
  let extension = "";
  
  switch (exportOptions.format) {
    case ExportFormat.MARKDOWN:
      content = formatAsMarkdown(docMeta, docData, exportOptions);
      extension = "md";
      break;
    case ExportFormat.HTML:
      content = formatAsHtml(docMeta, docData, exportOptions);
      extension = "html";
      break;
    case ExportFormat.TEXT:
    default:
      content = formatAsText(docMeta, docData, exportOptions);
      extension = "txt";
      break;
  }
  
  // Generate a sanitized filename
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-").substring(0, 19);
  const filename = `${sanitizeFilename(docMeta.title)}_${timestamp}.${extension}`;
  
  return { content, filename };
}

/**
 * Format document as Markdown
 */
function formatAsMarkdown(
  meta: DocumentMeta,
  data: DocumentationData,
  options: ExportOptions
): string {
  let output = ``;
  
  // Title and metadata
  output += `# ${meta.title}\n\n`;
  
  if (options.includeMetadata) {
    output += `> **Generated by:** CodeScribe GIS\n`;
    output += `> **Date:** ${meta.updatedAt.toLocaleDateString()} ${meta.updatedAt.toLocaleTimeString()}\n`;
    output += `> **Language:** ${meta.language}\n`;
    if (meta.tags && meta.tags.length > 0) {
      output += `> **Tags:** ${meta.tags.join(", ")}\n`;
    }
    output += `\n`;
  }
  
  // Description
  if (data.description) {
    output += `## Description\n\n${data.description}\n\n`;
  }
  
  // Parameters
  if (data.parameters && data.parameters.length > 0) {
    output += `## Parameters\n\n`;
    output += `| Name | Type | Required | Description |\n`;
    output += `|------|------|----------|-------------|\n`;
    
    data.parameters.forEach(param => {
      output += `| ${param.name} | ${param.type} | ${param.required ? "Yes" : "No"} | ${param.description} |\n`;
    });
    
    output += `\n`;
  }
  
  // Functions and Tools
  if (data.functions && data.functions.length > 0) {
    output += `## Functions and Tools\n\n`;
    
    data.functions.forEach(func => {
      output += `### ${func.name}\n\n`;
      output += `${func.description}\n\n`;
      
      if (func.parameters && func.parameters.length > 0) {
        output += `**Parameters:**\n\n`;
        output += `| Name | Type | Description |\n`;
        output += `|------|------|-------------|\n`;
        
        func.parameters.forEach(param => {
          output += `| ${param.name} | ${param.type} | ${param.description} |\n`;
        });
        
        output += `\n`;
      }
      
      if (func.returns) {
        output += `**Returns:** ${func.returns}\n\n`;
      }
    });
  }
  
  // Workflow
  if (data.workflow) {
    output += `## Workflow\n\n${data.workflow}\n\n`;
  }
  
  // Code
  if (options.includeCode && meta.code) {
    output += `## Original Code\n\n\`\`\`${meta.language.toLowerCase()}\n${meta.code}\n\`\`\`\n`;
  }
  
  return output;
}

/**
 * Format document as HTML
 */
function formatAsHtml(
  meta: DocumentMeta,
  data: DocumentationData,
  options: ExportOptions
): string {
  let output = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${escapeHtml(meta.title)}</title>
  <style>
    body { font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
    pre { background: #f5f5f5; padding: 16px; border-radius: 4px; overflow-x: auto; }
    code { font-family: 'Courier New', Courier, monospace; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .metadata { background-color: #f8f9fa; padding: 12px; border-radius: 4px; margin-bottom: 20px; }
  </style>
</head>
<body>
  <h1>${escapeHtml(meta.title)}</h1>
`;

  // Metadata
  if (options.includeMetadata) {
    output += `  <div class="metadata">
    <p><strong>Generated by:</strong> CodeScribe GIS</p>
    <p><strong>Date:</strong> ${meta.updatedAt.toLocaleDateString()} ${meta.updatedAt.toLocaleTimeString()}</p>
    <p><strong>Language:</strong> ${escapeHtml(meta.language)}</p>
`;
    
    if (meta.tags && meta.tags.length > 0) {
      output += `    <p><strong>Tags:</strong> ${escapeHtml(meta.tags.join(", "))}</p>
`;
    }
    
    output += `  </div>
`;
  }
  
  // Description
  if (data.description) {
    output += `  <h2>Description</h2>
  <p>${escapeHtml(data.description).replace(/\n/g, '<br>')}</p>
`;
  }
  
  // Parameters
  if (data.parameters && data.parameters.length > 0) {
    output += `  <h2>Parameters</h2>
  <table>
    <thead>
      <tr>
        <th>Name</th>
        <th>Type</th>
        <th>Required</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
`;
    
    data.parameters.forEach(param => {
      output += `      <tr>
        <td>${escapeHtml(param.name)}</td>
        <td>${escapeHtml(param.type)}</td>
        <td>${param.required ? "Yes" : "No"}</td>
        <td>${escapeHtml(param.description)}</td>
      </tr>
`;
    });
    
    output += `    </tbody>
  </table>
`;
  }
  
  // Functions and Tools
  if (data.functions && data.functions.length > 0) {
    output += `  <h2>Functions and Tools</h2>
`;
    
    data.functions.forEach(func => {
      output += `  <h3>${escapeHtml(func.name)}</h3>
  <p>${escapeHtml(func.description)}</p>
`;
      
      if (func.parameters && func.parameters.length > 0) {
        output += `  <h4>Parameters</h4>
  <table>
    <thead>
      <tr>
        <th>Name</th>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
`;
        
        func.parameters.forEach(param => {
          output += `      <tr>
        <td>${escapeHtml(param.name)}</td>
        <td>${escapeHtml(param.type)}</td>
        <td>${escapeHtml(param.description)}</td>
      </tr>
`;
        });
        
        output += `    </tbody>
  </table>
`;
      }
      
      if (func.returns) {
        output += `  <h4>Returns</h4>
  <p>${escapeHtml(func.returns)}</p>
`;
      }
    });
  }
  
  // Workflow
  if (data.workflow) {
    output += `  <h2>Workflow</h2>
  <p>${escapeHtml(data.workflow).replace(/\n/g, '<br>')}</p>
`;
  }
  
  // Code
  if (options.includeCode && meta.code) {
    output += `  <h2>Original Code</h2>
  <pre><code>${escapeHtml(meta.code)}</code></pre>
`;
  }
  
  output += `</body>
</html>`;
  
  return output;
}

/**
 * Format document as plain text
 */
function formatAsText(
  meta: DocumentMeta,
  data: DocumentationData,
  options: ExportOptions
): string {
  let output = ``;
  
  // Title
  output += `${meta.title}\n`;
  output += `${'='.repeat(meta.title.length)}\n\n`;
  
  // Metadata
  if (options.includeMetadata) {
    output += `Generated by: CodeScribe GIS\n`;
    output += `Date: ${meta.updatedAt.toLocaleDateString()} ${meta.updatedAt.toLocaleTimeString()}\n`;
    output += `Language: ${meta.language}\n`;
    if (meta.tags && meta.tags.length > 0) {
      output += `Tags: ${meta.tags.join(", ")}\n`;
    }
    output += `\n`;
  }
  
  // Description
  if (data.description) {
    output += `DESCRIPTION\n-----------\n\n${data.description}\n\n`;
  }
  
  // Parameters
  if (data.parameters && data.parameters.length > 0) {
    output += `PARAMETERS\n----------\n\n`;
    
    // Calculate column widths
    const nameWidth = Math.max(4, ...data.parameters.map(p => p.name.length));
    const typeWidth = Math.max(4, ...data.parameters.map(p => p.type.length));
    const reqWidth = 8; // For "Required"
    
    // Header
    output += `${'Name'.padEnd(nameWidth)} | ${'Type'.padEnd(typeWidth)} | ${'Required'.padEnd(reqWidth)} | Description\n`;
    output += `${'-'.repeat(nameWidth)} | ${'-'.repeat(typeWidth)} | ${'-'.repeat(reqWidth)} | ${'-'.repeat(11)}\n`;
    
    // Rows
    data.parameters.forEach(param => {
      output += `${param.name.padEnd(nameWidth)} | ${param.type.padEnd(typeWidth)} | ${(param.required ? "Yes" : "No").padEnd(reqWidth)} | ${param.description}\n`;
    });
    
    output += `\n`;
  }
  
  // Functions and Tools
  if (data.functions && data.functions.length > 0) {
    output += `FUNCTIONS AND TOOLS\n------------------\n\n`;
    
    data.functions.forEach(func => {
      output += `${func.name}\n${'-'.repeat(func.name.length)}\n\n`;
      output += `${func.description}\n\n`;
      
      if (func.parameters && func.parameters.length > 0) {
        output += `Parameters:\n\n`;
        
        // Calculate column widths
        const nameWidth = Math.max(4, ...func.parameters.map(p => p.name.length));
        const typeWidth = Math.max(4, ...func.parameters.map(p => p.type.length));
        
        // Header
        output += `${'Name'.padEnd(nameWidth)} | ${'Type'.padEnd(typeWidth)} | Description\n`;
        output += `${'-'.repeat(nameWidth)} | ${'-'.repeat(typeWidth)} | ${'-'.repeat(11)}\n`;
        
        // Rows
        func.parameters.forEach(param => {
          output += `${param.name.padEnd(nameWidth)} | ${param.type.padEnd(typeWidth)} | ${param.description}\n`;
        });
        
        output += `\n`;
      }
      
      if (func.returns) {
        output += `Returns: ${func.returns}\n\n`;
      }
    });
  }
  
  // Workflow
  if (data.workflow) {
    output += `WORKFLOW\n--------\n\n${data.workflow}\n\n`;
  }
  
  // Code
  if (options.includeCode && meta.code) {
    output += `ORIGINAL CODE\n------------\n\n${meta.code}\n`;
  }
  
  return output;
}

/**
 * Sanitize a filename to be safe for all operating systems
 * Removes invalid characters and limits length
 */
function sanitizeFilename(filename: string): string {
  // Remove invalid characters
  let sanitized = filename.replace(/[\\/:*?"<>|]/g, "_");
  
  // Limit length (some file systems have limits)
  if (sanitized.length > 100) {
    sanitized = sanitized.substring(0, 100);
  }
  
  return sanitized;
}

/**
 * Escape HTML special characters to prevent XSS
 */
function escapeHtml(text: string): string {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Trigger a file download in the browser
 */
export function downloadFile(filename: string, content: string, mimeType: string = "text/plain"): void {
  // Create a blob with the content
  const blob = new Blob([content], { type: mimeType });
  
  // Create a URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Create a link element
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  
  // Append the link to the body
  document.body.appendChild(link);
  
  // Click the link to trigger the download
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
