import{g as s,d as h,c as m}from"./index-3b130772.js";import{h as w,i as S,j as x,M as E,o as $,W as O,e as V,f as F,l as G,n as j,k as M,m as W,u as C,a as T}from"./index-3b130772.js";import{m as n,j as d,a0 as f,G as k,a1 as I,a2 as U,a3 as g,S as c}from"./vendor-8eb8bd34.js";const y=o=>{const r=[];return o.google&&r.push(k.PROVIDER_ID),o.facebook&&r.push(I.PROVIDER_ID),o.github&&r.push(U.PROVIDER_ID),o.twitter&&r.push(g.PROVIDER_ID),o.emailAndPassword&&r.push(c.PROVIDER_ID),o.magicLink&&r.push(c.PROVIDER_ID),r},v=o=>{const r=n.useMemo(()=>y(o.signInOptions),[o.signInOptions]),u=n.useMemo(()=>{const a=new URLSearchParams(window.location.search),e=a.get("next"),t=new URLSearchParams;for(const[i,p]of a.entries())i!=="next"&&t.append(i,p);return`//${e?`${e}${t.toString()?`?${t.toString()}`:""}`:`${s.signInSuccessUrl}`}`.replace(/\/+/g,"/")},[]),l=s.privacyPolicyLink?()=>{var e;const a=(e=s.privacyPolicyLink)!=null&&e.startsWith("/")?`//${s.privacyPolicyLink}`:s.privacyPolicyLink;window.open(a,"_blank")}:void 0,P=s.tosLink?()=>{var e;const a=(e=s.tosLink)!=null&&e.startsWith("/")?`//${s.tosLink}`:s.tosLink;window.open(a,"_blank")}:void 0;return d.jsx(f,{firebaseAuth:h,uiConfig:{signInFlow:"popup",autoUpgradeAnonymousUsers:!0,signInOptions:r,signInSuccessUrl:u,siteName:s.siteName,callbacks:{signInFailure:a=>{throw a}},privacyPolicyUrl:l,tosUrl:P}})},L=m;export{w as API_URL,S as APP_BASE_PATH,x as APP_ID,E as Mode,v as SignInOrUpForm,$ as UserGuard,O as WS_API_URL,V as auth,L as backend,F as firebaseApp,h as firebaseAuth,G as firebaseDb,j as firebaseStorage,M as firestore,W as mode,C as useCurrentUser,T as useUserGuardContext};
