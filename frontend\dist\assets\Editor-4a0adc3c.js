import{c as tt,a as Ea,b as Na,f as kt,C as Ge,_ as Pa}from"./index-3b130772.js";import{j as a,_ as Ee,P as W,m as s,W as Ce,A as Aa,n as za,a as Dt,v as ke,C as _e,D as Xe,x as Qt,B as Ta,w as en,F as Ma}from"./vendor-8eb8bd34.js";import{P as we,u as E}from"./index-fcfcda09.js";import{e as Ne,f as Ra,u as ye,j as Oa,B as ue,C as tn,a as nn,b as an,c as on}from"./card-b935221e.js";import{L as Ie,F as _a,A as St,P as La,f as Ia}from"./documentPermissions-f0c02e2b.js";import{j as Fa}from"./index-ae1e7bbf.js";import{c as Ft,f as Cn,A as $a,a as fe,P as Ve,D as qa,C as Ua,h as Ha,d as Va,u as Ba,R as Wa,i as Ka,g as De,j as Ya}from"./index-89be2a26.js";import{R as Ga,c as Xa}from"./index-ee3cbf23.js";import{c as ne}from"./createLucideIcon-6c39ff51.js";/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Za=ne("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ja=ne("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=ne("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=ne("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qa=ne("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const En=ne("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ei=ne("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=ne("FileCode2",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4",key:"1pf5j1"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m5 12-3 3 3 3",key:"oke12k"}],["path",{d:"m9 18 3-3-3-3",key:"112psh"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ti=ne("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ni=ne("FileWarning",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ai=ne("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ii=ne("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oi=ne("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ri=ne("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const si=ne("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=ne("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nn=ne("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=ne("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=ne("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function di({isLoading:e,message:t="Generating documentation...",overlay:n=!0}){return e?a.jsx("div",{className:`${n?"fixed inset-0 bg-background/80 backdrop-blur-sm z-50":"relative"} flex flex-col items-center justify-center`,children:a.jsxs("div",{className:"relative p-6 bg-card/30 backdrop-blur-md border border-primary/20 shadow-lg rounded-lg overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 -z-10 opacity-10",children:a.jsxs("svg",{width:"100%",height:"100%",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("pattern",{id:"hexagons",width:"50",height:"43.4",patternUnits:"userSpaceOnUse",patternTransform:"scale(2) rotate(0)",children:a.jsx("polygon",{points:"25,0 50,14.4 50,37.6 25,52 0,37.6 0,14.4",fill:"none",stroke:"currentColor",strokeWidth:"1"})}),a.jsx("rect",{width:"100%",height:"100%",fill:"url(#hexagons)"})]})}),a.jsxs("div",{className:"flex flex-col items-center gap-4",children:[a.jsxs("div",{className:"relative h-16 w-16",children:[a.jsx(Ie,{className:"h-16 w-16 animate-spin text-primary"}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"h-4 w-4 rounded-full bg-primary animate-pulse"})})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("h3",{className:"font-mono text-lg tracking-wider",children:t}),a.jsx("p",{className:"text-muted-foreground text-sm mt-1",children:"This process may take some time..."})]})]})]})}):null}const ui=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function Te(e,t,n){const i=mi(e),{webkitRelativePath:o}=e,r=typeof t=="string"?t:typeof o=="string"&&o.length>0?o:`./${e.name}`;return typeof i.path!="string"&&rn(i,"path",r),n!==void 0&&Object.defineProperty(i,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),rn(i,"relativePath",r),i}function mi(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const i=t.split(".").pop().toLowerCase(),o=ui.get(i);o&&Object.defineProperty(e,"type",{value:o,writable:!1,configurable:!1,enumerable:!0})}return e}function rn(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const fi=[".DS_Store","Thumbs.db"];function xi(e){return Ee(this,void 0,void 0,function*(){return nt(e)&&gi(e.dataTransfer)?wi(e.dataTransfer,e.type):vi(e)?hi(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?bi(e):[]})}function gi(e){return nt(e)}function vi(e){return nt(e)&&nt(e.target)}function nt(e){return typeof e=="object"&&e!==null}function hi(e){return Tt(e.target.files).map(t=>Te(t))}function bi(e){return Ee(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>Te(n))})}function wi(e,t){return Ee(this,void 0,void 0,function*(){if(e.items){const n=Tt(e.items).filter(o=>o.kind==="file");if(t!=="drop")return n;const i=yield Promise.all(n.map(yi));return sn(Pn(i))}return sn(Tt(e.files).map(n=>Te(n)))})}function sn(e){return e.filter(t=>fi.indexOf(t.name)===-1)}function Tt(e){if(e===null)return[];const t=[];for(let n=0;n<e.length;n++){const i=e[n];t.push(i)}return t}function yi(e){if(typeof e.webkitGetAsEntry!="function")return ln(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?An(t):ln(e,t)}function Pn(e){return e.reduce((t,n)=>[...t,...Array.isArray(n)?Pn(n):[n]],[])}function ln(e,t){return Ee(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const r=yield e.getAsFileSystemHandle();if(r===null)throw new Error(`${e} is not a File`);if(r!==void 0){const l=yield r.getFile();return l.handle=r,Te(l)}}const i=e.getAsFile();if(!i)throw new Error(`${e} is not a File`);return Te(i,(n=t==null?void 0:t.fullPath)!==null&&n!==void 0?n:void 0)})}function ji(e){return Ee(this,void 0,void 0,function*(){return e.isDirectory?An(e):ki(e)})}function An(e){const t=e.createReader();return new Promise((n,i)=>{const o=[];function r(){t.readEntries(l=>Ee(this,void 0,void 0,function*(){if(l.length){const p=Promise.all(l.map(ji));o.push(p),r()}else try{const p=yield Promise.all(o);n(p)}catch(p){i(p)}}),l=>{i(l)})}r()})}function ki(e){return Ee(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(i=>{const o=Te(i,e.fullPath);t(o)},i=>{n(i)})})})}var Ct=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(n.length===0)return!0;var i=e.name||"",o=(e.type||"").toLowerCase(),r=o.replace(/\/.*$/,"");return n.some(function(l){var p=l.trim().toLowerCase();return p.charAt(0)==="."?i.toLowerCase().endsWith(p):p.endsWith("/*")?r===p.replace(/\/.*$/,""):o===p})}return!0};function cn(e){return Ci(e)||Si(e)||Tn(e)||Di()}function Di(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Si(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ci(e){if(Array.isArray(e))return Mt(e)}function pn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function dn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?pn(Object(n),!0).forEach(function(i){zn(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pn(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function zn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qe(e,t){return Pi(e)||Ni(e,t)||Tn(e,t)||Ei()}function Ei(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tn(e,t){if(e){if(typeof e=="string")return Mt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mt(e,t)}}function Mt(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function Ni(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],o=!0,r=!1,l,p;try{for(n=n.call(e);!(o=(l=n.next()).done)&&(i.push(l.value),!(t&&i.length===t));o=!0);}catch(d){r=!0,p=d}finally{try{!o&&n.return!=null&&n.return()}finally{if(r)throw p}}return i}}function Pi(e){if(Array.isArray(e))return e}var Ai=typeof Ct=="function"?Ct:Ct.default,zi="file-invalid-type",Ti="file-too-large",Mi="file-too-small",Ri="too-many-files",Oi=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=t.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:zi,message:"File type must be ".concat(i)}},un=function(t){return{code:Ti,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},mn=function(t){return{code:Mi,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},_i={code:Ri,message:"Too many files"};function Mn(e,t){var n=e.type==="application/x-moz-file"||Ai(e,t);return[n,n?null:Oi(t)]}function Rn(e,t,n){if(Se(e.size))if(Se(t)&&Se(n)){if(e.size>n)return[!1,un(n)];if(e.size<t)return[!1,mn(t)]}else{if(Se(t)&&e.size<t)return[!1,mn(t)];if(Se(n)&&e.size>n)return[!1,un(n)]}return[!0,null]}function Se(e){return e!=null}function Li(e){var t=e.files,n=e.accept,i=e.minSize,o=e.maxSize,r=e.multiple,l=e.maxFiles,p=e.validator;return!r&&t.length>1||r&&l>=1&&t.length>l?!1:t.every(function(d){var c=Mn(d,n),u=qe(c,1),m=u[0],f=Rn(d,i,o),v=qe(f,1),w=v[0],h=p?p(d):null;return m&&w&&!h})}function at(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Ze(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function fn(e){e.preventDefault()}function Ii(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Fi(e){return e.indexOf("Edge/")!==-1}function $i(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Ii(e)||Fi(e)}function ve(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(i){for(var o=arguments.length,r=new Array(o>1?o-1:0),l=1;l<o;l++)r[l-1]=arguments[l];return t.some(function(p){return!at(i)&&p&&p.apply(void 0,[i].concat(r)),at(i)})}}function qi(){return"showOpenFilePicker"in window}function Ui(e){if(Se(e)){var t=Object.entries(e).filter(function(n){var i=qe(n,2),o=i[0],r=i[1],l=!0;return On(o)||(console.warn('Skipped "'.concat(o,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),l=!1),(!Array.isArray(r)||!r.every(_n))&&(console.warn('Skipped "'.concat(o,'" because an invalid file extension was provided.')),l=!1),l}).reduce(function(n,i){var o=qe(i,2),r=o[0],l=o[1];return dn(dn({},n),{},zn({},r,l))},{});return[{description:"Files",accept:t}]}return e}function Hi(e){if(Se(e))return Object.entries(e).reduce(function(t,n){var i=qe(n,2),o=i[0],r=i[1];return[].concat(cn(t),[o],cn(r))},[]).filter(function(t){return On(t)||_n(t)}).join(",")}function Vi(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Bi(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function On(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function _n(e){return/^.*\.[\w]+$/.test(e)}var Wi=["children"],Ki=["open"],Yi=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Gi=["refKey","onChange","onClick"];function Xi(e){return Qi(e)||Ji(e)||Ln(e)||Zi()}function Zi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ji(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Qi(e){if(Array.isArray(e))return Rt(e)}function Et(e,t){return no(e)||to(e,t)||Ln(e,t)||eo()}function eo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ln(e,t){if(e){if(typeof e=="string")return Rt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rt(e,t)}}function Rt(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function to(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],o=!0,r=!1,l,p;try{for(n=n.call(e);!(o=(l=n.next()).done)&&(i.push(l.value),!(t&&i.length===t));o=!0);}catch(d){r=!0,p=d}finally{try{!o&&n.return!=null&&n.return()}finally{if(r)throw p}}return i}}function no(e){if(Array.isArray(e))return e}function xn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?xn(Object(n),!0).forEach(function(i){Ot(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xn(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function Ot(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function it(e,t){if(e==null)return{};var n=ao(e,t),i,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function ao(e,t){if(e==null)return{};var n={},i=Object.keys(e),o,r;for(r=0;r<i.length;r++)o=i[r],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}var $t=s.forwardRef(function(e,t){var n=e.children,i=it(e,Wi),o=Fn(i),r=o.open,l=it(o,Ki);return s.useImperativeHandle(t,function(){return{open:r}},[r]),Ce.createElement(s.Fragment,null,n(J(J({},l),{},{open:r})))});$t.displayName="Dropzone";var In={disabled:!1,getFilesFromEvent:xi,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};$t.defaultProps=In;$t.propTypes={children:W.func,accept:W.objectOf(W.arrayOf(W.string)),multiple:W.bool,preventDropOnDocument:W.bool,noClick:W.bool,noKeyboard:W.bool,noDrag:W.bool,noDragEventsBubbling:W.bool,minSize:W.number,maxSize:W.number,maxFiles:W.number,disabled:W.bool,getFilesFromEvent:W.func,onFileDialogCancel:W.func,onFileDialogOpen:W.func,useFsAccessApi:W.bool,autoFocus:W.bool,onDragEnter:W.func,onDragLeave:W.func,onDragOver:W.func,onDrop:W.func,onDropAccepted:W.func,onDropRejected:W.func,onError:W.func,validator:W.func};var _t={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Fn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=J(J({},In),e),n=t.accept,i=t.disabled,o=t.getFilesFromEvent,r=t.maxSize,l=t.minSize,p=t.multiple,d=t.maxFiles,c=t.onDragEnter,u=t.onDragLeave,m=t.onDragOver,f=t.onDrop,v=t.onDropAccepted,w=t.onDropRejected,h=t.onFileDialogCancel,D=t.onFileDialogOpen,L=t.useFsAccessApi,S=t.autoFocus,T=t.preventDropOnDocument,F=t.noClick,M=t.noKeyboard,O=t.noDrag,I=t.noDragEventsBubbling,X=t.onError,A=t.validator,P=s.useMemo(function(){return Hi(n)},[n]),Y=s.useMemo(function(){return Ui(n)},[n]),se=s.useMemo(function(){return typeof D=="function"?D:gn},[D]),ie=s.useMemo(function(){return typeof h=="function"?h:gn},[h]),V=s.useRef(null),j=s.useRef(null),_=s.useReducer(io,_t),oe=Et(_,2),le=oe[0],z=oe[1],x=le.isFocused,g=le.isFileDialogActive,y=s.useRef(typeof window<"u"&&window.isSecureContext&&L&&qi()),R=function(){!y.current&&g&&setTimeout(function(){if(j.current){var N=j.current.files;N.length||(z({type:"closeDialog"}),ie())}},300)};s.useEffect(function(){return window.addEventListener("focus",R,!1),function(){window.removeEventListener("focus",R,!1)}},[j,g,ie,y]);var $=s.useRef([]),k=function(N){V.current&&V.current.contains(N.target)||(N.preventDefault(),$.current=[])};s.useEffect(function(){return T&&(document.addEventListener("dragover",fn,!1),document.addEventListener("drop",k,!1)),function(){T&&(document.removeEventListener("dragover",fn),document.removeEventListener("drop",k))}},[V,T]),s.useEffect(function(){return!i&&S&&V.current&&V.current.focus(),function(){}},[V,S,i]);var q=s.useCallback(function(b){X?X(b):console.error(b)},[X]),B=s.useCallback(function(b){b.preventDefault(),b.persist(),ee(b),$.current=[].concat(Xi($.current),[b.target]),Ze(b)&&Promise.resolve(o(b)).then(function(N){if(!(at(b)&&!I)){var Z=N.length,te=Z>0&&Li({files:N,accept:P,minSize:l,maxSize:r,multiple:p,maxFiles:d,validator:A}),re=Z>0&&!te;z({isDragAccept:te,isDragReject:re,isDragActive:!0,type:"setDraggedFiles"}),c&&c(b)}}).catch(function(N){return q(N)})},[o,c,q,I,P,l,r,p,d,A]),G=s.useCallback(function(b){b.preventDefault(),b.persist(),ee(b);var N=Ze(b);if(N&&b.dataTransfer)try{b.dataTransfer.dropEffect="copy"}catch{}return N&&m&&m(b),!1},[m,I]),Q=s.useCallback(function(b){b.preventDefault(),b.persist(),ee(b);var N=$.current.filter(function(te){return V.current&&V.current.contains(te)}),Z=N.indexOf(b.target);Z!==-1&&N.splice(Z,1),$.current=N,!(N.length>0)&&(z({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ze(b)&&u&&u(b))},[V,u,I]),U=s.useCallback(function(b,N){var Z=[],te=[];b.forEach(function(re){var Oe=Mn(re,P),Ae=Et(Oe,2),gt=Ae[0],vt=Ae[1],ht=Rn(re,l,r),Ye=Et(ht,2),bt=Ye[0],wt=Ye[1],yt=A?A(re):null;if(gt&&bt&&!yt)Z.push(re);else{var jt=[vt,wt];yt&&(jt=jt.concat(yt)),te.push({file:re,errors:jt.filter(function(Ca){return Ca})})}}),(!p&&Z.length>1||p&&d>=1&&Z.length>d)&&(Z.forEach(function(re){te.push({file:re,errors:[_i]})}),Z.splice(0)),z({acceptedFiles:Z,fileRejections:te,isDragReject:te.length>0,type:"setFiles"}),f&&f(Z,te,N),te.length>0&&w&&w(te,N),Z.length>0&&v&&v(Z,N)},[z,p,P,l,r,d,f,v,w,A]),H=s.useCallback(function(b){b.preventDefault(),b.persist(),ee(b),$.current=[],Ze(b)&&Promise.resolve(o(b)).then(function(N){at(b)&&!I||U(N,b)}).catch(function(N){return q(N)}),z({type:"reset"})},[o,U,q,I]),C=s.useCallback(function(){if(y.current){z({type:"openDialog"}),se();var b={multiple:p,types:Y};window.showOpenFilePicker(b).then(function(N){return o(N)}).then(function(N){U(N,null),z({type:"closeDialog"})}).catch(function(N){Vi(N)?(ie(N),z({type:"closeDialog"})):Bi(N)?(y.current=!1,j.current?(j.current.value=null,j.current.click()):q(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):q(N)});return}j.current&&(z({type:"openDialog"}),se(),j.current.value=null,j.current.click())},[z,se,ie,L,U,q,Y,p]),ce=s.useCallback(function(b){!V.current||!V.current.isEqualNode(b.target)||(b.key===" "||b.key==="Enter"||b.keyCode===32||b.keyCode===13)&&(b.preventDefault(),C())},[V,C]),K=s.useCallback(function(){z({type:"focus"})},[]),je=s.useCallback(function(){z({type:"blur"})},[]),de=s.useCallback(function(){F||($i()?setTimeout(C,0):C())},[F,C]),pe=function(N){return i?null:N},Pe=function(N){return M?null:pe(N)},ae=function(N){return O?null:pe(N)},ee=function(N){I&&N.stopPropagation()},xe=s.useMemo(function(){return function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},N=b.refKey,Z=N===void 0?"ref":N,te=b.role,re=b.onKeyDown,Oe=b.onFocus,Ae=b.onBlur,gt=b.onClick,vt=b.onDragEnter,ht=b.onDragOver,Ye=b.onDragLeave,bt=b.onDrop,wt=it(b,Yi);return J(J(Ot({onKeyDown:Pe(ve(re,ce)),onFocus:Pe(ve(Oe,K)),onBlur:Pe(ve(Ae,je)),onClick:pe(ve(gt,de)),onDragEnter:ae(ve(vt,B)),onDragOver:ae(ve(ht,G)),onDragLeave:ae(ve(Ye,Q)),onDrop:ae(ve(bt,H)),role:typeof te=="string"&&te!==""?te:"presentation"},Z,V),!i&&!M?{tabIndex:0}:{}),wt)}},[V,ce,K,je,de,B,G,Q,H,M,O,i]),We=s.useCallback(function(b){b.stopPropagation()},[]),Ke=s.useMemo(function(){return function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},N=b.refKey,Z=N===void 0?"ref":N,te=b.onChange,re=b.onClick,Oe=it(b,Gi),Ae=Ot({accept:P,multiple:p,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:pe(ve(te,H)),onClick:pe(ve(re,We)),tabIndex:-1},Z,j);return J(J({},Ae),Oe)}},[j,n,p,H,i]);return J(J({},le),{},{isFocused:x&&!i,getRootProps:xe,getInputProps:Ke,rootRef:V,inputRef:j,open:pe(C)})}function io(e,t){switch(t.type){case"focus":return J(J({},e),{},{isFocused:!0});case"blur":return J(J({},e),{},{isFocused:!1});case"openDialog":return J(J({},_t),{},{isFileDialogActive:!0});case"closeDialog":return J(J({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return J(J({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return J(J({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return J({},_t);default:return e}}function gn(){}function oo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function hn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?vn(Object(n),!0).forEach(function(i){oo(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vn(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function ro(e,t){if(e==null)return{};var n={},i=Object.keys(e),o,r;for(r=0;r<i.length;r++)o=i[r],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function so(e,t){if(e==null)return{};var n=ro(e,t),i,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function lo(e,t){return co(e)||po(e,t)||uo(e,t)||mo()}function co(e){if(Array.isArray(e))return e}function po(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],i=!0,o=!1,r=void 0;try{for(var l=e[Symbol.iterator](),p;!(i=(p=l.next()).done)&&(n.push(p.value),!(t&&n.length===t));i=!0);}catch(d){o=!0,r=d}finally{try{!i&&l.return!=null&&l.return()}finally{if(o)throw r}}return n}}function uo(e,t){if(e){if(typeof e=="string")return bn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return bn(e,t)}}function bn(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function mo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,i)}return n}function yn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?wn(Object(n),!0).forEach(function(i){fo(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wn(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function xo(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(i){return t.reduceRight(function(o,r){return r(o)},i)}}function Le(e){return function t(){for(var n=this,i=arguments.length,o=new Array(i),r=0;r<i;r++)o[r]=arguments[r];return o.length>=e.length?e.apply(this,o):function(){for(var l=arguments.length,p=new Array(l),d=0;d<l;d++)p[d]=arguments[d];return t.apply(n,[].concat(o,p))}}}function ot(e){return{}.toString.call(e).includes("Object")}function go(e){return!Object.keys(e).length}function Ue(e){return typeof e=="function"}function vo(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ho(e,t){return ot(t)||be("changeType"),Object.keys(t).some(function(n){return!vo(e,n)})&&be("changeField"),t}function bo(e){Ue(e)||be("selectorType")}function wo(e){Ue(e)||ot(e)||be("handlerType"),ot(e)&&Object.values(e).some(function(t){return!Ue(t)})&&be("handlersType")}function yo(e){e||be("initialIsRequired"),ot(e)||be("initialType"),go(e)&&be("initialContent")}function jo(e,t){throw new Error(e[t]||e.default)}var ko={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},be=Le(jo)(ko),Je={changes:ho,selector:bo,handler:wo,initial:yo};function Do(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Je.initial(e),Je.handler(t);var n={current:e},i=Le(Eo)(n,t),o=Le(Co)(n),r=Le(Je.changes)(e),l=Le(So)(n);function p(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(u){return u};return Je.selector(c),c(n.current)}function d(c){xo(i,o,r,l)(c)}return[p,d]}function So(e,t){return Ue(t)?t(e.current):t}function Co(e,t){return e.current=yn(yn({},e.current),t),t}function Eo(e,t,n){return Ue(t)?t(e.current):Object.keys(n).forEach(function(i){var o;return(o=t[i])===null||o===void 0?void 0:o.call(t,e.current[i])}),n}var No={create:Do},Po={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};function Ao(e){return function t(){for(var n=this,i=arguments.length,o=new Array(i),r=0;r<i;r++)o[r]=arguments[r];return o.length>=e.length?e.apply(this,o):function(){for(var l=arguments.length,p=new Array(l),d=0;d<l;d++)p[d]=arguments[d];return t.apply(n,[].concat(o,p))}}}function zo(e){return{}.toString.call(e).includes("Object")}function To(e){return e||jn("configIsRequired"),zo(e)||jn("configType"),e.urls?(Mo(),{paths:{vs:e.urls.monacoBase}}):e}function Mo(){console.warn($n.deprecation)}function Ro(e,t){throw new Error(e[t]||e.default)}var $n={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:`Deprecation warning!
    You are using deprecated way of configuration.

    Instead of using
      monaco.config({ urls: { monacoBase: '...' } })
    use
      monaco.config({ paths: { vs: '...' } })

    For more please check the link https://github.com/suren-atoyan/monaco-loader#config
  `},jn=Ao(Ro)($n),Oo={config:To},_o=function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return function(o){return n.reduceRight(function(r,l){return l(r)},o)}};function qn(e,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&e[n]&&Object.assign(t[n],qn(e[n],t[n]))}),hn(hn({},e),t)}var Lo={type:"cancelation",msg:"operation is manually canceled"};function Nt(e){var t=!1,n=new Promise(function(i,o){e.then(function(r){return t?o(Lo):i(r)}),e.catch(o)});return n.cancel=function(){return t=!0},n}var Io=No.create({config:Po,isInitialized:!1,resolve:null,reject:null,monaco:null}),Un=lo(Io,2),Be=Un[0],pt=Un[1];function Fo(e){var t=Oo.config(e),n=t.monaco,i=so(t,["monaco"]);pt(function(o){return{config:qn(o.config,i),monaco:n}})}function $o(){var e=Be(function(t){var n=t.monaco,i=t.isInitialized,o=t.resolve;return{monaco:n,isInitialized:i,resolve:o}});if(!e.isInitialized){if(pt({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Nt(Pt);if(window.monaco&&window.monaco.editor)return Hn(window.monaco),e.resolve(window.monaco),Nt(Pt);_o(qo,Ho)(Vo)}return Nt(Pt)}function qo(e){return document.body.appendChild(e)}function Uo(e){var t=document.createElement("script");return e&&(t.src=e),t}function Ho(e){var t=Be(function(i){var o=i.config,r=i.reject;return{config:o,reject:r}}),n=Uo("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function Vo(){var e=Be(function(n){var i=n.config,o=n.resolve,r=n.reject;return{config:i,resolve:o,reject:r}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(n){Hn(n),e.resolve(n)},function(n){e.reject(n)})}function Hn(e){Be().monaco||pt({monaco:e})}function Bo(){return Be(function(e){var t=e.monaco;return t})}var Pt=new Promise(function(e,t){return pt({resolve:e,reject:t})}),Vn={config:Fo,init:$o,__getMonacoInstance:Bo},Wo={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},At=Wo,Ko={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},Yo=Ko;function Go({children:e}){return Ce.createElement("div",{style:Yo.container},e)}var Xo=Go,Zo=Xo;function Jo({width:e,height:t,isEditorReady:n,loading:i,_ref:o,className:r,wrapperProps:l}){return Ce.createElement("section",{style:{...At.wrapper,width:e,height:t},...l},!n&&Ce.createElement(Zo,null,i),Ce.createElement("div",{ref:o,style:{...At.fullWidth,...!n&&At.hide},className:r}))}var Qo=Jo,Bn=s.memo(Qo);function er(e){s.useEffect(e,[])}var Wn=er;function tr(e,t,n=!0){let i=s.useRef(!0);s.useEffect(i.current||!n?()=>{i.current=!1}:e,t)}var me=tr;function Fe(){}function ze(e,t,n,i){return nr(e,i)||ar(e,t,n,i)}function nr(e,t){return e.editor.getModel(Kn(e,t))}function ar(e,t,n,i){return e.editor.createModel(t,n,i?Kn(e,i):void 0)}function Kn(e,t){return e.Uri.parse(t)}function ir({original:e,modified:t,language:n,originalLanguage:i,modifiedLanguage:o,originalModelPath:r,modifiedModelPath:l,keepCurrentOriginalModel:p=!1,keepCurrentModifiedModel:d=!1,theme:c="light",loading:u="Loading...",options:m={},height:f="100%",width:v="100%",className:w,wrapperProps:h={},beforeMount:D=Fe,onMount:L=Fe}){let[S,T]=s.useState(!1),[F,M]=s.useState(!0),O=s.useRef(null),I=s.useRef(null),X=s.useRef(null),A=s.useRef(L),P=s.useRef(D),Y=s.useRef(!1);Wn(()=>{let j=Vn.init();return j.then(_=>(I.current=_)&&M(!1)).catch(_=>(_==null?void 0:_.type)!=="cancelation"&&console.error("Monaco initialization: error:",_)),()=>O.current?V():j.cancel()}),me(()=>{if(O.current&&I.current){let j=O.current.getOriginalEditor(),_=ze(I.current,e||"",i||n||"text",r||"");_!==j.getModel()&&j.setModel(_)}},[r],S),me(()=>{if(O.current&&I.current){let j=O.current.getModifiedEditor(),_=ze(I.current,t||"",o||n||"text",l||"");_!==j.getModel()&&j.setModel(_)}},[l],S),me(()=>{let j=O.current.getModifiedEditor();j.getOption(I.current.editor.EditorOption.readOnly)?j.setValue(t||""):t!==j.getValue()&&(j.executeEdits("",[{range:j.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),j.pushUndoStop())},[t],S),me(()=>{var j,_;(_=(j=O.current)==null?void 0:j.getModel())==null||_.original.setValue(e||"")},[e],S),me(()=>{let{original:j,modified:_}=O.current.getModel();I.current.editor.setModelLanguage(j,i||n||"text"),I.current.editor.setModelLanguage(_,o||n||"text")},[n,i,o],S),me(()=>{var j;(j=I.current)==null||j.editor.setTheme(c)},[c],S),me(()=>{var j;(j=O.current)==null||j.updateOptions(m)},[m],S);let se=s.useCallback(()=>{var oe;if(!I.current)return;P.current(I.current);let j=ze(I.current,e||"",i||n||"text",r||""),_=ze(I.current,t||"",o||n||"text",l||"");(oe=O.current)==null||oe.setModel({original:j,modified:_})},[n,t,o,e,i,r,l]),ie=s.useCallback(()=>{var j;!Y.current&&X.current&&(O.current=I.current.editor.createDiffEditor(X.current,{automaticLayout:!0,...m}),se(),(j=I.current)==null||j.editor.setTheme(c),T(!0),Y.current=!0)},[m,c,se]);s.useEffect(()=>{S&&A.current(O.current,I.current)},[S]),s.useEffect(()=>{!F&&!S&&ie()},[F,S,ie]);function V(){var _,oe,le,z;let j=(_=O.current)==null?void 0:_.getModel();p||((oe=j==null?void 0:j.original)==null||oe.dispose()),d||((le=j==null?void 0:j.modified)==null||le.dispose()),(z=O.current)==null||z.dispose()}return Ce.createElement(Bn,{width:v,height:f,isEditorReady:S,loading:u,_ref:X,className:w,wrapperProps:h})}var or=ir;s.memo(or);function rr(e){let t=s.useRef();return s.useEffect(()=>{t.current=e},[e]),t.current}var sr=rr,Qe=new Map;function lr({defaultValue:e,defaultLanguage:t,defaultPath:n,value:i,language:o,path:r,theme:l="light",line:p,loading:d="Loading...",options:c={},overrideServices:u={},saveViewState:m=!0,keepCurrentModel:f=!1,width:v="100%",height:w="100%",className:h,wrapperProps:D={},beforeMount:L=Fe,onMount:S=Fe,onChange:T,onValidate:F=Fe}){let[M,O]=s.useState(!1),[I,X]=s.useState(!0),A=s.useRef(null),P=s.useRef(null),Y=s.useRef(null),se=s.useRef(S),ie=s.useRef(L),V=s.useRef(),j=s.useRef(i),_=sr(r),oe=s.useRef(!1),le=s.useRef(!1);Wn(()=>{let g=Vn.init();return g.then(y=>(A.current=y)&&X(!1)).catch(y=>(y==null?void 0:y.type)!=="cancelation"&&console.error("Monaco initialization: error:",y)),()=>P.current?x():g.cancel()}),me(()=>{var y,R,$,k;let g=ze(A.current,e||i||"",t||o||"",r||n||"");g!==((y=P.current)==null?void 0:y.getModel())&&(m&&Qe.set(_,(R=P.current)==null?void 0:R.saveViewState()),($=P.current)==null||$.setModel(g),m&&((k=P.current)==null||k.restoreViewState(Qe.get(r))))},[r],M),me(()=>{var g;(g=P.current)==null||g.updateOptions(c)},[c],M),me(()=>{!P.current||i===void 0||(P.current.getOption(A.current.editor.EditorOption.readOnly)?P.current.setValue(i):i!==P.current.getValue()&&(le.current=!0,P.current.executeEdits("",[{range:P.current.getModel().getFullModelRange(),text:i,forceMoveMarkers:!0}]),P.current.pushUndoStop(),le.current=!1))},[i],M),me(()=>{var y,R;let g=(y=P.current)==null?void 0:y.getModel();g&&o&&((R=A.current)==null||R.editor.setModelLanguage(g,o))},[o],M),me(()=>{var g;p!==void 0&&((g=P.current)==null||g.revealLine(p))},[p],M),me(()=>{var g;(g=A.current)==null||g.editor.setTheme(l)},[l],M);let z=s.useCallback(()=>{var g;if(!(!Y.current||!A.current)&&!oe.current){ie.current(A.current);let y=r||n,R=ze(A.current,i||e||"",t||o||"",y||"");P.current=(g=A.current)==null?void 0:g.editor.create(Y.current,{model:R,automaticLayout:!0,...c},u),m&&P.current.restoreViewState(Qe.get(y)),A.current.editor.setTheme(l),p!==void 0&&P.current.revealLine(p),O(!0),oe.current=!0}},[e,t,n,i,o,r,c,u,m,l,p]);s.useEffect(()=>{M&&se.current(P.current,A.current)},[M]),s.useEffect(()=>{!I&&!M&&z()},[I,M,z]),j.current=i,s.useEffect(()=>{var g,y;M&&T&&((g=V.current)==null||g.dispose(),V.current=(y=P.current)==null?void 0:y.onDidChangeModelContent(R=>{le.current||T(P.current.getValue(),R)}))},[M,T]),s.useEffect(()=>{if(M){let g=A.current.editor.onDidChangeMarkers(y=>{var $;let R=($=P.current.getModel())==null?void 0:$.uri;if(R&&y.find(k=>k.path===R.path)){let k=A.current.editor.getModelMarkers({resource:R});F==null||F(k)}});return()=>{g==null||g.dispose()}}return()=>{}},[M,F]);function x(){var g,y;(g=V.current)==null||g.dispose(),f?m&&Qe.set(r,P.current.saveViewState()):(y=P.current.getModel())==null||y.dispose(),P.current.dispose()}return Ce.createElement(Bn,{width:v,height:w,isEditorReady:M,loading:d,_ref:Y,className:h,wrapperProps:D})}var cr=lr,pr=s.memo(cr),dr=pr;const ur={python:"python",javascript:"javascript",typescript:"typescript",r:"r",sql:"sql",java:"java",c:"cpp","c++":"cpp",cpp:"cpp","c#":"csharp",csharp:"csharp",json:"json",markdown:"markdown",css:"css",html:"html",xml:"xml",yaml:"yaml",plaintext:"plaintext"},Yn=s.forwardRef(({value:e,onChange:t,language:n,height:i="400px",placeholder:o="",readOnly:r=!1,theme:l="vs-dark",errors:p=[]},d)=>{const c=s.useRef(null),u=s.useRef(null),m=s.useRef([]),f=w=>ur[w==null?void 0:w.toLowerCase()]||"plaintext",v=(w,h)=>{c.current=w,u.current=h};return s.useImperativeHandle(d,()=>({focus:()=>{var w;(w=c.current)==null||w.focus()},setCursor:w=>{var h;(h=c.current)==null||h.setPosition(w)},revealLineInCenter:w=>{var h,D;(D=c.current)==null||D.revealLineInCenter(w,(h=u.current)==null?void 0:h.editor.ScrollType.Smooth)},applyErrorMarkers:w=>{if(c.current&&u.current){const h=u.current,D=c.current,L=D.getModel();if(!L)return;const S=w.map(T=>{const F=L.getLineContent(T.line),M=L.getLineMaxColumn(T.line),O=Math.min(Math.max(1,T.column||1),M),I=F.includes("	")||(T.column||1)>=M-1?M:O+1;return{range:new h.Range(T.line,O,T.line,I),options:{className:T.severity==="warning"?"warning-line-highlight":"error-line-highlight",glyphMarginClassName:T.severity==="warning"?"warning-glyph-margin":"error-glyph-margin",hoverMessage:{value:T.message},overviewRuler:{color:T.severity==="warning"?"rgba(255, 220, 0, 0.7)":"rgba(255, 0, 0, 0.7)",position:h.editor.OverviewRulerLane.Full}}}});m.current=D.deltaDecorations(m.current,S)}},clearErrorMarkers:()=>{c.current&&(m.current=c.current.deltaDecorations(m.current,[]))},getValue:()=>{var w;return(w=c.current)==null?void 0:w.getValue()}})),s.useEffect(()=>{if(c.current&&u.current){const w=u.current,h=c.current,D=h.getModel();if(!D)return;const L=(p||[]).filter(S=>{const T=D.getLineCount(),F=Number.isInteger(S.line)&&S.line>=1&&S.line<=T;return F||console.warn(`MonacoEditor: Skipping error decoration due to invalid line number: ${S.line} (model lines: ${T})`,S),F}).map(S=>{const T=D.getLineMaxColumn(S.line);return{range:new w.Range(S.line,1,S.line,T),options:{isWholeLine:!0,className:S.severity==="warning"?"warning-line-highlight":"error-line-highlight",glyphMarginClassName:S.severity==="warning"?"warning-glyph-margin":"error-glyph-margin",hoverMessage:{value:S.message},stickiness:w.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,overviewRuler:{color:S.severity==="warning"?"rgba(255, 220, 0, 0.7)":"rgba(255, 0, 0, 0.7)",position:w.editor.OverviewRulerLane.Full}}}});m.current=h.deltaDecorations(m.current,L)}},[p,e,r]),a.jsx(dr,{height:i,language:f(n),value:e,onChange:t,onMount:v,theme:l,options:{readOnly:r,selectOnLineNumbers:!0,automaticLayout:!0,scrollBeyondLastLine:!1,lineNumbers:"on",glyphMargin:!0,minimap:{enabled:!0},wordWrap:"on"}})});Yn.displayName="MonacoEditor";const mr=Ra("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),qt=s.forwardRef(({className:e,variant:t,...n},i)=>a.jsx("div",{ref:i,role:"alert",className:Ne(mr({variant:t}),e),...n}));qt.displayName="Alert";const Ut=s.forwardRef(({className:e,...t},n)=>a.jsx("h5",{ref:n,className:Ne("mb-1 font-medium leading-none tracking-tight",e),...t}));Ut.displayName="AlertTitle";const Ht=s.forwardRef(({className:e,...t},n)=>a.jsx("div",{ref:n,className:Ne("text-sm [&_p]:leading-relaxed",e),...t}));Ht.displayName="AlertDescription";var Vt="Progress",Bt=100,[fr,us]=Ft(Vt),[xr,gr]=fr(Vt),Gn=s.forwardRef((e,t)=>{const{__scopeProgress:n,value:i=null,max:o,getValueLabel:r=vr,...l}=e;(o||o===0)&&!kn(o)&&console.error(hr(`${o}`,"Progress"));const p=kn(o)?o:Bt;i!==null&&!Dn(i,p)&&console.error(br(`${i}`,"Progress"));const d=Dn(i,p)?i:null,c=rt(d)?r(d,p):void 0;return a.jsx(xr,{scope:n,value:d,max:p,children:a.jsx(we.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":rt(d)?d:void 0,"aria-valuetext":c,role:"progressbar","data-state":Jn(d,p),"data-value":d??void 0,"data-max":p,...l,ref:t})})});Gn.displayName=Vt;var Xn="ProgressIndicator",Zn=s.forwardRef((e,t)=>{const{__scopeProgress:n,...i}=e,o=gr(Xn,n);return a.jsx(we.div,{"data-state":Jn(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...i,ref:t})});Zn.displayName=Xn;function vr(e,t){return`${Math.round(e/t*100)}%`}function Jn(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function rt(e){return typeof e=="number"}function kn(e){return rt(e)&&!isNaN(e)&&e>0}function Dn(e,t){return rt(e)&&!isNaN(e)&&e<=t&&e>=0}function hr(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Bt}\`.`}function br(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Bt} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Qn=Gn,wr=Zn;const ea=s.forwardRef(({className:e,value:t,...n},i)=>a.jsx(Qn,{ref:i,className:Ne("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...n,children:a.jsx(wr,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));ea.displayName=Qn.displayName;var[dt,ms]=Ft("Tooltip",[Cn]),ut=Cn(),ta="TooltipProvider",yr=700,Lt="tooltip.open",[jr,Wt]=dt(ta),na=e=>{const{__scopeTooltip:t,delayDuration:n=yr,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:r}=e,l=s.useRef(!0),p=s.useRef(!1),d=s.useRef(0);return s.useEffect(()=>{const c=d.current;return()=>window.clearTimeout(c)},[]),a.jsx(jr,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:s.useCallback(()=>{window.clearTimeout(d.current),l.current=!1},[]),onClose:s.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.current=!0,i)},[i]),isPointerInTransitRef:p,onPointerInTransitChange:s.useCallback(c=>{p.current=c},[]),disableHoverableContent:o,children:r})};na.displayName=ta;var He="Tooltip",[kr,mt]=dt(He),aa=e=>{const{__scopeTooltip:t,children:n,open:i,defaultOpen:o,onOpenChange:r,disableHoverableContent:l,delayDuration:p}=e,d=Wt(He,e.__scopeTooltip),c=ut(t),[u,m]=s.useState(null),f=Va(),v=s.useRef(0),w=l??d.disableHoverableContent,h=p??d.delayDuration,D=s.useRef(!1),[L,S]=Ba({prop:i,defaultProp:o??!1,onChange:I=>{I?(d.onOpen(),document.dispatchEvent(new CustomEvent(Lt))):d.onClose(),r==null||r(I)},caller:He}),T=s.useMemo(()=>L?D.current?"delayed-open":"instant-open":"closed",[L]),F=s.useCallback(()=>{window.clearTimeout(v.current),v.current=0,D.current=!1,S(!0)},[S]),M=s.useCallback(()=>{window.clearTimeout(v.current),v.current=0,S(!1)},[S]),O=s.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{D.current=!0,S(!0),v.current=0},h)},[h,S]);return s.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),a.jsx(Wa,{...c,children:a.jsx(kr,{scope:t,contentId:f,open:L,stateAttribute:T,trigger:u,onTriggerChange:m,onTriggerEnter:s.useCallback(()=>{d.isOpenDelayedRef.current?O():F()},[d.isOpenDelayedRef,O,F]),onTriggerLeave:s.useCallback(()=>{w?M():(window.clearTimeout(v.current),v.current=0)},[M,w]),onOpen:F,onClose:M,disableHoverableContent:w,children:n})})};aa.displayName=He;var It="TooltipTrigger",ia=s.forwardRef((e,t)=>{const{__scopeTooltip:n,...i}=e,o=mt(It,n),r=Wt(It,n),l=ut(n),p=s.useRef(null),d=ye(t,p,o.onTriggerChange),c=s.useRef(!1),u=s.useRef(!1),m=s.useCallback(()=>c.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),a.jsx($a,{asChild:!0,...l,children:a.jsx(we.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...i,ref:d,onPointerMove:fe(e.onPointerMove,f=>{f.pointerType!=="touch"&&!u.current&&!r.isPointerInTransitRef.current&&(o.onTriggerEnter(),u.current=!0)}),onPointerLeave:fe(e.onPointerLeave,()=>{o.onTriggerLeave(),u.current=!1}),onPointerDown:fe(e.onPointerDown,()=>{o.open&&o.onClose(),c.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:fe(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:fe(e.onBlur,o.onClose),onClick:fe(e.onClick,o.onClose)})})});ia.displayName=It;var Dr="TooltipPortal",[fs,Sr]=dt(Dr,{forceMount:void 0}),Me="TooltipContent",oa=s.forwardRef((e,t)=>{const n=Sr(Me,e.__scopeTooltip),{forceMount:i=n.forceMount,side:o="top",...r}=e,l=mt(Me,e.__scopeTooltip);return a.jsx(Ve,{present:i||l.open,children:l.disableHoverableContent?a.jsx(ra,{side:o,...r,ref:t}):a.jsx(Cr,{side:o,...r,ref:t})})}),Cr=s.forwardRef((e,t)=>{const n=mt(Me,e.__scopeTooltip),i=Wt(Me,e.__scopeTooltip),o=s.useRef(null),r=ye(t,o),[l,p]=s.useState(null),{trigger:d,onClose:c}=n,u=o.current,{onPointerInTransitChange:m}=i,f=s.useCallback(()=>{p(null),m(!1)},[m]),v=s.useCallback((w,h)=>{const D=w.currentTarget,L={x:w.clientX,y:w.clientY},S=zr(L,D.getBoundingClientRect()),T=Tr(L,S),F=Mr(h.getBoundingClientRect()),M=Or([...T,...F]);p(M),m(!0)},[m]);return s.useEffect(()=>()=>f(),[f]),s.useEffect(()=>{if(d&&u){const w=D=>v(D,u),h=D=>v(D,d);return d.addEventListener("pointerleave",w),u.addEventListener("pointerleave",h),()=>{d.removeEventListener("pointerleave",w),u.removeEventListener("pointerleave",h)}}},[d,u,v,f]),s.useEffect(()=>{if(l){const w=h=>{const D=h.target,L={x:h.clientX,y:h.clientY},S=(d==null?void 0:d.contains(D))||(u==null?void 0:u.contains(D)),T=!Rr(L,l);S?f():T&&(f(),c())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[d,u,l,c,f]),a.jsx(ra,{...e,ref:r})}),[Er,Nr]=dt(He,{isInside:!1}),Pr=Oa("TooltipContent"),ra=s.forwardRef((e,t)=>{const{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:r,onPointerDownOutside:l,...p}=e,d=mt(Me,n),c=ut(n),{onClose:u}=d;return s.useEffect(()=>(document.addEventListener(Lt,u),()=>document.removeEventListener(Lt,u)),[u]),s.useEffect(()=>{if(d.trigger){const m=f=>{const v=f.target;v!=null&&v.contains(d.trigger)&&u()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[d.trigger,u]),a.jsx(qa,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:r,onPointerDownOutside:l,onFocusOutside:m=>m.preventDefault(),onDismiss:u,children:a.jsxs(Ua,{"data-state":d.stateAttribute,...c,...p,ref:t,style:{...p.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[a.jsx(Pr,{children:i}),a.jsx(Er,{scope:n,isInside:!0,children:a.jsx(Ga,{id:d.contentId,role:"tooltip",children:o||i})})]})})});oa.displayName=Me;var sa="TooltipArrow",Ar=s.forwardRef((e,t)=>{const{__scopeTooltip:n,...i}=e,o=ut(n);return Nr(sa,n).isInside?null:a.jsx(Ha,{...o,...i,ref:t})});Ar.displayName=sa;function zr(e,t){const n=Math.abs(t.top-e.y),i=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),r=Math.abs(t.left-e.x);switch(Math.min(n,i,o,r)){case r:return"left";case o:return"right";case n:return"top";case i:return"bottom";default:throw new Error("unreachable")}}function Tr(e,t,n=5){const i=[];switch(t){case"top":i.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":i.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":i.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":i.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return i}function Mr(e){const{top:t,right:n,bottom:i,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:i},{x:o,y:i}]}function Rr(e,t){const{x:n,y:i}=e;let o=!1;for(let r=0,l=t.length-1;r<t.length;l=r++){const p=t[r],d=t[l],c=p.x,u=p.y,m=d.x,f=d.y;u>i!=f>i&&n<(m-c)*(i-u)/(f-u)+c&&(o=!o)}return o}function Or(e){const t=e.slice();return t.sort((n,i)=>n.x<i.x?-1:n.x>i.x?1:n.y<i.y?-1:n.y>i.y?1:0),_r(t)}function _r(e){if(e.length<=1)return e.slice();const t=[];for(let i=0;i<e.length;i++){const o=e[i];for(;t.length>=2;){const r=t[t.length-1],l=t[t.length-2];if((r.x-l.x)*(o.y-l.y)>=(r.y-l.y)*(o.x-l.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let i=e.length-1;i>=0;i--){const o=e[i];for(;n.length>=2;){const r=n[n.length-1],l=n[n.length-2];if((r.x-l.x)*(o.y-l.y)>=(r.y-l.y)*(o.x-l.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Lr=na,Ir=aa,Fr=ia,la=oa;const ca=Lr,pa=Ir,da=Fr,Kt=s.forwardRef(({className:e,sideOffset:t=4,...n},i)=>a.jsx(la,{ref:i,sideOffset:t,className:Ne("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));Kt.displayName=la.displayName;function $r({fileSize:e,onDismiss:t,isGisCode:n}){const[i,o]=s.useState(!0);if(s.useEffect(()=>{const h=setTimeout(()=>{o(!1),t&&t()},1e4);return()=>clearTimeout(h)},[t]),!i)return null;const r=h=>h<1024?`${h} bytes`:h<1024*1024?`${(h/1024).toFixed(1)} KB`:`${(h/(1024*1024)).toFixed(2)} MB`,l=h=>h<100*1024?"info":h<1024*1024?"warning":"critical",p=(h,D=!1)=>h<50*1024?"less than 1 second":h<200*1024?`${D?"1-3":"1-2"} seconds`:h<500*1024?`${D?"3-5":"2-3"} seconds`:h<1024*1024?`${D?"5-8":"3-5"} seconds`:h<2*1024*1024?`${D?"8-15":"5-10"} seconds`:`${D?"15+":"10+"} seconds`,d=h=>Math.min(Math.round(h/(n?3145728:5242880)*100),100),c=l(e),u=r(e),m=d(e),f=p(e,n),[v,w]=s.useState(0);return s.useEffect(()=>{const h=setInterval(()=>{w(D=>D>=100?(clearInterval(h),100):D+1)},100);return()=>clearInterval(h)},[]),a.jsxs(qt,{className:`
        mb-4 border-l-4 transition-colors relative overflow-hidden backdrop-blur-[2px]
        ${c==="info"?"border-l-blue-500 bg-blue-500/10 dark:bg-blue-950/20":""}
        ${c==="warning"?"border-l-amber-500 bg-amber-500/10 dark:bg-amber-950/20":""}
        ${c==="critical"?"border-l-red-500 bg-red-500/10 dark:bg-red-950/20":""}
      `,style:{animation:"fadeIn 0.3s ease-out forwards",boxShadow:c==="critical"?"0 4px 12px rgba(239, 68, 68, 0.1)":"none"},children:[a.jsx("div",{className:"absolute inset-0 overflow-hidden opacity-5 pointer-events-none z-0",children:a.jsx("div",{className:"h-full w-full",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='49' viewBox='0 0 28 49'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='hexagons' fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M13.99 9.25l13 7.5v15l-13 7.5L1 31.75v-15l12.99-7.5zM3 17.9v12.7l10.99 6.34 11-6.35V17.9l-11-6.34L3 17.9zM0 15l12.98-7.5V0h-2v6.35L0 12.69v2.3zm0 18.5L12.98 41v8h-2v-6.85L0 35.81v-2.3zM15 0v7.5L27.99 15H28v-2.31h-.01L17 6.35V0h-2zm0 49v-8l12.99-7.5H28v2.31h-.01L17 42.15V49h-2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}})}),c==="critical"&&a.jsx("div",{className:"absolute top-0 right-0 h-full w-16 bg-red-500/5 skew-x-[-15deg] -mr-5 pointer-events-none z-0"}),n&&a.jsx("div",{className:"absolute top-0 left-12 h-full w-12 bg-primary/5 skew-x-[30deg] -ml-5 pointer-events-none z-0"}),a.jsxs("div",{className:"flex justify-between items-start w-full",children:[a.jsxs("div",{className:"flex gap-2",children:[n?a.jsxs("div",{className:"relative",children:[a.jsx(ri,{size:18,className:`
              ${c==="info"?"text-blue-500":""}
              ${c==="warning"?"text-amber-500":""}
              ${c==="critical"?"text-red-500":""}
            `}),a.jsx("div",{className:"absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-card flex items-center justify-center",children:a.jsx(ii,{size:8,className:"text-primary"})})]}):a.jsxs(a.Fragment,{children:[c==="info"&&a.jsx(et,{size:18,className:"text-blue-500"}),c==="warning"&&a.jsx(Qa,{size:18,className:"text-amber-500"}),c==="critical"&&a.jsx(ni,{size:18,className:"text-red-500"})]}),a.jsxs("div",{className:"flex-1",children:[a.jsxs(Ut,{className:"text-sm font-mono tracking-wide flex items-center gap-2",children:[n?"GIS ":"",c==="info"&&"File Size Info",c==="warning"&&"Large File Warning",c==="critical"&&"Very Large File Warning",a.jsx("span",{className:"font-mono text-xs px-1.5 py-0.5 rounded-sm bg-muted/30 border border-muted/20 backdrop-blur-sm",children:u})]}),a.jsxs(Ht,{className:"text-xs mt-1 space-y-2",children:[a.jsxs("div",{children:[c==="info"&&a.jsx(a.Fragment,{children:n?"Small GIS file, validation and spatial analysis will be quick.":"Small file, processing will be quick."}),c==="warning"&&a.jsx(a.Fragment,{children:n?"Medium-sized GIS file, spatial analysis may take longer than standard code.":"Medium-sized file, processing may take a moment."}),c==="critical"&&a.jsx(a.Fragment,{children:n?"Large GIS file, spatial operations and validation may be resource-intensive. Consider isolating core workflow logic.":"Large file, this may affect performance. Consider splitting into smaller files for better results."})]}),a.jsxs("div",{className:"space-y-1",children:[a.jsxs("div",{className:"flex items-center justify-between text-[10px] text-muted-foreground",children:[a.jsx("span",{children:"File size"}),a.jsx(ca,{children:a.jsxs(pa,{children:[a.jsx(da,{asChild:!0,children:a.jsxs("div",{className:"flex items-center gap-1 cursor-help",children:[a.jsxs("span",{className:"whitespace-nowrap",children:["Est. processing time: ",f]}),n&&a.jsx(En,{size:10,className:"text-primary/70"})]})}),a.jsx(Kt,{side:"bottom",className:"max-w-[240px]",children:a.jsx("p",{className:"text-xs",children:n?"Estimated time to validate and analyze this GIS code. Spatial operations typically require more processing time.":"Estimated time to validate and analyze this code file"})})]})})]}),a.jsx(ea,{value:m,className:`h-1.5 ${m>80?"bg-muted/30":"bg-muted/20"}`,style:{clipPath:"polygon(0 0, 100% 0, 95% 100%, 5% 100%)"}}),a.jsxs("div",{className:"flex justify-between text-[10px] text-muted-foreground",children:[a.jsxs("span",{className:"relative",children:[a.jsx("span",{children:"0 KB"}),n&&a.jsx("span",{className:"absolute -left-2 top-[4px] h-[1px] w-2 bg-primary/30 rotate-45"})]}),a.jsx("span",{className:"relative px-1 bg-muted/10 -mx-1 backdrop-blur-sm rounded-sm",children:n?"Optimal for GIS (&lt;3 MB)":"Optimal (&lt;5 MB)"}),a.jsxs("span",{className:"relative",children:[n?"3 MB+":"5 MB+",c==="critical"&&a.jsx("span",{className:"absolute -right-2 top-[4px] h-[1px] w-2 bg-red-500/30 rotate-45"})]})]})]}),c==="critical"&&a.jsx("div",{className:"text-[10px] border-l-2 border-red-500/30 pl-2 italic",children:n?a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"font-semibold",children:"GIS Performance Guidance:"})," Large spatial analysis code may affect browser performance. Consider isolating the core workflow components like parameter definition and main spatial functions. Complex models with many spatial operations benefit from focused documentation."]}):a.jsx(a.Fragment,{children:"Very large files may cause browser performance issues during validation and analysis. For scripts over 3MB, consider focusing on core functionality sections."})})]})]})]}),a.jsx(ue,{variant:"ghost",size:"icon",className:"h-5 w-5 rounded-full",onClick:()=>{o(!1),t&&t()},children:a.jsx(pi,{size:12})})]}),a.jsx("style",{jsx:"true",children:`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        /* Brutalist angle decorations */
        .slashed-bg::before {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          width: 20%;
          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.05));
          clip-path: polygon(0 0, 100% 0, 100% 100%, 20% 100%);
        }
      `})]})}function st({fileExtension:e,isGisCode:t}){if(!e)return null;const n=e.toLowerCase(),i={python:[".py",".pyw",".pyc",".pyd",".pyo",".pyi"],javascript:[".js",".jsx",".mjs",".cjs"],typescript:[".ts",".tsx",".mts",".cts"],r:[".r",".rmd",".rds"],sql:[".sql"],java:[".java",".class",".jar"],csharp:[".cs",".csx"],cpp:[".cpp",".cc",".cxx",".c++",".h",".hpp",".hxx",".h++"],c:[".c",".h"],other:[".txt",".md",".json",".xml",".yaml",".yml"]};let o="unknown";for(const[d,c]of Object.entries(i))if(c.includes(n)){o=d;break}const r={python:"bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/30",javascript:"bg-yellow-500/10 text-yellow-700 dark:text-yellow-500 border-yellow-500/30",typescript:"bg-blue-400/10 text-blue-700 dark:text-blue-400 border-blue-400/30",r:"bg-purple-500/10 text-purple-700 dark:text-purple-400 border-purple-500/30",sql:"bg-orange-500/10 text-orange-700 dark:text-orange-400 border-orange-500/30",java:"bg-red-500/10 text-red-700 dark:text-red-400 border-red-500/30",csharp:"bg-green-500/10 text-green-700 dark:text-green-400 border-green-500/30",cpp:"bg-pink-500/10 text-pink-700 dark:text-pink-400 border-pink-500/30",c:"bg-blue-300/10 text-blue-800 dark:text-blue-300 border-blue-300/30",other:"bg-gray-500/10 text-gray-700 dark:text-gray-400 border-gray-500/30",unknown:"bg-gray-500/10 text-gray-700 dark:text-gray-400 border-gray-500/30"},l=(d,c)=>{if(c)return a.jsx(ai,{size:12,className:"mr-1"});switch(d){case"python":return a.jsx(li,{size:12,className:"mr-1"});case"javascript":case"typescript":return a.jsx($e,{size:12,className:"mr-1"});case"sql":return a.jsx(ei,{size:12,className:"mr-1"});case"r":return a.jsx(Ja,{size:12,className:"mr-1"});case"java":case"csharp":case"cpp":case"c":return a.jsx(oi,{size:12,className:"mr-1"});default:return a.jsx(_a,{size:12,className:"mr-1"})}},p={python:"Python",javascript:"JavaScript",typescript:"TypeScript",r:"R",sql:"SQL",java:"Java",csharp:"C#",cpp:"C++",c:"C",other:n.slice(1).toUpperCase(),unknown:n.slice(1).toUpperCase()}[o];return a.jsxs("span",{className:`inline-flex items-center ${r[o]} font-mono text-[10px] py-0 transition-all hover:shadow-sm rounded-full border px-2.5`,style:{clipPath:"polygon(5% 0%, 95% 0%, 100% 50%, 95% 100%, 5% 100%, 0% 50%)",paddingLeft:"0.625rem",paddingRight:"0.625rem"},children:[l(o,t),t?`GIS ${p}`:p]})}function qr(e,t){return s.useReducer((n,i)=>t[n][i]??n,e)}var Yt="ScrollArea",[ua,xs]=Ft(Yt),[Ur,ge]=ua(Yt),ma=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:i="hover",dir:o,scrollHideDelay:r=600,...l}=e,[p,d]=s.useState(null),[c,u]=s.useState(null),[m,f]=s.useState(null),[v,w]=s.useState(null),[h,D]=s.useState(null),[L,S]=s.useState(0),[T,F]=s.useState(0),[M,O]=s.useState(!1),[I,X]=s.useState(!1),A=ye(t,Y=>d(Y)),P=Ka(o);return a.jsx(Ur,{scope:n,type:i,dir:P,scrollHideDelay:r,scrollArea:p,viewport:c,onViewportChange:u,content:m,onContentChange:f,scrollbarX:v,onScrollbarXChange:w,scrollbarXEnabled:M,onScrollbarXEnabledChange:O,scrollbarY:h,onScrollbarYChange:D,scrollbarYEnabled:I,onScrollbarYEnabledChange:X,onCornerWidthChange:S,onCornerHeightChange:F,children:a.jsx(we.div,{dir:P,...l,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":L+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});ma.displayName=Yt;var fa="ScrollAreaViewport",xa=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:i,nonce:o,...r}=e,l=ge(fa,n),p=s.useRef(null),d=ye(t,p,l.onViewportChange);return a.jsxs(a.Fragment,{children:[a.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),a.jsx(we.div,{"data-radix-scroll-area-viewport":"",...r,ref:d,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:a.jsx("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});xa.displayName=fa;var he="ScrollAreaScrollbar",Gt=s.forwardRef((e,t)=>{const{forceMount:n,...i}=e,o=ge(he,e.__scopeScrollArea),{onScrollbarXEnabledChange:r,onScrollbarYEnabledChange:l}=o,p=e.orientation==="horizontal";return s.useEffect(()=>(p?r(!0):l(!0),()=>{p?r(!1):l(!1)}),[p,r,l]),o.type==="hover"?a.jsx(Hr,{...i,ref:t,forceMount:n}):o.type==="scroll"?a.jsx(Vr,{...i,ref:t,forceMount:n}):o.type==="auto"?a.jsx(ga,{...i,ref:t,forceMount:n}):o.type==="always"?a.jsx(Xt,{...i,ref:t}):null});Gt.displayName=he;var Hr=s.forwardRef((e,t)=>{const{forceMount:n,...i}=e,o=ge(he,e.__scopeScrollArea),[r,l]=s.useState(!1);return s.useEffect(()=>{const p=o.scrollArea;let d=0;if(p){const c=()=>{window.clearTimeout(d),l(!0)},u=()=>{d=window.setTimeout(()=>l(!1),o.scrollHideDelay)};return p.addEventListener("pointerenter",c),p.addEventListener("pointerleave",u),()=>{window.clearTimeout(d),p.removeEventListener("pointerenter",c),p.removeEventListener("pointerleave",u)}}},[o.scrollArea,o.scrollHideDelay]),a.jsx(Ve,{present:n||r,children:a.jsx(ga,{"data-state":r?"visible":"hidden",...i,ref:t})})}),Vr=s.forwardRef((e,t)=>{const{forceMount:n,...i}=e,o=ge(he,e.__scopeScrollArea),r=e.orientation==="horizontal",l=xt(()=>d("SCROLL_END"),100),[p,d]=qr("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return s.useEffect(()=>{if(p==="idle"){const c=window.setTimeout(()=>d("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(c)}},[p,o.scrollHideDelay,d]),s.useEffect(()=>{const c=o.viewport,u=r?"scrollLeft":"scrollTop";if(c){let m=c[u];const f=()=>{const v=c[u];m!==v&&(d("SCROLL"),l()),m=v};return c.addEventListener("scroll",f),()=>c.removeEventListener("scroll",f)}},[o.viewport,r,d,l]),a.jsx(Ve,{present:n||p!=="hidden",children:a.jsx(Xt,{"data-state":p==="hidden"?"hidden":"visible",...i,ref:t,onPointerEnter:fe(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:fe(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),ga=s.forwardRef((e,t)=>{const n=ge(he,e.__scopeScrollArea),{forceMount:i,...o}=e,[r,l]=s.useState(!1),p=e.orientation==="horizontal",d=xt(()=>{if(n.viewport){const c=n.viewport.offsetWidth<n.viewport.scrollWidth,u=n.viewport.offsetHeight<n.viewport.scrollHeight;l(p?c:u)}},10);return Re(n.viewport,d),Re(n.content,d),a.jsx(Ve,{present:i||r,children:a.jsx(Xt,{"data-state":r?"visible":"hidden",...o,ref:t})})}),Xt=s.forwardRef((e,t)=>{const{orientation:n="vertical",...i}=e,o=ge(he,e.__scopeScrollArea),r=s.useRef(null),l=s.useRef(0),[p,d]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=ya(p.viewport,p.content),u={...i,sizes:p,onSizesChange:d,hasThumb:c>0&&c<1,onThumbChange:f=>r.current=f,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:f=>l.current=f};function m(f,v){return Xr(f,l.current,p,v)}return n==="horizontal"?a.jsx(Br,{...u,ref:t,onThumbPositionChange:()=>{if(o.viewport&&r.current){const f=o.viewport.scrollLeft,v=Sn(f,p,o.dir);r.current.style.transform=`translate3d(${v}px, 0, 0)`}},onWheelScroll:f=>{o.viewport&&(o.viewport.scrollLeft=f)},onDragScroll:f=>{o.viewport&&(o.viewport.scrollLeft=m(f,o.dir))}}):n==="vertical"?a.jsx(Wr,{...u,ref:t,onThumbPositionChange:()=>{if(o.viewport&&r.current){const f=o.viewport.scrollTop,v=Sn(f,p);r.current.style.transform=`translate3d(0, ${v}px, 0)`}},onWheelScroll:f=>{o.viewport&&(o.viewport.scrollTop=f)},onDragScroll:f=>{o.viewport&&(o.viewport.scrollTop=m(f))}}):null}),Br=s.forwardRef((e,t)=>{const{sizes:n,onSizesChange:i,...o}=e,r=ge(he,e.__scopeScrollArea),[l,p]=s.useState(),d=s.useRef(null),c=ye(t,d,r.onScrollbarXChange);return s.useEffect(()=>{d.current&&p(getComputedStyle(d.current))},[d]),a.jsx(ha,{"data-orientation":"horizontal",...o,ref:c,sizes:n,style:{bottom:0,left:r.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:r.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ft(n)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.x),onDragScroll:u=>e.onDragScroll(u.x),onWheelScroll:(u,m)=>{if(r.viewport){const f=r.viewport.scrollLeft+u.deltaX;e.onWheelScroll(f),ka(f,m)&&u.preventDefault()}},onResize:()=>{d.current&&r.viewport&&l&&i({content:r.viewport.scrollWidth,viewport:r.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:ct(l.paddingLeft),paddingEnd:ct(l.paddingRight)}})}})}),Wr=s.forwardRef((e,t)=>{const{sizes:n,onSizesChange:i,...o}=e,r=ge(he,e.__scopeScrollArea),[l,p]=s.useState(),d=s.useRef(null),c=ye(t,d,r.onScrollbarYChange);return s.useEffect(()=>{d.current&&p(getComputedStyle(d.current))},[d]),a.jsx(ha,{"data-orientation":"vertical",...o,ref:c,sizes:n,style:{top:0,right:r.dir==="ltr"?0:void 0,left:r.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ft(n)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.y),onDragScroll:u=>e.onDragScroll(u.y),onWheelScroll:(u,m)=>{if(r.viewport){const f=r.viewport.scrollTop+u.deltaY;e.onWheelScroll(f),ka(f,m)&&u.preventDefault()}},onResize:()=>{d.current&&r.viewport&&l&&i({content:r.viewport.scrollHeight,viewport:r.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:ct(l.paddingTop),paddingEnd:ct(l.paddingBottom)}})}})}),[Kr,va]=ua(he),ha=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:i,hasThumb:o,onThumbChange:r,onThumbPointerUp:l,onThumbPointerDown:p,onThumbPositionChange:d,onDragScroll:c,onWheelScroll:u,onResize:m,...f}=e,v=ge(he,n),[w,h]=s.useState(null),D=ye(t,A=>h(A)),L=s.useRef(null),S=s.useRef(""),T=v.viewport,F=i.content-i.viewport,M=De(u),O=De(d),I=xt(m,10);function X(A){if(L.current){const P=A.clientX-L.current.left,Y=A.clientY-L.current.top;c({x:P,y:Y})}}return s.useEffect(()=>{const A=P=>{const Y=P.target;(w==null?void 0:w.contains(Y))&&M(P,F)};return document.addEventListener("wheel",A,{passive:!1}),()=>document.removeEventListener("wheel",A,{passive:!1})},[T,w,F,M]),s.useEffect(O,[i,O]),Re(w,I),Re(v.content,I),a.jsx(Kr,{scope:n,scrollbar:w,hasThumb:o,onThumbChange:De(r),onThumbPointerUp:De(l),onThumbPositionChange:O,onThumbPointerDown:De(p),children:a.jsx(we.div,{...f,ref:D,style:{position:"absolute",...f.style},onPointerDown:fe(e.onPointerDown,A=>{A.button===0&&(A.target.setPointerCapture(A.pointerId),L.current=w.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",v.viewport&&(v.viewport.style.scrollBehavior="auto"),X(A))}),onPointerMove:fe(e.onPointerMove,X),onPointerUp:fe(e.onPointerUp,A=>{const P=A.target;P.hasPointerCapture(A.pointerId)&&P.releasePointerCapture(A.pointerId),document.body.style.webkitUserSelect=S.current,v.viewport&&(v.viewport.style.scrollBehavior=""),L.current=null})})})}),lt="ScrollAreaThumb",ba=s.forwardRef((e,t)=>{const{forceMount:n,...i}=e,o=va(lt,e.__scopeScrollArea);return a.jsx(Ve,{present:n||o.hasThumb,children:a.jsx(Yr,{ref:t,...i})})}),Yr=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:i,...o}=e,r=ge(lt,n),l=va(lt,n),{onThumbPositionChange:p}=l,d=ye(t,m=>l.onThumbChange(m)),c=s.useRef(void 0),u=xt(()=>{c.current&&(c.current(),c.current=void 0)},100);return s.useEffect(()=>{const m=r.viewport;if(m){const f=()=>{if(u(),!c.current){const v=Zr(m,p);c.current=v,p()}};return p(),m.addEventListener("scroll",f),()=>m.removeEventListener("scroll",f)}},[r.viewport,u,p]),a.jsx(we.div,{"data-state":l.hasThumb?"visible":"hidden",...o,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:fe(e.onPointerDownCapture,m=>{const v=m.target.getBoundingClientRect(),w=m.clientX-v.left,h=m.clientY-v.top;l.onThumbPointerDown({x:w,y:h})}),onPointerUp:fe(e.onPointerUp,l.onThumbPointerUp)})});ba.displayName=lt;var Zt="ScrollAreaCorner",wa=s.forwardRef((e,t)=>{const n=ge(Zt,e.__scopeScrollArea),i=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&i?a.jsx(Gr,{...e,ref:t}):null});wa.displayName=Zt;var Gr=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,...i}=e,o=ge(Zt,n),[r,l]=s.useState(0),[p,d]=s.useState(0),c=!!(r&&p);return Re(o.scrollbarX,()=>{var m;const u=((m=o.scrollbarX)==null?void 0:m.offsetHeight)||0;o.onCornerHeightChange(u),d(u)}),Re(o.scrollbarY,()=>{var m;const u=((m=o.scrollbarY)==null?void 0:m.offsetWidth)||0;o.onCornerWidthChange(u),l(u)}),c?a.jsx(we.div,{...i,ref:t,style:{width:r,height:p,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function ct(e){return e?parseInt(e,10):0}function ya(e,t){const n=e/t;return isNaN(n)?0:n}function ft(e){const t=ya(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,i=(e.scrollbar.size-n)*t;return Math.max(i,18)}function Xr(e,t,n,i="ltr"){const o=ft(n),r=o/2,l=t||r,p=o-l,d=n.scrollbar.paddingStart+l,c=n.scrollbar.size-n.scrollbar.paddingEnd-p,u=n.content-n.viewport,m=i==="ltr"?[0,u]:[u*-1,0];return ja([d,c],m)(e)}function Sn(e,t,n="ltr"){const i=ft(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,r=t.scrollbar.size-o,l=t.content-t.viewport,p=r-i,d=n==="ltr"?[0,l]:[l*-1,0],c=Xa(e,d);return ja([0,l],[0,p])(c)}function ja(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const i=(t[1]-t[0])/(e[1]-e[0]);return t[0]+i*(n-e[0])}}function ka(e,t){return e>0&&e<t}var Zr=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},i=0;return function o(){const r={left:e.scrollLeft,top:e.scrollTop},l=n.left!==r.left,p=n.top!==r.top;(l||p)&&t(),n=r,i=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(i)};function xt(e,t){const n=De(e),i=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(i.current),[]),s.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(n,t)},[n,t])}function Re(e,t){const n=De(t);Ya(()=>{let i=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(i),i=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(i),o.unobserve(e)}}},[e,n])}var Da=ma,Jr=xa,Qr=wa;const Jt=s.forwardRef(({className:e,children:t,...n},i)=>a.jsxs(Da,{ref:i,className:Ne("relative overflow-hidden",e),...n,children:[a.jsx(Jr,{className:"h-full w-full rounded-[inherit]",children:t}),a.jsx(Sa,{}),a.jsx(Qr,{})]}));Jt.displayName=Da.displayName;const Sa=s.forwardRef(({className:e,orientation:t="vertical",...n},i)=>a.jsx(Gt,{ref:i,orientation:t,className:Ne("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...n,children:a.jsx(ba,{className:"relative flex-1 rounded-full bg-border"})}));Sa.displayName=Gt.displayName;const es=({fileName:e,isValid:t,errors:n,isSpecificGIS:i,isLoading:o})=>{if(o)return a.jsxs(tn,{className:"w-full",children:[a.jsx(nn,{children:a.jsx(an,{children:"Validiere Code..."})}),a.jsx(on,{children:a.jsx("p",{children:"Bitte warten, der Code wird analysiert."})})]});if(e===null||t===null)return null;const r=n&&n.filter(l=>l.severity==="error").length>0;return a.jsxs(tn,{className:"w-full mt-4",children:[a.jsx(nn,{className:"pb-2",children:a.jsxs(an,{className:"flex items-center",children:[r?a.jsx(Nn,{className:"text-red-500 mr-2"}):a.jsx(zt,{className:"text-green-500 mr-2"}),"Validierungsergebnis für: ",e]})}),a.jsxs(on,{children:[i===!0&&a.jsx("p",{className:"text-sm text-blue-600 mb-2",children:"Spezifischer GIS-Code erkannt (z.B. arcpy, pyqgis). Die grundlegende Syntax scheint valide zu sein."}),i===!1&&a.jsx("p",{className:"text-sm text-orange-600 mb-2",children:"Kein spezifischer GIS-Code (arcpy, pyqgis) erkannt. Die Validierung konzentriert sich auf allgemeine Syntax."}),r?a.jsxs(a.Fragment,{children:[a.jsxs("p",{className:"text-red-500 mb-3",children:["Es ",n.filter(l=>l.severity==="error").length===1?"wurde 1 Syntaxfehler":`wurden ${n.filter(l=>l.severity==="error").length} Syntaxfehler`," gefunden:"]}),a.jsx(Jt,{className:"h-[150px] w-full rounded-md border p-4",children:n.map((l,p)=>l.severity==="error"&&a.jsxs("div",{className:"mb-2 pb-2 border-b last:border-b-0 last:mb-0",children:[a.jsxs("p",{className:"text-sm font-semibold text-red-700",children:["Syntaxfehler #",p+1]}),a.jsx("p",{className:"text-xs text-gray-700 whitespace-pre-wrap",children:l.message})]},p))})]}):t&&a.jsx("p",{className:"text-green-600",children:"Der Code scheint syntaktisch korrekt zu sein."}),!t&&!r&&a.jsx("p",{className:"text-orange-500",children:"Die Validierung war nicht erfolgreich, aber es wurden keine spezifischen Syntaxfehler vom LLM gemeldet."})]})]})};function ts({value:e,onChange:t,language:n,onLanguageChange:i,height:o="500px",onGenerateDocumentation:r,readOnly:l=!1,onFileUploaded:p,errors:d=[],onErrorsChange:c=()=>{}}){const[u,m]=s.useState(!1),[f,v]=s.useState(null),[w,h]=s.useState(null),[D,L]=s.useState(null),[S,T]=s.useState(0),F=s.useRef(null),{theme:M,systemTheme:O}=Fa(),I=s.useRef(null);s.useMemo(()=>["arcpy","arcgis","qgis","gdal","ogr","geopandas","shapely","fiona","pyproj","rasterio","earthpy","leaflet","mapbox","openlayers","spatial","buffer","intersect","projection","feature","geometry","raster","vector","crs","epsg","wgs84","utm","overlay","clip","dissolve","union","erase","merge","spatial.join","geotransform","multipolygon","multipoint","multilinestring","featurelayer","pointcloud","dem","tin","raster.dataset","feature.class","shapefile","geojson","geopackage","gdb","geodatabase"],[]);const X=s.useMemo(()=>`url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cpath d='M15 10L30 0L45 10L45 30L30 40L15 30L15 10Z' fill='none' stroke='%23cccccc' stroke-opacity='0.2' stroke-width='1'/%3E%3C/svg%3E")`,[]);s.useEffect(()=>{e&&e.trim().length>0&&A(e)},[]);const A=s.useCallback(async(z,x)=>{if(!z.trim()){v(null),c([]);return}F.current&&clearTimeout(F.current),F.current=setTimeout(async()=>{var y;m(!0),c([]);let g=x||n||"";try{const R={code:z,language:x||n||""};E.info(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-blue-500 border-r-transparent animate-spin mt-0.5"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Code-Validierung (Phase 1/2)"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Syntax-Prüfung & Sprach-Erkennung..."})]})]}),{id:"code-validation-initial",duration:4e3});const k=await(await tt.validate_code_endpoint(R)).json();g=k.detected_language||g,k.detected_language&&k.detected_language!=="Unknown"&&i&&i(k.detected_language);let q,B;if(!k.is_valid&&k.error_message){const H=k.error_message.match(/line\s+(\d+)/i),C=k.error_message.match(/column\s+(\d+)/i);H&&(q=parseInt(H[1],10)),C&&(B=parseInt(C[1],10)),q!==void 0&&q<1&&(q=1),B!==void 0&&B<1&&(B=1)}v({...k,errorLineNumber:k.is_valid?void 0:k.error_line_number?Math.max(1,k.error_line_number):q,errorColumnNumber:k.is_valid?void 0:k.error_column_number?Math.max(1,k.error_column_number):B}),E.dismiss("code-validation-initial"),k.is_valid?E.success(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(zt,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Erste Prüfung OK"}),a.jsxs("p",{className:"text-xs text-muted-foreground",children:["Sprache: ",k.detected_language,k.is_gis_code&&" (GIS)",". Fahre mit LLM-Validierung fort..."]})]})]}),{duration:2e3}):E.warning(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(et,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Erste Prüfung fand Probleme"}),a.jsxs("p",{className:"text-xs text-muted-foreground",children:[(y=k.error_message)==null?void 0:y.substring(0,100),"... Fahre mit LLM für Details fort."]})]})]}),{duration:3e3}),E.info(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-purple-500 border-r-transparent animate-spin mt-0.5"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"LLM-Validierung (Phase 2/2)"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Detaillierte Syntax-Analyse mit KI..."})]})]}),{id:"code-validation-llm",duration:1e4});const G={code:z,language:g},Q=await tt.llm_validate_code_syntax_endpoint(G),U=await Q.json();if(console.log("LLM Validation API Raw Response (llmApiResponse):",Q),console.log("LLM Validation API Parsed Data (llmData):",JSON.stringify(U,null,2)),U&&typeof U.isValid<"u"?(console.log("LLM Validation - llmData.isValid:",U.isValid),console.log("LLM Validation - llmData.errors:",JSON.stringify(U.errors,null,2))):console.log(U?"LLM Validation - llmData received, but isValid is undefined.":"LLM Validation - No llmData object after parsing JSON."),E.dismiss("code-validation-llm"),U.errors&&U.errors.length>0){const H=U.errors.map(C=>({line:Math.max(1,C.line||1),column:Math.max(1,C.column||1),message:C.message,severity:C.severity||"error"}));c(H),E.error(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(et,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsxs("span",{className:"font-medium",children:["LLM fand ",U.errors.length," Syntax-Problem(e)"]}),a.jsxs("p",{className:"text-xs text-muted-foreground",children:["Prüfen Sie den Editor für Details. Erstes Problem: ",U.errors[0].message.substring(0,100),"..."]})]})]}),{duration:5e3}),v(C=>({...C||k||{},isValid:!1,error_message:C!=null&&C.is_valid?`LLM found ${U.errors.length} issue(s). Check details below.`:(C==null?void 0:C.error_message)||`LLM found ${U.errors.length} issue(s). Check details below.`}))}else c([]),k.is_valid&&E.success(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(zt,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"LLM bestätigt: Code sieht gut aus!"}),a.jsxs("p",{className:"text-xs text-muted-foreground",children:["Sprache: ",g]})]})]}),{duration:3e3})}catch(R){console.error("Error during multi-stage code validation:",R),E.dismiss("code-validation-initial"),E.dismiss("code-validation-llm"),E.error(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(Nn,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Validierungsprozess-Fehler"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Ein Problem ist aufgetreten. Bitte versuchen Sie es erneut."})]})]})),c([])}finally{m(!1)}},800)},[n,i,c]),P=z=>{t(z||""),c([]),v(null),A(z||"")},Y=s.useCallback(()=>{T(z=>z+1)},[]),se=s.useCallback(()=>{T(z=>Math.max(0,z-1))},[]),ie=s.useCallback(z=>{var G;T(0);const x=z[0];if(!x)return;const g=5*1024*1024;if(x.size>g){E.error(`Dateigröße überschreitet das 5MB-Limit (${(x.size/(1024*1024)).toFixed(2)}MB)`);return}v(null),c([]);const y=[".py",".pyw",".pyc",".pyd",".pyo",".pyi",".js",".jsx",".mjs",".cjs",".ts",".tsx",".r",".rmd",".sql",".java",".c",".cpp",".cc",".h",".hpp",".cs",".txt",".md",".json",".xml",".yaml",".yml"],R="."+((G=x.name.split(".").pop())==null?void 0:G.toLowerCase());let $;y.includes(R)||E.warning(a.jsxs("div",{className:"flex items-start gap-2",children:[a.jsx(et,{className:"flex-shrink-0 mt-0.5",size:16}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Nicht unterstützter Dateityp"}),a.jsxs("p",{className:"text-sm text-muted-foreground",children:[R," Dateien können möglicherweise nicht korrekt validiert werden. Versuche als Text zu lesen..."]})]})]}));const k=x.type||"text/plain";L({name:x.name,extension:R,type:k}),p&&p({name:x.name,extension:R,type:k}),$={".py":"Python",".js":"JavaScript",".jsx":"JavaScript",".ts":"TypeScript",".tsx":"TypeScript",".r":"R",".rmd":"R",".sql":"SQL",".java":"Java",".c":"C",".cpp":"C++",".cc":"C++",".h":"C++",".hpp":"C++",".cs":"C#"}[R],i&&$&&i($),h(x.size);const B=new FileReader;B.onload=Q=>{var H;const U=(H=Q.target)==null?void 0:H.result;t(U),A(U,$),E.success(`Datei "${x.name}" erfolgreich hochgeladen`)},B.onerror=()=>{E.error("Fehler beim Lesen der Datei")},B.readAsText(x)},[t,A,i,p,n]),{getRootProps:V,getInputProps:j,isDragActive:_,open:oe}=Fn({onDragEnter:Y,onDragLeave:se,onDrop:ie,accept:{"text/plain":[".txt",".py",".js",".ts",".r",".sql",".java",".cpp",".c",".h",".cs"],"application/x-python":[".py"],"application/javascript":[".js"],"application/x-javascript":[".js"],"text/javascript":[".js"]},noClick:!0,noKeyboard:!0}),le=()=>{h(null)};return a.jsxs("div",{className:"space-y-4","data-testid":"code-uploader",children:[a.jsx("style",{jsx:"true",children:`
        @keyframes slideIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-slideIn {
          animation: slideIn 0.3s ease-out forwards;
        }

        /* Error highlighting styles for Ace Editor */
        :global(.ace_editor .error-line) {
          position: absolute;
          background-color: rgba(255, 0, 0, 0.08);
          z-index: 5;
          width: 100% !important;
          pointer-events: none;
        }
      `}),w&&w>100*1024&&a.jsx($r,{fileSize:w,onDismiss:le,isGisCode:f==null?void 0:f.isGisCode}),a.jsxs("div",{...l?{}:V(),className:`relative border-2 border-dashed rounded-lg p-2 transition-colors flex flex-col items-center justify-center ${l?"":"cursor-pointer"} 
        ${_?"border-primary bg-primary/5":"border-muted hover:border-primary/50 hover:bg-muted/10"} ${l?"opacity-70":""}`,style:{minHeight:"60px"},children:[a.jsxs("div",{className:"absolute inset-0 overflow-hidden opacity-20 pointer-events-none transition-opacity duration-300",style:{opacity:_?.35:.2,backgroundImage:X,backgroundSize:"60px 60px",filter:_?"blur(0)":"blur(1px)"},children:[a.jsx("div",{className:"absolute -top-5 -right-5 w-24 h-24 rotate-12 opacity-20 bg-primary rounded-xl blur-xl transition-all duration-700",style:{transform:_?"rotate(12deg) scale(1.2)":"rotate(12deg) scale(1)",opacity:_?.3:.2}}),a.jsx("div",{className:"absolute bottom-3 left-10 w-16 h-16 bg-card opacity-40 rounded-lg transition-all duration-500",style:{transform:_?"rotate(60deg) scale(1.1)":"rotate(45deg) scale(1)",clipPath:"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"}}),a.jsx("div",{className:"absolute top-1/3 left-1/4 w-10 h-10 bg-card opacity-30 rounded-lg transition-all duration-300",style:{transform:_?"rotate(60deg) scale(1.15)":"rotate(45deg) scale(1)",clipPath:_?"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)":"none"}}),a.jsx("div",{className:"absolute top-1/2 right-1/3 w-12 h-12 bg-card opacity-20 rounded-full blur-sm transition-all duration-400",style:{transform:_?"rotate(-30deg) scale(1.2)":"rotate(12deg) scale(1)",opacity:_?.4:.2}})]}),a.jsx("input",{...j()}),a.jsxs("div",{className:"text-center space-y-1 flex items-center p-1",children:[a.jsxs("div",{className:"relative mx-auto w-10 h-10 flex items-center justify-center",children:[a.jsx("div",{className:"absolute w-8 h-8 bg-primary/10 rotate-[30deg] rounded-xl backdrop-blur-sm"}),a.jsx("div",{className:"absolute w-8 h-8 bg-primary/5 rotate-[60deg] rounded-xl"}),a.jsx(ci,{size:16,className:"relative z-10 text-primary"})]}),a.jsxs("div",{className:"flex-1 flex flex-col ml-2",children:[a.jsx("h3",{className:"text-sm font-mono tracking-wide transition-all duration-300",style:{transform:_?"scale(1.05)":"scale(1)",color:_?"var(--primary)":"inherit"},children:_?"Drop your code file here...":"Drag & drop or select a file"}),a.jsx("div",{className:"flex flex-wrap gap-1 mt-1 justify-start",children:a.jsx(ca,{children:[".py",".js",".ts",".r",".sql",".java",".cpp",".c",".cs"].map(z=>a.jsxs(pa,{children:[a.jsx(da,{asChild:!0,children:a.jsx("span",{className:"cursor-help",children:a.jsx(st,{fileExtension:z})})}),a.jsx(Kt,{side:"bottom",className:"max-w-[200px]",children:{".py":"Python files (.py, .pyw, .pyi)",".js":"JavaScript files (.js, .jsx, .mjs, .cjs)",".ts":"TypeScript files (.ts, .tsx)",".r":"R files (.r, .rmd)",".sql":"SQL files (.sql)",".java":"Java files (.java)",".cpp":"C++ files (.cpp, .cc, .h, .hpp)",".c":"C files (.c, .h)",".cs":"C# files (.cs)"}[z]})]},z))})})]}),a.jsxs(ue,{type:"button",variant:"outline",size:"sm",onClick:oe,className:"ml-2 font-mono tracking-wide whitespace-nowrap",disabled:l,children:[a.jsx($e,{size:14,className:"mr-1"}),"Select"]})]})]}),r&&!l&&a.jsx("div",{className:"mt-2 mb-2 relative overflow-hidden",children:a.jsxs(ue,{onClick:z=>{z.preventDefault(),z.stopPropagation(),r&&r()},disabled:!e.trim(),className:"w-full relative overflow-hidden group transition-all duration-200 bg-gradient-to-r from-primary/80 to-primary hover:from-primary hover:to-primary/90 font-mono tracking-wide",children:[a.jsx("div",{className:"absolute inset-0 opacity-10 pointer-events-none group-hover:opacity-20 transition-opacity duration-200 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMTMuOTggMjVMNiA0Mi42NjAzIDE0IDQyLjY2MDNsMi41LTYuNjYwMyA3IC4wMDAxIDIuNSA2LjY2MDMgOCAwTDI2LjAyIDI1IDM0IDcuMzM5N2wtOC0uMDAwMUwyMy41IDE0IDEzLjk4IDI1WiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+')]"}),a.jsx("div",{className:"absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10",style:{clipPath:"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"}}),a.jsx("div",{className:"absolute -right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/10",style:{clipPath:"polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"}}),a.jsxs("span",{className:"relative z-10 flex items-center justify-center gap-2",children:[a.jsx(En,{size:16,className:"mr-1"}),"Generate Documentation"]})]})}),a.jsx("div",{className:"relative border shadow-sm rounded-md overflow-hidden",children:a.jsx(Yn,{ref:I,value:e,onChange:P,language:n,height:o,placeholder:"// Or paste your code here...",readOnly:l,theme:M==="system"?O==="dark"?"vs-dark":"light":M==="dark"?"vs-dark":"light",errors:d})}),(f||d.length>0)&&a.jsx(es,{fileName:(D==null?void 0:D.name)||"Current Code",isValid:d.length>0?!1:f?f.isValid:!0,errors:d.map(z=>({line:Math.max(1,z.line),column:Math.max(1,z.column),message:z.message,severity:z.severity||"error"})),isSpecificGIS:(f==null?void 0:f.is_gis_code)||!1,isLoading:u}),u&&a.jsxs(qt,{className:"bg-card/80 backdrop-blur-sm border-primary/20 animate-pulse border-l-4",style:{borderLeftColor:"var(--primary)"},children:[a.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-primary border-r-transparent animate-spin mr-2"}),a.jsx(Ut,{children:"Validating your code"}),a.jsxs(Ht,{children:["Checking syntax and detecting language...",n==="Python"&&a.jsx("span",{className:"text-xs block mt-1 text-muted-foreground",children:"Auch GIS-spezifische Funktionen (wie arcpy, QGIS) werden erkannt."})]})]}),D&&a.jsxs("div",{className:"flex items-center gap-2 text-sm animate-slideIn p-2 bg-muted/20 rounded-md backdrop-blur-sm border-l-2 border-primary/30 relative overflow-hidden",children:[a.jsx("div",{className:"absolute -right-3 top-0 h-full w-6 bg-primary/5 skew-x-[-20deg]"}),a.jsx($e,{size:16,className:"text-muted-foreground"}),a.jsx("span",{className:"text-muted-foreground truncate max-w-[180px]",children:D.name}),a.jsx(st,{fileExtension:D.extension,isGisCode:f==null?void 0:f.isGisCode})]})]})}function ns({documentation:e,isLoading:t,onExportRequest:n,projectContext:i}){const o=()=>{n()},r=()=>t?a.jsxs("div",{className:"flex items-center justify-center p-8 h-full",children:[a.jsx(Ie,{className:"h-8 w-8 animate-spin text-primary"}),a.jsx("p",{className:"ml-2",children:"Generiere Dokumentation..."})]}):e?a.jsx("div",{className:"prose prose-sm dark:prose-invert max-w-none p-4",dangerouslySetInnerHTML:{__html:e}}):a.jsx("div",{className:"p-4 text-muted-foreground",children:a.jsx("p",{children:"Hier wird die generierte Dokumentation angezeigt."})});return a.jsxs("div",{className:"flex flex-col h-full border rounded-md bg-card",children:[a.jsxs("div",{className:"flex items-center justify-between p-2 border-b bg-muted/30",children:[a.jsx("h2",{className:"text-lg font-semibold px-2",children:"Vorschau"}),a.jsxs(ue,{variant:"outline",size:"sm",onClick:o,disabled:!e||t,children:[a.jsx(ti,{className:"h-4 w-4 mr-2"}),"Exportieren"]})]}),a.jsx(Jt,{className:"flex-grow",children:r()})]})}console.log("[Editor.tsx] Component rendering anfangen");function gs(){var le,z;Aa();const e=za(),{user:t}=Ea(),{profile:n}=Na(),[i,o]=s.useState("editor"),[r,l]=s.useState(!0),[p,d]=s.useState(null),[c,u]=s.useState(null),[m,f]=s.useState(!1),[v,w]=s.useState(""),[h,D]=s.useState(""),[L,S]=s.useState(""),[T,F]=s.useState(""),[M,O]=s.useState(!1),[I,X]=s.useState(!1),[A,P]=s.useState(!1),[Y,se]=s.useState(null),[ie,V]=s.useState([]);s.useEffect(()=>{console.log("[Editor.tsx] useEffect anfangen - location.search:",e.search);const x=async(G,Q)=>{if(!(G!=null&&G.uid))return E.error("Sie müssen angemeldet sein, um ein Dokument zu erstellen"),!1;try{const{useDocumentStore:U}=await Pa(()=>import("./documentStore-4a8258c1.js"),["assets/documentStore-4a8258c1.js","assets/vendor-8eb8bd34.js","assets/index-3b130772.js","assets/index-2ce5e6d7.css"]),{canCreate:H,currentCount:C,maxAllowed:ce}=await U.getState().checkDocumentLimit(G.uid);return H?!0:(d(`Sie haben das Limit von ${ce} Dokumenten für Ihren aktuellen Plan erreicht. Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente.`),E.error(a.jsxs("div",{className:"space-y-2",children:[a.jsx("p",{children:a.jsx("strong",{children:"Dokumentenlimit erreicht!"})}),a.jsxs("p",{children:["Sie haben das Limit von ",ce," Dokumenten für Ihren aktuellen Plan erreicht."]}),a.jsx("p",{className:"text-sm",children:"Um mehr Dokumente zu erstellen, aktualisieren Sie auf einen höheren Plan oder löschen Sie nicht benötigte Dokumente."})]}),{duration:8e3}),!1)}catch(U){return console.error("Error checking document limit:",U),!0}},g=new URLSearchParams(window.location.search),y=g.get("id"),R=g.get("view"),$=g.get("new"),k=g.get("tab");console.log("URL parameters:",g.toString(),"viewParam:",R);const q=R==="true"||R==="True"||R==="1"||R==="yes";console.log("Setting view mode to:",q),f(q),k&&(k==="editor"||k==="documentation")&&o(k),(async()=>{var G,Q,U;if($==="true"&&!y){if(t!=null&&t.uid){if(!await x(t)){l(!1);return}const C=sessionStorage.getItem("lastDocumentCreationTime"),ce=Date.now();if(C&&ce-parseInt(C)<1e4){console.log("Preventing duplicate document creation, redirecting to documents page"),E.info("Document creation already in progress"),window.location.href="./documents";return}sessionStorage.setItem("lastDocumentCreationTime",ce.toString())}l(!1),u({id:"new",title:"New Document",description:"",createdAt:new Date,updatedAt:new Date,userId:(t==null?void 0:t.uid)||"",language:"python",codeSize:0,isPublic:!1,tags:[],content:{originalCode:"",generatedDocumentation:null,parameters:null,toolsUsed:null,workflow:null,manualEdits:[]}}),w(""),D("New Document"),S(""),F(""),se(null),V([]);return}if(!y){l(!1);return}try{if(l(!0),d(null),E.loading("Loading document..."),!(t!=null&&t.uid)){d("You must be logged in to view this document"),E.error("You must be logged in to view this document"),l(!1);return}const H=Dt(kt),C=ke(H,Ge.DOCUMENTS,y),ce=await Qt(C);if(!ce.exists()){d("Document not found"),E.error("Document not found"),l(!1);return}const K=ce.data();if(K.userId!==t.uid&&!K.isPublic){E.loading("Checking document permissions..."),await Ia(t.uid);const ee=await Qt(C);if(!ee.exists()||ee.data().userId!==t.uid&&!ee.data().isPublic){d("You do not have permission to view this document"),E.error("You do not have permission to view this document"),l(!1);return}}const je=await Ta(_e(C,"content"));if(je.empty){d("Document content not found"),E.error("Document content not found"),l(!1);return}const de=je.docs[0].data(),pe={id:y,title:K.title,description:K.description,createdAt:(G=K.createdAt)==null?void 0:G.toDate(),updatedAt:(Q=K.updatedAt)==null?void 0:Q.toDate(),userId:K.userId,language:K.language,codeSize:K.codeSize,isPublic:K.isPublic,tags:K.tags||[],content:{originalCode:de.originalCode,generatedDocumentation:de.generatedDocumentation,parameters:de.parameters,toolsUsed:de.toolsUsed,workflow:de.workflow,manualEdits:((U=de.manualEdits)==null?void 0:U.map(ee=>{var xe;return{...ee,timestamp:(xe=ee.timestamp)==null?void 0:xe.toDate()}}))||[]}};if(u(pe),w(pe.content.originalCode||""),D(pe.title),S(pe.description||""),console.log("[Editor.tsx] Raw docData.project_context from Firestore:",K.project_context),K.project_context)if(typeof K.project_context=="string")F(K.project_context),console.log("[Editor.tsx] Set projectContext from string:",K.project_context);else if(typeof K.project_context=="object"&&K.project_context!==null){console.log("[Editor.tsx] docData.project_context is object:",K.project_context);const ee=K.project_context;let xe="";ee.text_context&&typeof ee.text_context=="string"&&ee.text_context.trim()!==""&&(xe+=`### Manueller Kontext

${ee.text_context.trim()}

`),ee.file_context_summary&&typeof ee.file_context_summary=="string"&&ee.file_context_summary.trim()!==""&&(xe+=`### Zusammenfassung der Kontextdateien

${ee.file_context_summary.trim()}

`),F(xe.trim()),console.log("[Editor.tsx] Set projectContext from object (combined):",xe.trim())}else F(""),console.log("[Editor.tsx] Set projectContext to empty string (type was not string or expected object).");else F(""),console.log("[Editor.tsx] docData.project_context is null or undefined, set projectContext to empty string.");se(null),V([]),E.success("Document loaded successfully");const ae=new URLSearchParams(window.location.search).get("view");console.log("Final viewParam check after document load:",ae),(ae==="true"||ae==="True"||ae==="1"||ae==="yes")&&(console.log("Re-setting view mode to true after document load"),f(!0))}catch(H){console.error("Error loading document:",H),d(H instanceof Error?H.message:"An unknown error occurred"),E.error(`Error loading document: ${H instanceof Error?H.message:"Unknown error"}`)}finally{l(!1),E.dismiss()}})()},[e.search,t]);const j=async()=>{if(!(t!=null&&t.uid))return E.error("You must be logged in to save documents"),!1;O(!0),E.loading("Saving document...");try{const x=Dt(kt),g=new URLSearchParams(window.location.search),y=g.get("id")!==null,R=!c||g.get("new")==="true"&&!y,$=new Date;if(R){console.log("Creating new document");const k=ke(_e(x,Ge.DOCUMENTS));sessionStorage.removeItem("lastDocumentCreationTime");const q={id:k.id,title:h||"Untitled Document",description:L||"",project_context:T,createdAt:$,updatedAt:$,userId:t.uid,language:"python",codeSize:v.length,isPublic:!1,tags:[],isDeleted:!1,owner:t.uid,readers:[t.uid],writers:[t.uid],permissionsLastUpdated:$},B={originalCode:v,generatedDocumentation:null,parameters:null,toolsUsed:null,workflow:null,manualEdits:[]};await en(k,q),console.log("Main document created successfully");const G=ke(_e(k,"content"),"latest");await en(G,B),console.log("Content subcollection document created successfully"),u({...q,content:B});const Q=`${window.location.pathname}?id=${k.id}`;return window.history.replaceState(null,"",Q),console.log("Updated URL without full navigation"),E.success("New document created successfully"),!0}else{if(console.log("Updating existing document"),!(c!=null&&c.id))return E.error("Cannot update document without an ID."),!1;const k=ke(x,Ge.DOCUMENTS,c.id);await Xe(k,{title:h,description:L,project_context:T,updatedAt:$,codeSize:v.length}),console.log("Main document updated successfully");const q=ke(_e(k,"content"),"latest");return await Xe(q,{originalCode:v,manualEdits:Ma({timestamp:$,section:"code",content:"Manual update"})}),console.log("Content subcollection updated successfully"),u(B=>B?{...B,title:h,description:L,project_context:T,updatedAt:$,codeSize:v.length,content:{...B.content,originalCode:v,manualEdits:[...B.content.manualEdits||[],{timestamp:$,section:"code",content:"Manual update"}]}}:null),E.success("Document updated successfully"),!0}}catch(x){console.error("Detailed error saving document:",x);let g="An unknown error occurred",y="N/A";return x instanceof Error?(g=x.message,typeof x=="object"&&x!==null&&"code"in x&&(y=x.code),y&&y!=="N/A"&&(g+=` (Firestore code: ${y})`),console.error("Error Name:",x.name),console.error("Error Message:",x.message),y!=="N/A"&&console.error("Firestore Error Code:",y),x.stack&&console.error("Stack Trace:",x.stack)):(console.error("Non-Error object thrown:",x),g="An unexpected non-error type was thrown."),E.dismiss(),x instanceof Error&&x.message.includes("permission")||y.includes("permission-denied")?E.error(a.jsxs("div",{className:"space-y-2 p-2",children:[a.jsx("p",{className:"font-semibold",children:"Permissions error when saving document."}),a.jsx("p",{className:"text-xs",children:"This is likely due to Firestore security rules. Please check the browser console (F12) for more technical details."}),a.jsxs("p",{className:"text-xs font-mono bg-muted p-1 rounded",children:["Error: ",g]}),a.jsx("p",{className:"text-xs mt-2",children:"Possible checks:"}),a.jsxs("ul",{className:"text-xs list-disc list-inside space-y-1 pl-4",children:[a.jsx("li",{children:"Are you correctly signed in?"}),a.jsx("li",{children:"Do Firestore rules for the 'documents' and their 'content' subcollections allow the necessary 'create' or 'update' operations for your user ID? (See console for specific operation causing failure)"})]})]}),{duration:15e3}):E.error(`Error saving document: ${g}`),!1}finally{O(!1),setTimeout(()=>E.dismiss(),100)}},_=async()=>{if(ie.length===0){E.info("Bitte wählen Sie zuerst Dateien für die Zusammenfassung aus.");return}if(m){E.info("Das Zusammenfassen von Kontextdateien ist im Ansichtsmodus nicht möglich.");return}P(!0);const x=E.loading("Kontextdateien werden zusammengefasst...");try{const g={files:ie},y=await tt.summarize_uploaded_files(g);if(!y.ok){let q=`Fehler beim Zusammenfassen: ${y.status} ${y.statusText}`;try{q=(await y.json()).detail||q}catch{}throw new Error(q)}const R=await y.json(),$=R.summary,k=R.message;$&&$.trim()!==""?(F(q=>{const B=`### Zusammenfassung der Kontextdateien

${$.trim()}`,G=q&&typeof q=="string"?q.trim():"";return G!==""?G.includes("### Zusammenfassung der Kontextdateien")?G+`

`+$.trim():G+`

`+B:B}),E.success(k||"Kontextdateien erfolgreich zusammengefasst.",{id:x})):k?E.info(k,{id:x,duration:8e3}):E.warning("Die API hat keine Zusammenfassung zurückgegeben, aber auch keine spezifische Nachricht.",{id:x,duration:8e3}),V([])}catch(g){console.error("Error summarizing context files:",g),E.error(`Fehler beim Zusammenfassen der Kontextdateien: ${g instanceof Error?g.message:"Unbekannter Fehler"}`,{id:x})}finally{P(!1)}},oe=s.useMemo(()=>{var g,y;if(!((g=c==null?void 0:c.content)!=null&&g.generatedDocumentation))return{originalCode:((y=c==null?void 0:c.content)==null?void 0:y.originalCode)||"",textualDescription:"Keine Textbeschreibung verfügbar.",parameters:[],toolsUsed:[],workflow:{steps:[]}};let x={};if(typeof c.content.generatedDocumentation=="string")try{x=JSON.parse(c.content.generatedDocumentation)}catch(R){console.error("Failed to parse document.content.generatedDocumentation:",R)}else typeof c.content.generatedDocumentation=="object"&&c.content.generatedDocumentation!==null&&(x=c.content.generatedDocumentation);return{originalCode:c.content.originalCode||"",textualDescription:x.textualDescription||"Keine Textbeschreibung verfügbar.",parameters:x.parameters||[],toolsUsed:x.toolsUsed||[],workflow:x.workflow||{steps:[]},...Object.keys(x).length>0?x:{}}},[(le=c==null?void 0:c.content)==null?void 0:le.generatedDocumentation,(z=c==null?void 0:c.content)==null?void 0:z.originalCode]);return console.log("[Editor.tsx] Processed Documentation for Preview:",oe),console.log("[Editor.tsx] Project Context BEFORE passing to Preview:",T),r?a.jsx("div",{className:"container p-4 flex items-center justify-center min-h-[50vh]",children:a.jsxs("div",{className:"text-center",children:[a.jsx(Ie,{className:"h-10 w-10 animate-spin mx-auto mb-4 text-primary"}),a.jsx("p",{children:"Loading document..."})]})}):p?a.jsxs("div",{className:"container p-4",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h1",{className:"text-xl font-bold",children:"Code Documentation Generator"}),a.jsxs(ue,{onClick:()=>{window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"",window.location.href="./documents"},children:[a.jsx(St,{className:"h-4 w-4 mr-2"}),"Back to Documents"]})]}),a.jsxs("div",{className:"border rounded p-6 bg-destructive/10",children:[a.jsx("h2",{className:"text-lg font-bold mb-4",children:"Error Loading Document"}),a.jsx("p",{children:p}),a.jsx(ue,{variant:"outline",className:"mt-4",onClick:()=>{const x=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href=x.split("/editor")[0]+"/documents"},children:"Return to Documents"})]})]}):a.jsxs("div",{className:"container p-4",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h1",{className:"text-xl font-bold",children:"Code Documentation Generator"}),a.jsxs(ue,{onClick:()=>{const x=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href=x.split("/editor")[0]+"/documents"},children:[a.jsx(St,{className:"h-4 w-4 mr-2"}),"Back to Documents"]})]}),c?a.jsxs("div",{children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsxs("div",{children:[a.jsx("h2",{className:"text-lg",children:h}),L&&a.jsx("p",{className:"text-sm text-muted-foreground",children:L})]}),!m&&a.jsx(ue,{onClick:j,disabled:M,children:M?a.jsxs(a.Fragment,{children:[a.jsx(Ie,{className:"h-4 w-4 mr-2 animate-spin"})," Saving..."]}):a.jsxs(a.Fragment,{children:[a.jsx(si,{className:"h-4 w-4 mr-2"})," Save"]})})]}),a.jsx("div",{className:"bg-card/30 p-4 rounded-md mb-4",children:a.jsxs("div",{className:"flex flex-col gap-2 mb-4",children:[a.jsxs("div",{className:"flex flex-col",children:[a.jsx("label",{htmlFor:"documentTitle",className:"text-sm font-medium mb-1",children:"Dokumenttitel"}),a.jsx("input",{id:"documentTitle",type:"text",value:h,onChange:x=>D(x.target.value),placeholder:"Geben Sie einen Titel für dieses Dokument ein",className:"border rounded-md p-2",disabled:m})]}),a.jsxs("div",{className:"flex flex-col",children:[a.jsx("label",{htmlFor:"documentDescription",className:"text-sm font-medium mb-1",children:"Beschreibung"}),a.jsx("textarea",{id:"documentDescription",value:L,onChange:x=>S(x.target.value),placeholder:"Beschreiben Sie den Zweck dieses Codes",className:"border rounded-md p-2 min-h-[80px]",disabled:m})]}),a.jsxs("div",{className:"bg-muted/30 backdrop-blur-sm border border-border/30 rounded-lg p-4 mt-4",children:[a.jsxs("div",{className:"flex flex-col",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsx("label",{htmlFor:"projectContextTextarea",className:"text-sm font-medium mb-1",children:"Projektkontext (optional)"})}),a.jsx("textarea",{id:"projectContextTextarea",value:T||"",onChange:x=>F(x.target.value),placeholder:"Geben Sie zusätzlichen Kontext für die Dokumentationsgenerierung ein (z.B. Projekt-Hintergrundinformationen, spezifische Domänen-Terminologie)",className:"border rounded-md p-2 min-h-[120px]",disabled:m}),a.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Dieser Kontext wird während der Dokumentationsgenerierung verwendet, um domänenspezifisches Wissen einzubeziehen."})]}),a.jsxs("div",{className:"flex flex-col mt-3",children:[a.jsx("label",{htmlFor:"contextFiles",className:"text-sm font-medium mb-1",children:"Kontextdateien (optional, z.B. PDF, DOCX, TXT, Bilder)"}),a.jsx("input",{id:"contextFiles",type:"file",accept:".pdf,.docx,.txt,.jpg,.jpeg,.png",multiple:!0,onChange:x=>{x.target.files&&(V(g=>[...g,...Array.from(x.target.files)]),x.target.value="")},className:"border rounded-md p-2 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20",disabled:m}),ie.length>0&&a.jsxs("div",{className:"mt-2 space-y-1",children:[a.jsx("p",{className:"text-xs font-medium",children:"Ausgewählte Kontextdateien:"}),a.jsx("ul",{className:"list-disc list-inside pl-4 text-xs",children:ie.map((x,g)=>a.jsxs("li",{className:"flex justify-between items-center",children:[a.jsxs("span",{children:[x.name," (",(x.size/1024).toFixed(2)," KB)"]}),!m&&a.jsx(ue,{variant:"ghost",size:"sm",className:"text-destructive hover:text-destructive-foreground hover:bg-destructive/80 h-6 px-2",onClick:()=>{V(y=>y.filter((R,$)=>$!==g))},children:"Entfernen"})]},g))}),!m&&a.jsxs(ue,{onClick:_,variant:"outline",size:"sm",className:"mt-2",disabled:ie.length===0||M||A,children:[A?a.jsx(Ie,{size:14,className:"mr-2 animate-spin"}):a.jsx(Za,{size:14,className:"mr-2"}),"Inhalte Zusammenfassen"]})]}),a.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Die Inhalte dieser Dateien werden temporär verarbeitet, um eine Zusammenfassung für den Projektkontext zu erstellen. Die Dateien selbst werden NICHT dauerhaft gespeichert."})]})]})," "]})}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{className:"border rounded-md overflow-hidden",children:[a.jsxs("div",{className:"bg-card/70 backdrop-blur-sm p-2 border-b font-mono text-sm flex justify-between items-center",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx($e,{size:14,className:"mr-2 text-muted-foreground"}),a.jsx("span",{children:"Code Editor"})]}),Y&&a.jsxs("div",{className:"flex items-center text-xs text-muted-foreground",children:[a.jsx(st,{fileExtension:`.${Y.name.split(".").pop()}`||"",isGisCode:!1}),a.jsx("span",{className:"ml-1 truncate max-w-[100px]",children:Y.name})]})]}),a.jsx(ts,{value:v,onChange:m?()=>{}:w,language:c.language||"python",onLanguageChange:x=>{!m&&c.id==="new"&&u(g=>g?{...g,language:x}:null)},height:"600px",readOnly:m,onGenerateDocumentation:async()=>{var x,g,y,R,$;try{console.log("Starting documentation generation process"),O(!0),X(!0);const k=!c||c.id==="new"||e.search.includes("new=true");if(console.log("Document state before saving:",{isNewDocument:k,documentId:c==null?void 0:c.id,locationSearch:e.search}),!await j()){console.log("Document save failed, aborting documentation generation"),E.error("Document could not be saved. Please try again."),X(!1),O(!1);return}const G=new URLSearchParams(window.location.search).get("id"),Q=c&&c.id!=="new"?c.id:G;if(!Q){console.error("No valid document ID found after save attempt. Aborting generation."),E.error("Could not determine document ID for generation. Please save the document first."),X(!1),O(!1);return}console.log("Proceeding with documentation generation for document ID:",Q),E.loading("Generating documentation... This may take a moment.",{id:"gen-doc-process"});let U={code:v,language:(c==null?void 0:c.language)||"python",provider:"auto",format:"json",documentId:Q,documentationLanguage:((x=n==null?void 0:n.preferences)==null?void 0:x.language)||"en"};try{const H=await tt.generate_documentation(U);console.log("Got response from documentation API:",H);const C=await H.json();if(console.log("Documentation generation data:",C),console.log("Processing API response - success:",C.success),!C.success){console.error("API reported failure:",C.error_message||"Unknown error"),E.error("Failed to generate documentation: "+(C.error_message||"Unknown error")),X(!1),O(!1);return}E.success("Documentation generated successfully",{id:"doc-success-"+Date.now()});try{console.log(`[Firestore Update Debug] Starting update. User UID: ${t.uid}, Document ID: ${Q}`);const ce=Dt(kt),K=ke(ce,Ge.DOCUMENTS,Q),je=ke(_e(K,"content"),"latest"),de=JSON.stringify(C.documentation);if(!L&&((g=C.documentation)!=null&&g.textualDescription)){const ae=C.documentation.textualDescription.split(".")[0]+".";console.log(`[Firestore Update Debug] Attempting to update parent document description. Expected Owner (from state): ${c==null?void 0:c.userId}`),S(ae),await Xe(K,{description:ae,language_used:C.language_used}),console.log("[Firestore Update Debug] Parent document description updated.")}console.log(`[Firestore Update Debug] Attempting to update content subcollection. Expected Parent Owner (from state): ${c==null?void 0:c.userId}`),await Xe(je,{generatedDocumentation:de,parameters:((y=C.documentation)==null?void 0:y.parameters)||null,toolsUsed:((R=C.documentation)==null?void 0:R.toolsUsed)||null,workflow:(($=C.documentation)==null?void 0:$.workflow)||null}),console.log("[Firestore Update Debug] Content subcollection updated."),console.log("Updating document state with generated documentation"),u(ae=>{var xe,We,Ke,b,N,Z,te,re;return ae?{...ae,id:Q,description:L||((N=C.documentation)!=null&&N.textualDescription?C.documentation.textualDescription.split(".")[0]+".":ae.description),language_used:C.language_used,content:{...ae.content,generatedDocumentation:de,parameters:((Z=C.documentation)==null?void 0:Z.parameters)||null,toolsUsed:((te=C.documentation)==null?void 0:te.toolsUsed)||null,workflow:((re=C.documentation)==null?void 0:re.workflow)||null}}:(console.log("Creating new document state from remote data"),{id:Q,title:h||"Untitled Document",description:L||((xe=C.documentation)!=null&&xe.textualDescription?C.documentation.textualDescription.split(".")[0]+".":""),language:"python",createdAt:new Date,updatedAt:new Date,userId:t.uid,language_used:C.language_used,codeSize:v.length,isPublic:!1,isDeleted:!1,content:{originalCode:v,generatedDocumentation:de,parameters:((We=C.documentation)==null?void 0:We.parameters)||null,toolsUsed:((Ke=C.documentation)==null?void 0:Ke.toolsUsed)||null,workflow:((b=C.documentation)==null?void 0:b.workflow)||null,manualEdits:[]}})}),setTimeout(()=>{console.log("Document state after update:",{docId:c==null?void 0:c.id,effectiveDocId:Q})},100),console.log("Switching to documentation tab"),o("documentation");const pe=new URLSearchParams(window.location.search);pe.set("tab","documentation");const Pe=`${window.location.pathname}?${pe.toString()}`;window.history.replaceState(null,"",Pe),E.success("Documentation is ready to view",{id:"doc-view-"+Date.now()})}catch(ce){console.error("Error updating document with generated documentation:",ce),E.error("Documentation was generated but could not be saved to the document")}}catch(H){console.error("Error generating documentation:",H),E.error(`Error generating documentation: ${H instanceof Error?H.message:"Unknown error"}`)}finally{E.dismiss("gen-doc-process"),X(!1),O(!1)}}catch(k){console.error("Unexpected error during documentation generation:",k),E.error(`Unexpected error: ${k instanceof Error?k.message:"Unknown error"}`),X(!1),O(!1)}},onFileUploaded:x=>se(x)})]}),a.jsx("div",{className:"border rounded-md min-h-[600px] h-[calc(100vh-280px)] bg-card/70 backdrop-blur-sm overflow-hidden",children:c.content.generatedDocumentation?a.jsx("div",{className:"flex flex-col h-full w-full",children:a.jsx(ns,{documentation:oe,isLoading:r,documentTitle:c.title,createdAt:c.createdAt,updatedAt:c.updatedAt,author:(t==null?void 0:t.displayName)||(t==null?void 0:t.email)||"Unbekannter Autor",version:c.version||"1.0.0",language_used:c.language_used||"en",projectContext:T||null,documentDescriptionProp:c.description||null})}):a.jsxs("div",{className:"h-full flex flex-col items-center justify-center text-center p-8",children:[a.jsx("div",{className:"w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mb-4",children:a.jsx($e,{size:24,className:"text-muted-foreground"})}),a.jsx("h3",{className:"text-lg font-medium mb-2",children:"No documentation generated yet"}),a.jsx("p",{className:"text-muted-foreground max-w-md",children:'Upload your code and click the "Generate Documentation" button to create automatic documentation.'}),Y&&a.jsx("div",{className:"mt-4 text-sm p-2 border rounded-md bg-muted/20",children:a.jsxs("p",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"font-medium",children:"Uploaded file:"}),a.jsxs("span",{className:"flex items-center",children:[a.jsx(st,{fileExtension:Y.extension,isGisCode:!1}),a.jsx("span",{className:"ml-1",children:Y.name})]})]})})]})})]})]}):a.jsxs("div",{className:"border rounded p-6 bg-muted/30 backdrop-blur-sm",children:[a.jsx("h2",{className:"text-lg font-bold mb-4",children:"No Document Selected"}),a.jsx("p",{className:"mb-4",children:"Please select a document from the documents page or create a new one."}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[a.jsxs(ue,{variant:"default",className:"font-mono tracking-wide",onClick:()=>{window.location.href="./editor?new=true"},children:[a.jsx(La,{size:18,className:"mr-2"})," Create New Document"]}),a.jsx(ue,{variant:"outline",onClick:()=>{const x=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href=x.split("/editor")[0]+"/documents"},children:"Go to Documents"}),a.jsxs(ue,{variant:"secondary",onClick:()=>{window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"",window.location.href="./"},children:[a.jsx(St,{size:16,className:"mr-2"})," Back to Home"]})]})]}),a.jsx(di,{isLoading:I,message:"Generating documentation"})]})}export{gs as default};
