/**
 * Types related to documentation generation
 */

/**
 * Legacy parameter table item format
 */
export interface ParameterTableItem {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default_value?: string | null;
  constraints?: string | null;
  restrictions?: string | null;
}

/**
 * New parameter format from the API
 */
export interface Parameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  constraints?: string;
}

/**
 * Legacy function table item format
 */
export interface FunctionTableItem {
  name: string;
  description: string;
  parameters: ParameterTableItem[];
  return_type?: string;
  return_description?: string;
}

/**
 * Function information from the API
 */
export interface DocumentationFunction {
  name: string;
  description: string;
  parameters: string[];
  returns: string;
}

/**
 * Legacy workflow step format
 */
export interface WorkflowStep {
  step_number: number;
  description: string;
  input_data?: string[];
  output_data?: string[];
  processing?: string;
}

/**
 * Workflow step from the API
 */
export interface DocumentationWorkflowStep {
  id: number;
  name: string;
  description: string;
}

/**
 * Workflow container from the API
 */
export interface DocumentationWorkflow {
  steps: DocumentationWorkflowStep[];
}

/**
 * Documentation section for rendering
 */
export interface DocumentationSection {
  title: string;
  content: string;
  order: number;
  section_type: 'description' | 'parameters' | 'functions' | 'workflow' | 'code' | 'meta';
}

/**
 * Legacy model for the complete documentation data
 */
export interface LegacyDocumentationData {
  title: string;
  description: string;
  parameters_input: ParameterTableItem[];
  parameters_output: ParameterTableItem[];
  parameters_hardcoded: ParameterTableItem[];
  functions: FunctionTableItem[];
  workflow_steps: WorkflowStep[];
  original_code: string;
  meta_info: Record<string, string>;
  sections: DocumentationSection[];
}

/**
 * New model for the documentation response from the API
 */
export interface DocumentationResponse {
  textualDescription: string;
  parameters: Parameter[];
  workflow: DocumentationWorkflow;
  toolsUsed: string[];
  originalCode: string;
}

/**
 * Union type to support both old and new format
 */
export type DocumentationData = LegacyDocumentationData | DocumentationResponse;

/**
 * Request model for documentation generation
 */
export interface DocumentationRequest {
  code: string;
  language?: string;  // Programming language of the code
  title?: string;
  provider?: string;
  format?: string;
  documentationLanguage?: string;  // Language for the documentation (en, de, fr, it, es)
}

/**
 * Response model for documentation generation
 */
export interface DocumentationResponseData {
  is_valid: boolean;
  error_message?: string;
  title: string;
  documentation: string;
  documentation_data?: DocumentationData;
  provider_used?: string | null;
  language_detected: string;
  format: string;
}
