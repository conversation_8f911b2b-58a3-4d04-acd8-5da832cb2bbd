@tailwind base;

/* Monaco Editor Error <PERSON>light<PERSON> */
.error-line-highlight {
  background-color: rgba(255, 0, 0, 0.08) !important; /* Subtiler roter Hintergrund für die Zeile */
  /* Optional: Eine stärkere linke Border, falls gewünscht */
  /* border-left: 3px solid rgba(200, 0, 0, 0.6) !important; */
}

.error-glyph-margin {
  /* SVG für ein rotes Ausrufezeichen oder Kreis */
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%23dc2626" width="16" height="16"><path fill-rule="evenodd" d="M8 1.5a6.5 6.5 0 100 13A6.5 6.5 0 008 1.5zM0 8a8 8 0 1116 0A8 8 0 010 8zm9-4a1 1 0 00-2 0v4a1 1 0 102 0V4zm-1 5.5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"/></svg>') !important;
  background-repeat: no-repeat !important;
  background-position: center center !important;
  width: 18px !important; /* Breite des Glyphen-Bereichs */
}

/* Sorge dafür, dass Hover-Nachrichten (z.B. Fehlermeldungen) über anderen Elementen erscheinen */
.monaco-editor .monaco-hover {
  z-index: 100 !important; /* Sehr hoher z-index */
}


/* Monaco Editor Error Highlighting */
.error-line-highlight {
  background-color: rgba(255, 0, 0, 0.1) !important;
  border-left: 3px solid rgba(255, 0, 0, 0.7);
}

.error-glyph-margin {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%23ef4444" width="16" height="16"><path fill-rule="evenodd" d="M8 1.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5S1.5 11.59 1.5 8 4.41 1.5 8 1.5zM0 8c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8-8-3.58-8-8zm9-3H7v5h2V5zm0 6H7v2h2V11z" clip-rule="evenodd"/></svg>');
  background-repeat: no-repeat;
  background-position: center center;
  width: 16px !important; /* Ensure glyph takes space */
  height: 100%;
}

/* Ensure the content widget for errors (e.g. hover) is above other elements */
.monaco-editor .contentWidgets {
  z-index: 50 !important; /* Higher than most other elements */
}
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
    --primary-rgb: 50 55 60;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --primary-rgb: 230 235 240;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* GIS-inspired utility classes */
@layer utilities {
  /* Hexagonal clip paths */
  .clip-hex {
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  }
  
  /* Angled cuts for brutalist design */
  .cut-top-right {
    clip-path: polygon(0 0, 100% 15%, 100% 100%, 0 100%);
  }
  
  .cut-bottom-left {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 85%);
  }
  
  /* Glassmorphic effects */
  .glassmorphic {
    background: rgba(var(--primary-rgb), 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
  }
  
  .glassmorphic-hover {
    transition: all 0.3s ease;
  }
  
  .glassmorphic-hover:hover {
    background: rgba(var(--primary-rgb), 0.1);
    backdrop-filter: blur(12px);
  }
  
  /* GIS-inspired background with hexagonal grid pattern */
  .bg-gis-grid {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cpath d='M15 10L30 0L45 10L45 30L30 40L15 30L15 10Z' fill='none' stroke='hsl(var(--primary))' stroke-opacity='0.1' stroke-width='1'/%3E%3C/svg%3E");
    background-size: 60px 60px;
  }
  
  /* Functional brutalism angled borders */
  .angled-border-left {
    position: relative;
  }
  
  .angled-border-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: hsl(var(--primary));
    transform: skewX(-15deg);
  }
  
  /* Enhanced monospace font with better spacing for code */
  .font-code {
    font-family: "Fira Code", "JetBrains Mono", "SF Mono", Menlo, Monaco, Consolas, monospace;
    letter-spacing: -0.025em;
  }
}
