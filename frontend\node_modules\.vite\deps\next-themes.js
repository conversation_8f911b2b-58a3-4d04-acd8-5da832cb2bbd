"use client";
import {
  require_react
} from "./chunk-LXGCQ6UQ.js";
import {
  __toESM
} from "./chunk-ROME4SDB.js";

// node_modules/next-themes/dist/index.mjs
var t = __toESM(require_react(), 1);
var P = ["light", "dark"];
var E = "(prefers-color-scheme: dark)";
var Q = typeof window == "undefined";
var L = t.createContext(void 0);
var D = { setTheme: (e) => {
}, themes: [] };
var j = () => {
  var e;
  return (e = t.useContext(L)) != null ? e : D;
};
var z = (e) => t.useContext(L) ? e.children : t.createElement(O, { ...e });
var N = ["light", "dark"];
var O = ({ forcedTheme: e, disableTransitionOnChange: a = false, enableSystem: n = true, enableColorScheme: g = true, storageKey: m = "theme", themes: c = N, defaultTheme: o = n ? "system" : "light", attribute: y = "data-theme", value: h, children: k, nonce: w }) => {
  let [i, d] = t.useState(() => M(m, o)), [S, l] = t.useState(() => M(m)), u = h ? Object.values(h) : c, R = t.useCallback((s) => {
    let r = s;
    if (!r)
      return;
    s === "system" && n && (r = T());
    let v = h ? h[r] : r, C = a ? _() : null, x = document.documentElement;
    if (y === "class" ? (x.classList.remove(...u), v && x.classList.add(v)) : v ? x.setAttribute(y, v) : x.removeAttribute(y), g) {
      let I = P.includes(o) ? o : null, A = P.includes(r) ? r : I;
      x.style.colorScheme = A;
    }
    C == null || C();
  }, []), f = t.useCallback((s) => {
    let r = typeof s == "function" ? s(s) : s;
    d(r);
    try {
      localStorage.setItem(m, r);
    } catch (v) {
    }
  }, [e]), p = t.useCallback((s) => {
    let r = T(s);
    l(r), i === "system" && n && !e && R("system");
  }, [i, e]);
  t.useEffect(() => {
    let s = window.matchMedia(E);
    return s.addListener(p), p(s), () => s.removeListener(p);
  }, [p]), t.useEffect(() => {
    let s = (r) => {
      if (r.key !== m)
        return;
      let v = r.newValue || o;
      f(v);
    };
    return window.addEventListener("storage", s), () => window.removeEventListener("storage", s);
  }, [f]), t.useEffect(() => {
    R(e != null ? e : i);
  }, [e, i]);
  let $ = t.useMemo(() => ({ theme: i, setTheme: f, forcedTheme: e, resolvedTheme: i === "system" ? S : i, themes: n ? [...c, "system"] : c, systemTheme: n ? S : void 0 }), [i, f, e, S, n, c]);
  return t.createElement(L.Provider, { value: $ }, t.createElement(U, { forcedTheme: e, disableTransitionOnChange: a, enableSystem: n, enableColorScheme: g, storageKey: m, themes: c, defaultTheme: o, attribute: y, value: h, children: k, attrs: u, nonce: w }), k);
};
var U = t.memo(({ forcedTheme: e, storageKey: a, attribute: n, enableSystem: g, enableColorScheme: m, defaultTheme: c, value: o, attrs: y, nonce: h }) => {
  let k = c === "system", w = n === "class" ? `var d=document.documentElement,c=d.classList;${`c.remove(${y.map((u) => `'${u}'`).join(",")})`};` : `var d=document.documentElement,n='${n}',s='setAttribute';`, i = m ? (P.includes(c) ? c : null) ? `if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${c}'` : "if(e==='light'||e==='dark')d.style.colorScheme=e" : "", d = (l, u = false, R = true) => {
    let f = o ? o[l] : l, p = u ? l + "|| ''" : `'${f}'`, $ = "";
    return m && R && !u && P.includes(l) && ($ += `d.style.colorScheme = '${l}';`), n === "class" ? u || f ? $ += `c.add(${p})` : $ += "null" : f && ($ += `d[s](n,${p})`), $;
  }, S = e ? `!function(){${w}${d(e)}}()` : g ? `!function(){try{${w}var e=localStorage.getItem('${a}');if('system'===e||(!e&&${k})){var t='${E}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${o ? `var x=${JSON.stringify(o)};` : ""}${d(o ? "x[e]" : "e", true)}}${k ? "" : "else{" + d(c, false, false) + "}"}${i}}catch(e){}}()` : `!function(){try{${w}var e=localStorage.getItem('${a}');if(e){${o ? `var x=${JSON.stringify(o)};` : ""}${d(o ? "x[e]" : "e", true)}}else{${d(c, false, false)};}${i}}catch(t){}}();`;
  return t.createElement("script", { nonce: h, dangerouslySetInnerHTML: { __html: S } });
});
var M = (e, a) => {
  if (Q)
    return;
  let n;
  try {
    n = localStorage.getItem(e) || void 0;
  } catch (g) {
  }
  return n || a;
};
var _ = () => {
  let e = document.createElement("style");
  return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")), document.head.appendChild(e), () => {
    window.getComputedStyle(document.body), setTimeout(() => {
      document.head.removeChild(e);
    }, 1);
  };
};
var T = (e) => (e || (e = window.matchMedia(E)), e.matches ? "dark" : "light");
export {
  z as ThemeProvider,
  j as useTheme
};
//# sourceMappingURL=next-themes.js.map
