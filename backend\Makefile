.PHONY: install install-dev run test clean security-audit update-deps

# Production dependencies
install:
	uv pip install -e .

# Development dependencies (includes security tools)
install-dev:
	uv pip install -e ".[dev,security]"

# Update requirements.txt from pyproject.toml
update-deps:
	python scripts/generate_requirements.py

# Run the application
run:
	uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Run tests
test:
	python -m pytest

# Security audit
security-audit:
	python -m safety scan
	python -m bandit -r app/ -f json

# Clean up
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage

# Install and setup everything
setup: install-dev update-deps
	@echo "✅ Backend setup complete!"

# Check for dependency vulnerabilities and update if needed
check-deps: security-audit
	@echo "🔍 Dependency security check complete"

# Format code
format:
	python -m black app/
	python -m isort app/

# Lint code
lint:
	python -m flake8 app/
	python -m mypy app/

# Full quality check
quality: format lint test security-audit
	@echo "✅ Code quality check complete!"
