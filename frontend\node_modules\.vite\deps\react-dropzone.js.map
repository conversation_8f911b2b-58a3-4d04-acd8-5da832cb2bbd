{"version": 3, "sources": ["../../attr-accept/dist/es/index.js", "../../react-dropzone/dist/es/index.js", "../../file-selector/src/file.ts", "../../file-selector/src/file-selector.ts", "../../react-dropzone/dist/es/utils/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};", "var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils\";", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\n\n\nexport function toFileWithPath(file: FileWithPath, path?: string, h?: FileSystemHandle): FileWithPath {\n    const f = withMimeType(file);\n    const {webkitRelativePath} = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\n\nexport interface FileWithPath extends File {\n    readonly path?: string;\n    readonly handle?: FileSystemFileHandle;\n    readonly relativePath?: string;\n}\n\nfunction withMimeType(file: FileWithPath) {\n    const {name} = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop()!.toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n\n    return file;\n}\n\nfunction setObjProp(f: FileWithPath, key: string, value: string) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    })\n}\n", "import {FileWithPath, toFileWithPath} from './file';\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db'  // Windows\n];\n\n\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport async function fromEvent(evt: Event | any): Promise<(FileWithPath | DataTransferItem)[]> {\n    if (isObject<DragEvent>(evt) && isDataTransfer(evt.dataTransfer)) {\n        return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n        return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n        return getFsHandleFiles(evt)\n    }\n    return [];\n}\n\nfunction isDataTransfer(value: any): value is DataTransfer {\n    return isObject(value);\n}\n\nfunction isChangeEvt(value: any): value is Event {\n    return isObject<Event>(value) && isObject(value.target);\n}\n\nfunction isObject<T>(v: any): v is T {\n    return typeof v === 'object' && v !== null\n}\n\nfunction getInputFiles(evt: Event) {\n    return fromList<FileWithPath>((evt.target as HTMLInputElement).files).map(file => toFileWithPath(file));\n}\n\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nasync function getFsHandleFiles(handles: any[]) {\n    const files = await Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n}\n\n\nasync function getDataTransferFiles(dt: DataTransfer, type: string) {\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n        const items = fromList<DataTransferItem>(dt.items)\n            .filter(item => item.kind === 'file');\n        // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n        // only 'dragstart' and 'drop' has access to the data (source node)\n        if (type !== 'drop') {\n            return items;\n        }\n        const files = await Promise.all(items.map(toFilePromises));\n        return noIgnoredFiles(flatten<FileWithPath>(files));\n    }\n\n    return noIgnoredFiles(fromList<FileWithPath>(dt.files)\n        .map(file => toFileWithPath(file)));\n}\n\nfunction noIgnoredFiles(files: FileWithPath[]) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList<T>(items: DataTransferItemList | FileList | null): T[] {\n    if (items === null) {\n        return [];\n    }\n\n    const files = [];\n\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n\n    return files as any;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item: DataTransferItem) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n\n    const entry = item.webkitGetAsEntry();\n\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry) as any;\n    }\n\n    return fromDataTransferItem(item, entry);\n}\n\nfunction flatten<T>(items: any[]): T[] {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\n\nasync function fromDataTransferItem(item: DataTransferItem, entry?: FileSystemEntry | null) {\n    // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n    // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n    //\n    // See:\n    // - https://issues.chromium.org/issues/40186242\n    // - https://github.com/react-dropzone/react-dropzone/issues/1397\n    if (globalThis.isSecureContext && typeof (item as any).getAsFileSystemHandle === 'function') {\n        const h = await (item as any).getAsFileSystemHandle();\n        if (h === null) {\n            throw new Error(`${item} is not a File`);\n        }\n        // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n        // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n        if (h !== undefined) {\n            const file = await h.getFile();\n            file.handle = h;\n            return toFileWithPath(file);\n        }\n    }\n    const file = item.getAsFile();\n    if (!file) {\n        throw new Error(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file, entry?.fullPath ?? undefined);\n    return fwp;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nasync function fromEntry(entry: any) {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry: any) {\n    const reader = entry.createReader();\n\n    return new Promise<FileArray[]>((resolve, reject) => {\n        const entries: Promise<FileValue[]>[] = [];\n\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(async (batch: any[]) => {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = await Promise.all(entries);\n                        resolve(files);\n                    } catch (err) {\n                        reject(err);\n                    }\n                } else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n\n                    // Continue reading\n                    readEntries();\n                }\n            }, (err: any) => {\n                reject(err);\n            });\n        }\n\n        readEntries();\n    });\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nasync function fromFileEntry(entry: any) {\n    return new Promise<FileWithPath>((resolve, reject) => {\n        entry.file((file: FileWithPath) => {\n            const fwp = toFileWithPath(file, entry.fullPath);\n            resolve(fwp);\n        }, (err: any) => {\n            reject(err);\n        });\n    });\n}\n\n// Infinite type recursion\n// https://github.com/Microsoft/TypeScript/issues/3496#issuecomment-128553540\ninterface FileArray extends Array<FileValue> {}\ntype FileValue = FileWithPath\n    | FileArray[];\n", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,YAAQ,aAAa;AAErB,YAAQ,UAAU,SAAU,MAAM,eAAe;AAC/C,UAAI,QAAQ,eAAe;AACzB,YAAI,qBAAqB,MAAM,QAAQ,aAAa,IAAI,gBAAgB,cAAc,MAAM,GAAG;AAE/F,YAAI,mBAAmB,WAAW,GAAG;AACnC,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,KAAK,QAAQ;AAC5B,YAAI,YAAY,KAAK,QAAQ,IAAI,YAAY;AAC7C,YAAI,eAAe,SAAS,QAAQ,SAAS,EAAE;AAC/C,eAAO,mBAAmB,KAAK,SAAU,MAAM;AAC7C,cAAI,YAAY,KAAK,KAAK,EAAE,YAAY;AAExC,cAAI,UAAU,OAAO,CAAC,MAAM,KAAK;AAC/B,mBAAO,SAAS,YAAY,EAAE,SAAS,SAAS;AAAA,UAClD,WAAW,UAAU,SAAS,IAAI,GAAG;AAEnC,mBAAO,iBAAiB,UAAU,QAAQ,SAAS,EAAE;AAAA,UACvD;AAEA,iBAAO,aAAa;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACMA,mBAAsH;AACtH,wBAAsB;;;;;;ACrCf,IAAM,oBAAoB,oBAAI,IAAI;;EAErC,CAAC,OAAO,8CAA8C;EACtD,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,WAAW;EACnB,CAAC,MAAM,6BAA6B;EACpC,CAAC,QAAQ,6BAA6B;EACtC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,MAAM,qCAAqC;EAC5C,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,sCAAsC;EAC9C,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,0BAA0B;EAClC,CAAC,SAAS,yBAAyB;EACnC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,4BAA4B;EACpC,CAAC,SAAS,6BAA6B;EACvC,CAAC,MAAM,iBAAiB;EACxB,CAAC,OAAO,cAAc;EACtB,CAAC,QAAQ,cAAc;EACvB,CAAC,QAAQ,cAAc;EACvB,CAAC,OAAO,6DAA6D;EACrE,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,yCAAyC;EACjD,CAAC,QAAQ,YAAY;EACrB,CAAC,YAAY,qBAAqB;EAClC,CAAC,eAAe,8BAA8B;EAC9C,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,WAAW,yBAAyB;EACrC,CAAC,eAAe,6BAA6B;EAC7C,CAAC,WAAW,yBAAyB;EACrC,CAAC,OAAO,sCAAsC;EAC9C,CAAC,MAAM,YAAY;EACnB,CAAC,OAAO,iBAAiB;EACzB,CAAC,QAAQ,YAAY;EACrB,CAAC,MAAM,wBAAwB;EAC/B,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,0BAA0B;EAClC,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,iCAAiC;EACzC,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,mCAAmC;EAC5C,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,4BAA4B;EACrC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,0BAA0B;EACnC,CAAC,OAAO,qCAAqC;EAC7C,CAAC,QAAQ,gBAAgB;EACzB,CAAC,UAAU,0BAA0B;EACrC,CAAC,MAAM,oBAAoB;EAC3B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,KAAK,UAAU;EAChB,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,UAAU,8CAA8C;EACzD,CAAC,UAAU,kDAAkD;EAC7D,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,MAAM,UAAU;EACjB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,wBAAwB;EAChC,CAAC,SAAS,uBAAuB;EACjC,CAAC,WAAW,8BAA8B;EAC1C,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,SAAS,oCAAoC;EAC9C,CAAC,SAAS,6BAA6B;EACvC,CAAC,SAAS,4BAA4B;EACtC,CAAC,SAAS,yBAAyB;EACnC,CAAC,SAAS,yBAAyB;EACnC,CAAC,SAAS,wBAAwB;EAClC,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,gBAAgB;EACxB,CAAC,SAAS,8BAA8B;EACxC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,6BAA6B;EACrC,CAAC,QAAQ,4BAA4B;EACrC,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,wDAAwD;EAChE,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,0BAA0B;EAClC,CAAC,SAAS,0BAA0B;EACpC,CAAC,QAAQ,wCAAwC;EACjD,CAAC,QAAQ,uCAAuC;EAChD,CAAC,QAAQ,wCAAwC;EACjD,CAAC,QAAQ,wCAAwC;EACjD,CAAC,QAAQ,+BAA+B;EACxC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,6BAA6B;EACrC,CAAC,QAAQ,iBAAiB;EAC1B,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,yCAAyC;EACjD,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,yBAAyB;EACjC,CAAC,UAAU,mBAAmB;EAC9B,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,gCAAgC;EACxC,CAAC,cAAc,gCAAgC;EAC/C,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,0CAA0C;EAClD,CAAC,QAAQ,iBAAiB;EAC1B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,UAAU;EAClB,CAAC,MAAM,sBAAsB;EAC7B,CAAC,QAAQ,eAAe;EACxB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,4BAA4B;EACpC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,YAAY,2BAA2B;EACxC,CAAC,YAAY,0BAA0B;EACvC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,wBAAwB;EAChC,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,YAAY;EACpB,CAAC,UAAU,0BAA0B;EACrC,CAAC,OAAO,4BAA4B;EACpC,CAAC,QAAQ,8BAA8B;EACvC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,4BAA4B;EACpC,CAAC,4BAA4B,kCAAkC;EAC/D,CAAC,QAAQ,0BAA0B;EACnC,CAAC,SAAS,0BAA0B;EACpC,CAAC,OAAO,gBAAgB;EACxB,CAAC,QAAQ,gBAAgB;EACzB,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,kDAAkD;EAC3D,CAAC,QAAQ,yEAAyE;EAClF,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,kDAAkD;EAC3D,CAAC,QAAQ,yEAAyE;EAClF,CAAC,MAAM,yBAAyB;EAChC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,iBAAiB;EAC1B,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,eAAe;EACvB,CAAC,SAAS,kBAAkB;EAC5B,CAAC,QAAQ,0BAA0B;EACnC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,0BAA0B;EAClC,CAAC,aAAa,2BAA2B;EACzC,CAAC,aAAa,2BAA2B;EACzC,CAAC,aAAa,2BAA2B;EACzC,CAAC,QAAQ,wBAAwB;EACjC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,8BAA8B;EACtC,CAAC,QAAQ,wBAAwB;EACjC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,gBAAgB;EACxB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,aAAa,2BAA2B;EACzC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,8BAA8B;EACtC,CAAC,MAAM,0BAA0B;EACjC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,+BAA+B;EACvC,CAAC,KAAK,gBAAgB;EACtB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,yCAAyC;EAClD,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,aAAa,wCAAwC;EACtD,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,wBAAwB;EAChC,CAAC,MAAM,kBAAkB;EACzB,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,cAAc;EACvB,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,cAAc;EACtB,CAAC,MAAM,4BAA4B;EACnC,CAAC,OAAO,6BAA6B;EACrC,CAAC,MAAM,6CAA6C;EACpD,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,eAAe;EACvB,CAAC,SAAS,4BAA4B;EACtC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,qDAAqD;EAC7D,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,2BAA2B;EACnC,CAAC,QAAQ,2BAA2B;EACpC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,yBAAyB;EACjC,CAAC,MAAM,aAAa;EACpB,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,sCAAsC;EAC/C,CAAC,OAAO,yBAAyB;EACjC,CAAC,WAAW,sBAAsB;EAClC,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,yCAAyC;EACjD,CAAC,OAAO,mBAAmB;EAC3B,CAAC,QAAQ,iBAAiB;EAC1B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,YAAY,wBAAwB;EACrC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,UAAU,0BAA0B;EACrC,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,iCAAiC;EACzC,CAAC,SAAS,sBAAsB;EAChC,CAAC,OAAO,gCAAgC;EACxC,CAAC,UAAU,yCAAyC;EACpD,CAAC,WAAW,0CAA0C;EACtD,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,eAAe;EACvB,CAAC,MAAM,mBAAmB;EAC1B,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,yBAAyB;EACjC,CAAC,MAAM,kBAAkB;EACzB,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,KAAK,UAAU;EAChB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,QAAQ,YAAY;EACrB,CAAC,SAAS,qBAAqB;EAC/B,CAAC,QAAQ,YAAY;EACrB,CAAC,SAAS,qBAAqB;EAC/B,CAAC,QAAQ,aAAa;EACtB,CAAC,QAAQ,2BAA2B;EACpC,CAAC,MAAM,UAAU;EACjB,CAAC,SAAS,mBAAmB;EAC7B,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,yBAAyB;EAClC,CAAC,QAAQ,yBAAyB;EAClC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,kBAAkB;EAC1B,CAAC,QAAQ,4BAA4B;EACrC,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,cAAc;EACtB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,yCAAyC;EACjD,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,4CAA4C;EACpD,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,wBAAwB;EAChC,CAAC,MAAM,YAAY;EACnB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,uBAAuB;EAC/B,CAAC,SAAS,uBAAuB;EACjC,CAAC,WAAW,oCAAoC;EAChD,CAAC,QAAQ,uCAAuC;EAChD,CAAC,SAAS,mBAAmB;EAC7B,CAAC,OAAO,wCAAwC;EAChD,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,yCAAyC;EACjD,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,6CAA6C;EACrD,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,WAAW,iCAAiC;EAC7C,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,aAAa;EACrB,CAAC,QAAQ,8BAA8B;EACvC,CAAC,QAAQ,oCAAoC;EAC7C,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,WAAW;EACnB,CAAC,MAAM,wBAAwB;EAC/B,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,SAAS,mBAAmB;EAC7B,CAAC,UAAU,qBAAqB;;EAEhC,CAAC,SAAS,mBAAmB;EAC7B,CAAC,UAAU,yBAAyB;EACpC,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,YAAY;EACpB,CAAC,UAAU,4BAA4B;EACvC,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,wBAAwB;EACjC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,sCAAsC;EAC9C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,gCAAgC;EACxC,CAAC,QAAQ,6BAA6B;EACtC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,2BAA2B;EACnC,CAAC,UAAU,6BAA6B;EACxC,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,oDAAoD;EAC5D,CAAC,OAAO,yDAAyD;EACjE,CAAC,OAAO,mCAAmC;EAC3C,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,UAAU,oCAAoC;EAC/C,CAAC,QAAQ,YAAY;EACrB,CAAC,YAAY,4BAA4B;EACzC,CAAC,WAAW,4BAA4B;EACxC,CAAC,aAAa,mBAAmB;EACjC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,YAAY;EACpB,CAAC,WAAW,sBAAsB;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,4BAA4B;EACrC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,+BAA+B;EACxC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,kBAAkB;EAC1B,CAAC,MAAM,yBAAyB;EAChC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,yBAAyB;EAClC,CAAC,OAAO,8BAA8B;EACtC,CAAC,SAAS,4BAA4B;EACtC,CAAC,OAAO,YAAY;EACpB,CAAC,YAAY,qBAAqB;EAClC,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,0BAA0B;EAClC,CAAC,YAAY,eAAe;EAC5B,CAAC,UAAU,wBAAwB;EACnC,CAAC,MAAM,yBAAyB;EAChC,CAAC,OAAO,4BAA4B;EACpC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,SAAS,qBAAqB;EAC/B,CAAC,MAAM,eAAe;EACtB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,UAAU;EAClB,CAAC,MAAM,YAAY;EACnB,CAAC,QAAQ,YAAY;EACrB,CAAC,SAAS,2BAA2B;EACrC,CAAC,YAAY,0BAA0B;EACvC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,wCAAwC;EAChD,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,gBAAgB;EACzB,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,sCAAsC;EAC9C,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,uBAAuB;EAC/B,CAAC,QAAQ,gCAAgC;EACzC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,iBAAiB;EACzB,CAAC,SAAS,mBAAmB;EAC7B,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,iBAAiB;EAC1B,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,QAAQ,qCAAqC;EAC9C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,kBAAkB;EAC1B,CAAC,QAAQ,yBAAyB;EAClC,CAAC,MAAM,YAAY;EACnB,CAAC,SAAS,oCAAoC;EAC9C,CAAC,SAAS,4BAA4B;EACtC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,6BAA6B;EACtC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,yBAAyB;EAClC,CAAC,YAAY,wCAAwC;EACrD,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,oCAAoC;EAC5C,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,UAAU,8CAA8C;EACzD,CAAC,MAAM,SAAS;EAChB,CAAC,MAAM,yBAAyB;EAChC,CAAC,OAAO,gCAAgC;EACxC,CAAC,MAAM,sBAAsB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,YAAY;EACpB,CAAC,SAAS,mCAAmC;EAC7C,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,MAAM,qBAAqB;EAC5B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,6BAA6B;EACrC,CAAC,MAAM,uBAAuB;EAC9B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,WAAW,wCAAwC;EACpD,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,mCAAmC;EAC5C,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,6CAA6C;EACrD,CAAC,OAAO,0CAA0C;EAClD,CAAC,OAAO,4CAA4C;EACpD,CAAC,QAAQ,qDAAqD;EAC9D,CAAC,OAAO,6CAA6C;EACrD,CAAC,OAAO,0CAA0C;EAClD,CAAC,OAAO,gDAAgD;EACxD,CAAC,OAAO,iDAAiD;EACzD,CAAC,OAAO,gDAAgD;EACxD,CAAC,OAAO,yCAAyC;EACjD,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,mBAAmB;EAC5B,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,iBAAiB;EACzB,CAAC,SAAS,uBAAuB;EACjC,CAAC,UAAU,qBAAqB;EAChC,CAAC,UAAU,qBAAqB;EAChC,CAAC,UAAU,qBAAqB;EAChC,CAAC,WAAW,qBAAqB;EACjC,CAAC,OAAO,+BAA+B;EACvC,CAAC,QAAQ,aAAa;EACtB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,wCAAwC;EAChD,CAAC,UAAU,mDAAmD;EAC9D,CAAC,OAAO,wCAAwC;EAChD,CAAC,OAAO,mDAAmD;EAC3D,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,sDAAsD;EAC9D,CAAC,OAAO,6CAA6C;EACrD,CAAC,OAAO,mDAAmD;EAC3D,CAAC,OAAO,0DAA0D;EAClE,CAAC,OAAO,yDAAyD;EACjE,CAAC,OAAO,kDAAkD;EAC1D,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,yCAAyC;EACjD,CAAC,KAAK,eAAe;EACrB,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,6BAA6B;EACrC,CAAC,MAAM,mBAAmB;EAC1B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,mCAAmC;EAC3C,CAAC,SAAS,oCAAoC;EAC9C,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,8BAA8B;EACvC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,wBAAwB;EAChC,CAAC,SAAS,0BAA0B;EACpC,CAAC,OAAO,cAAc;EACtB,CAAC,SAAS,4BAA4B;EACtC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,yBAAyB;EAClC,CAAC,QAAQ,yBAAyB;EAClC,CAAC,QAAQ,gCAAgC;EACzC,CAAC,SAAS,yBAAyB;EACnC,CAAC,OAAO,cAAc;EACtB,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,WAAW,0BAA0B;EACtC,CAAC,UAAU,8BAA8B;EACzC,CAAC,MAAM,oBAAoB;EAC3B,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,MAAM,oBAAoB;EAC3B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,yBAAyB;EACjC,CAAC,WAAW,kCAAkC;EAC9C,CAAC,OAAO,+BAA+B;EACvC,CAAC,QAAQ,4DAA4D;EACrE,CAAC,QAAQ,uEAAuE;EAChF,CAAC,OAAO,+BAA+B;EACvC,CAAC,QAAQ,qDAAqD;EAC9D,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,+BAA+B;EACvC,CAAC,QAAQ,yDAAyD;EAClE,CAAC,QAAQ,wEAAwE;EACjF,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,4DAA4D;EACrE,CAAC,QAAQ,2EAA2E;EACpF,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,wBAAwB;EAChC,CAAC,SAAS,4BAA4B;EACtC,CAAC,MAAM,wBAAwB;EAC/B,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,8BAA8B;EACtC,CAAC,WAAW,sBAAsB;EAClC,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,2BAA2B;EACpC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,uCAAuC;EAC/C,CAAC,MAAM,iBAAiB;EACxB,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,MAAM,mBAAmB;EAC1B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,uBAAuB;EAChC,CAAC,QAAQ,2BAA2B;EACpC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,oBAAoB;EAC5B,CAAC,aAAa,uCAAuC;EACrD,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,iCAAiC;EACzC,CAAC,QAAQ,6BAA6B;EACtC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,qCAAqC;EAC7C,CAAC,MAAM,gCAAgC;EACvC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,qCAAqC;EAC7C,CAAC,MAAM,sBAAsB;EAC7B,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,uCAAuC;EAC/C,CAAC,QAAQ,kCAAkC;EAC3C,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,6BAA6B;EACrC,CAAC,QAAQ,qCAAqC;EAC9C,CAAC,QAAQ,oCAAoC;EAC7C,CAAC,MAAM,0BAA0B;EACjC,CAAC,MAAM,8BAA8B;EACrC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,2BAA2B;EACpC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,UAAU,8BAA8B;EACzC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,wBAAwB;EAChC,CAAC,QAAQ,2BAA2B;EACpC,CAAC,MAAM,wBAAwB;EAC/B,CAAC,KAAK,YAAY;EAClB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,mCAAmC;EAC3C,CAAC,QAAQ,aAAa;EACtB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,MAAM,sCAAsC;EAC7C,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,8BAA8B;EACtC,CAAC,QAAQ,aAAa;EACtB,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,sCAAsC;EAC9C,CAAC,QAAQ,iCAAiC;EAC1C,CAAC,QAAQ,iCAAiC;EAC1C,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,2BAA2B;EACpC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,UAAU,uBAAuB;EAClC,CAAC,WAAW,wBAAwB;EACpC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,UAAU,oCAAoC;EAC/C,CAAC,UAAU,yCAAyC;EACpD,CAAC,aAAa,sCAAsC;EACpD,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,4CAA4C;EACpD,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,WAAW;EACpB,CAAC,MAAM,kBAAkB;EACzB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,SAAS,WAAW;EACrB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,SAAS,mBAAmB;EAC7B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,iCAAiC;EACzC,CAAC,QAAQ,iCAAiC;EAC1C,CAAC,OAAO,uBAAuB;EAC/B,CAAC,QAAQ,wBAAwB;EACjC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,qDAAqD;EAC9D,CAAC,QAAQ,oEAAoE;EAC7E,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,4BAA4B;EACpC,CAAC,MAAM,qCAAqC;EAC5C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,OAAO,kBAAkB;EAC1B,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,aAAa;EACrB,CAAC,SAAS,mCAAmC;EAC7C,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,wBAAwB;EAChC,CAAC,MAAM,0BAA0B;EACjC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,QAAQ,WAAW;EACpB,CAAC,OAAO,oCAAoC;EAC5C,CAAC,OAAO,4BAA4B;EACpC,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,gCAAgC;EACxC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,2BAA2B;EACnC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,0BAA0B;EAClC,CAAC,MAAM,sCAAsC;EAC7C,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,0CAA0C;EAClD,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,WAAW;EACnB,CAAC,QAAQ,gBAAgB;EACzB,CAAC,SAAS,oBAAoB;EAC9B,CAAC,QAAQ,gBAAgB;EACzB,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,yCAAyC;EACjD,CAAC,QAAQ,aAAa;EACtB,CAAC,UAAU,aAAa;EACxB,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,8BAA8B;EACtC,CAAC,QAAQ,8BAA8B;EACvC,CAAC,WAAW,uBAAuB;EACnC,CAAC,UAAU,sBAAsB;EACjC,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,eAAe;EACxB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,oCAAoC;EAC5C,CAAC,WAAW,sBAAsB;EAClC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,uCAAuC;EAC/C,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,gCAAgC;EACxC,CAAC,KAAK,YAAY;EAClB,CAAC,MAAM,0BAA0B;EACjC,CAAC,OAAO,WAAW;EACnB,CAAC,UAAU,uBAAuB;EAClC,CAAC,OAAO,2CAA2C;EACnD,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,QAAQ,4BAA4B;EACrC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,MAAM,gCAAgC;EACvC,CAAC,WAAW,+BAA+B;EAC3C,CAAC,OAAO,qBAAqB;EAC7B,CAAC,aAAa,qBAAqB;EACnC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,QAAQ,uBAAuB;EAChC,CAAC,WAAW,uBAAuB;EACnC,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,eAAe;EACvB,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,mBAAmB;EAC3B,CAAC,QAAQ,gCAAgC;EACzC,CAAC,OAAO,YAAY;EACpB,CAAC,QAAQ,YAAY;EACrB,CAAC,MAAM,mBAAmB;EAC1B,CAAC,OAAO,gCAAgC;EACxC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,WAAW,0BAA0B;EACtC,CAAC,OAAO,sCAAsC;EAC9C,CAAC,OAAO,0BAA0B;EAClC,CAAC,MAAM,YAAY;EACnB,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,0BAA0B;EAClC,CAAC,MAAM,YAAY;EACnB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,aAAa;EACrB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,oCAAoC;EAC5C,CAAC,QAAQ,oCAAoC;EAC7C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,4BAA4B;EACpC,CAAC,OAAO,YAAY;EACpB,CAAC,SAAS,gCAAgC;EAC1C,CAAC,SAAS,wBAAwB;EAClC,CAAC,SAAS,yCAAyC;EACnD,CAAC,SAAS,gBAAgB;EAC1B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,8BAA8B;EACvC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,wBAAwB;EAChC,CAAC,YAAY,uBAAuB;EACpC,CAAC,QAAQ,0BAA0B;EACnC,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,eAAe;EACxB,CAAC,QAAQ,eAAe;EACxB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,SAAS,qBAAqB;EAC/B,CAAC,OAAO,2BAA2B;EACnC,CAAC,MAAM,iBAAiB;EACxB,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,2BAA2B;EACpC,CAAC,QAAQ,2BAA2B;EACpC,CAAC,QAAQ,wBAAwB;EACjC,CAAC,QAAQ,mBAAmB;EAC5B,CAAC,QAAQ,wBAAwB;EACjC,CAAC,QAAQ,uBAAuB;EAChC,CAAC,QAAQ,mBAAmB;EAC5B,CAAC,QAAQ,mBAAmB;EAC5B,CAAC,QAAQ,+BAA+B;EACxC,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,QAAQ,kCAAkC;EAC3C,CAAC,QAAQ,0BAA0B;EACnC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,+BAA+B;EACxC,CAAC,gBAAgB,uCAAuC;EACxD,CAAC,SAAS,YAAY;EACtB,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,cAAc;EACtB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,kBAAkB;EAC1B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,mBAAmB;EAC3B,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,sBAAsB;EAC9B,CAAC,QAAQ,+BAA+B;EACxC,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,8BAA8B;EACtC,CAAC,QAAQ,YAAY;EACrB,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,UAAU;EAClB,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,0BAA0B;EACnC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,8BAA8B;EACvC,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,aAAa;EACrB,CAAC,OAAO,gBAAgB;EACxB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,uCAAuC;EAC/C,CAAC,SAAS,mBAAmB;EAC7B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,YAAY;EACrB,CAAC,UAAU,qCAAqC;EAChD,CAAC,QAAQ,YAAY;EACrB,CAAC,eAAe,2BAA2B;EAC3C,CAAC,QAAQ,YAAY;EACrB,CAAC,MAAM,4BAA4B;EACnC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,OAAO,0BAA0B;EAClC,CAAC,MAAM,eAAe;EACtB,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,kBAAkB;EAC1B,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,QAAQ,wBAAwB;EACjC,CAAC,SAAS,gCAAgC;EAC1C,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,WAAW;EACpB,CAAC,SAAS,YAAY;EACtB,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,6BAA6B;EACrC,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,YAAY;EACpB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,YAAY,0BAA0B;EACvC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,gBAAgB;EACxB,CAAC,OAAO,eAAe;EACvB,CAAC,QAAQ,uBAAuB;EAChC,CAAC,SAAS,kBAAkB;EAC5B,CAAC,QAAQ,gBAAgB;EACzB,CAAC,SAAS,gBAAgB;EAC1B,CAAC,QAAQ,eAAe;EACxB,CAAC,OAAO,8BAA8B;EACtC,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,mCAAmC;EAC3C,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,sBAAsB;EAC9B,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,uBAAuB;EAChC,CAAC,OAAO,4CAA4C;EACpD,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,2BAA2B;EACnC,CAAC,OAAO,+BAA+B;EACvC,CAAC,OAAO,+BAA+B;EACvC,CAAC,SAAS,sBAAsB;EAChC,CAAC,OAAO,qCAAqC;EAC7C,CAAC,OAAO,yBAAyB;EACjC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,iCAAiC;EACzC,CAAC,QAAQ,4BAA4B;EACrC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,uBAAuB;EAC/B,CAAC,SAAS,uBAAuB;EACjC,CAAC,SAAS,oBAAoB;EAC9B,CAAC,OAAO,gBAAgB;EACxB,CAAC,MAAM,mBAAmB;EAC1B,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,gDAAgD;EACzD,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,0BAA0B;EAClC,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,uDAAuD;EAChE,CAAC,QAAQ,gDAAgD;EACzD,CAAC,QAAQ,mEAAmE;EAC5E,CAAC,OAAO,0BAA0B;EAClC,CAAC,QAAQ,mDAAmD;EAC5D,CAAC,QAAQ,sEAAsE;EAC/E,CAAC,OAAO,0BAA0B;EAClC,CAAC,MAAM,UAAU;EACjB,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,yBAAyB;EACjC,CAAC,MAAM,4BAA4B;EACnC,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,yBAAyB;EACjC,CAAC,OAAO,uBAAuB;EAC/B,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,wBAAwB;EAChC,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,kCAAkC;EAC1C,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,iBAAiB;EACzB,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,4BAA4B;EACpC,CAAC,QAAQ,sBAAsB;EAC/B,CAAC,OAAO,iCAAiC;EACzC,CAAC,OAAO,oBAAoB;EAC5B,CAAC,QAAQ,oBAAoB;EAC7B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,gBAAgB;EACxB,CAAC,MAAM,kBAAkB;EACzB,CAAC,QAAQ,WAAW;EACpB,CAAC,QAAQ,kBAAkB;EAC3B,CAAC,OAAO,qBAAqB;EAC7B,CAAC,OAAO,WAAW;EACnB,CAAC,OAAO,iBAAiB;EACzB,CAAC,KAAK,wBAAwB;EAC9B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,MAAM,wBAAwB;EAC/B,CAAC,OAAO,gCAAgC;EACxC,CAAC,OAAO,iBAAiB;EACzB,CAAC,OAAO,qBAAqB;EAC7B,CAAC,QAAQ,qBAAqB;EAC9B,CAAC,OAAO,4CAA4C;EACpD,CAAC,OAAO,kBAAkB;CAC7B;AAGK,SAAU,eAAe,MAAoB,MAAe,GAAoB;AAClF,QAAM,IAAI,aAAa,IAAI;AAC3B,QAAM,EAAC,mBAAkB,IAAI;AAC7B,QAAM,IAAI,OAAO,SAAS,WACpB,OAIA,OAAO,uBAAuB,YAAY,mBAAmB,SAAS,IAClE,qBACA,KAAK,KAAK,IAAI;AACxB,MAAI,OAAO,EAAE,SAAS,UAAU;AAC5B,eAAW,GAAG,QAAQ,CAAC;EAC3B;AACA,MAAI,MAAM,QAAW;AACjB,WAAO,eAAe,GAAG,UAAU;MAC/B,OAAO;MACP,UAAU;MACV,cAAc;MACd,YAAY;KACf;EACL;AAEA,aAAW,GAAG,gBAAgB,CAAC;AAC/B,SAAO;AACX;AAQA,SAAS,aAAa,MAAkB;AACpC,QAAM,EAAC,KAAI,IAAI;AACf,QAAM,eAAe,QAAQ,KAAK,YAAY,GAAG,MAAM;AAEvD,MAAI,gBAAgB,CAAC,KAAK,MAAM;AAC5B,UAAM,MAAM,KAAK,MAAM,GAAG,EACrB,IAAG,EAAI,YAAW;AACvB,UAAM,OAAO,kBAAkB,IAAI,GAAG;AACtC,QAAI,MAAM;AACN,aAAO,eAAe,MAAM,QAAQ;QAChC,OAAO;QACP,UAAU;QACV,cAAc;QACd,YAAY;OACf;IACL;EACJ;AAEA,SAAO;AACX;AAEA,SAAS,WAAW,GAAiB,KAAa,OAAa;AAC3D,SAAO,eAAe,GAAG,KAAK;IAC1B;IACA,UAAU;IACV,cAAc;IACd,YAAY;GACf;AACL;;;AC/uCA,IAAM,kBAAkB;;EAEpB;;EACA;;;AAcE,SAAgB,UAAU,KAAgB;;AAC5C,QAAI,SAAoB,GAAG,KAAK,eAAe,IAAI,YAAY,GAAG;AAC9D,aAAO,qBAAqB,IAAI,cAAc,IAAI,IAAI;IAC1D,WAAW,YAAY,GAAG,GAAG;AACzB,aAAO,cAAc,GAAG;IAC5B,WAAW,MAAM,QAAQ,GAAG,KAAK,IAAI,MAAM,UAAQ,aAAa,QAAQ,OAAO,KAAK,YAAY,UAAU,GAAG;AACzG,aAAO,iBAAiB,GAAG;IAC/B;AACA,WAAO,CAAA;EACX,CAAC;;AAED,SAAS,eAAe,OAAU;AAC9B,SAAO,SAAS,KAAK;AACzB;AAEA,SAAS,YAAY,OAAU;AAC3B,SAAO,SAAgB,KAAK,KAAK,SAAS,MAAM,MAAM;AAC1D;AAEA,SAAS,SAAY,GAAM;AACvB,SAAO,OAAO,MAAM,YAAY,MAAM;AAC1C;AAEA,SAAS,cAAc,KAAU;AAC7B,SAAO,SAAwB,IAAI,OAA4B,KAAK,EAAE,IAAI,UAAQ,eAAe,IAAI,CAAC;AAC1G;AAGA,SAAe,iBAAiB,SAAc;;AAC1C,UAAM,QAAQ,MAAM,QAAQ,IAAI,QAAQ,IAAI,OAAK,EAAE,QAAO,CAAE,CAAC;AAC7D,WAAO,MAAM,IAAI,UAAQ,eAAe,IAAI,CAAC;EACjD,CAAC;;AAGD,SAAe,qBAAqB,IAAkB,MAAY;;AAG9D,QAAI,GAAG,OAAO;AACV,YAAM,QAAQ,SAA2B,GAAG,KAAK,EAC5C,OAAO,UAAQ,KAAK,SAAS,MAAM;AAGxC,UAAI,SAAS,QAAQ;AACjB,eAAO;MACX;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;AACzD,aAAO,eAAe,QAAsB,KAAK,CAAC;IACtD;AAEA,WAAO,eAAe,SAAuB,GAAG,KAAK,EAChD,IAAI,UAAQ,eAAe,IAAI,CAAC,CAAC;EAC1C,CAAC;;AAED,SAAS,eAAe,OAAqB;AACzC,SAAO,MAAM,OAAO,UAAQ,gBAAgB,QAAQ,KAAK,IAAI,MAAM,EAAE;AACzE;AAMA,SAAS,SAAY,OAA6C;AAC9D,MAAI,UAAU,MAAM;AAChB,WAAO,CAAA;EACX;AAEA,QAAM,QAAQ,CAAA;AAGd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,KAAK,IAAI;EACnB;AAEA,SAAO;AACX;AAGA,SAAS,eAAe,MAAsB;AAC1C,MAAI,OAAO,KAAK,qBAAqB,YAAY;AAC7C,WAAO,qBAAqB,IAAI;EACpC;AAEA,QAAM,QAAQ,KAAK,iBAAgB;AAKnC,MAAI,SAAS,MAAM,aAAa;AAC5B,WAAO,aAAa,KAAK;EAC7B;AAEA,SAAO,qBAAqB,MAAM,KAAK;AAC3C;AAEA,SAAS,QAAW,OAAY;AAC5B,SAAO,MAAM,OAAO,CAAC,KAAK,UAAU;IAChC,GAAG;IACH,GAAI,MAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK;KACnD,CAAA,CAAE;AACT;AAEA,SAAe,qBAAqB,MAAwB,OAA8B;;;AAOtF,QAAI,WAAW,mBAAmB,OAAQ,KAAa,0BAA0B,YAAY;AACzF,YAAM,IAAI,MAAO,KAAa,sBAAqB;AACnD,UAAI,MAAM,MAAM;AACZ,cAAM,IAAI,MAAM,GAAG,IAAI,gBAAgB;MAC3C;AAGA,UAAI,MAAM,QAAW;AACjB,cAAMA,QAAO,MAAM,EAAE,QAAO;AAC5B,QAAAA,MAAK,SAAS;AACd,eAAO,eAAeA,KAAI;MAC9B;IACJ;AACA,UAAM,OAAO,KAAK,UAAS;AAC3B,QAAI,CAAC,MAAM;AACP,YAAM,IAAI,MAAM,GAAG,IAAI,gBAAgB;IAC3C;AACA,UAAM,MAAM,eAAe,OAAM,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,cAAQ,QAAA,OAAA,SAAA,KAAI,MAAS;AAC7D,WAAO;EACX,CAAC;;AAGD,SAAe,UAAU,OAAU;;AAC/B,WAAO,MAAM,cAAc,aAAa,KAAK,IAAI,cAAc,KAAK;EACxE,CAAC;;AAGD,SAAS,aAAa,OAAU;AAC5B,QAAM,SAAS,MAAM,aAAY;AAEjC,SAAO,IAAI,QAAqB,CAAC,SAAS,WAAU;AAChD,UAAM,UAAkC,CAAA;AAExC,aAAS,cAAW;AAGhB,aAAO,YAAY,CAAO,UAAgB,UAAA,MAAA,QAAA,QAAA,aAAA;AACtC,YAAI,CAAC,MAAM,QAAQ;AAEf,cAAI;AACA,kBAAM,QAAQ,MAAM,QAAQ,IAAI,OAAO;AACvC,oBAAQ,KAAK;UACjB,SAAS,KAAK;AACV,mBAAO,GAAG;UACd;QACJ,OAAO;AACH,gBAAM,QAAQ,QAAQ,IAAI,MAAM,IAAI,SAAS,CAAC;AAC9C,kBAAQ,KAAK,KAAK;AAGlB,sBAAW;QACf;MACJ,CAAC,GAAE,CAAC,QAAY;AACZ,eAAO,GAAG;MACd,CAAC;IACL;AAEA,gBAAW;EACf,CAAC;AACL;AAGA,SAAe,cAAc,OAAU;;AACnC,WAAO,IAAI,QAAsB,CAAC,SAAS,WAAU;AACjD,YAAM,KAAK,CAAC,SAAsB;AAC9B,cAAM,MAAM,eAAe,MAAM,MAAM,QAAQ;AAC/C,gBAAQ,GAAG;MACf,GAAG,CAAC,QAAY;AACZ,eAAO,GAAG;MACd,CAAC;IACL,CAAC;EACL,CAAC;;;;AC5KD,yBAAqB;AA5BrB,SAAS,mBAAmB,KAAK;AAAE,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AAAG;AAExJ,SAAS,qBAAqB;AAAE,QAAM,IAAI,UAAU,sIAAsI;AAAG;AAE7L,SAAS,iBAAiB,MAAM;AAAE,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK;AAAM,WAAO,MAAM,KAAK,IAAI;AAAG;AAE7J,SAAS,mBAAmB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO,kBAAkB,GAAG;AAAG;AAE1F,SAAS,QAAQ,QAAQ,gBAAgB;AAAE,MAAI,OAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAAG;AAAE,SAAO;AAAM;AAEpV,SAAS,cAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAEzf,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAIhN,SAAS,eAAe,KAAK,GAAG;AAAE,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAAG;AAE7J,SAAS,mBAAmB;AAAE,QAAM,IAAI,UAAU,2IAA2I;AAAG;AAEhM,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC;AAAG;AAAQ,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AAAG;AAE/Z,SAAS,kBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK;AAAE,SAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAAG;AAAE,SAAO;AAAM;AAEtL,SAAS,sBAAsB,KAAK,GAAG;AAAE,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAAG,MAAI,MAAM;AAAM;AAAQ,MAAI,OAAO,CAAC;AAAG,MAAI,KAAK;AAAM,MAAI,KAAK;AAAO,MAAI,IAAI;AAAI,MAAI;AAAE,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAAE,WAAK,KAAK,GAAG,KAAK;AAAG,UAAI,KAAK,KAAK,WAAW;AAAG;AAAA,IAAO;AAAA,EAAE,SAAS,KAAK;AAAE,SAAK;AAAM,SAAK;AAAA,EAAK,UAAE;AAAU,QAAI;AAAE,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;AAAM,WAAG,QAAQ,EAAE;AAAA,IAAG,UAAE;AAAU,UAAI;AAAI,cAAM;AAAA,IAAI;AAAA,EAAE;AAAE,SAAO;AAAM;AAEhgB,SAAS,gBAAgB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO;AAAK;AAGpE,IAAI,UAAU,OAAO,mBAAAC,YAAa,aAAa,mBAAAA,UAAW,mBAAAA,QAAS;AAE5D,IAAI,oBAAoB;AACxB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAAA,EACrB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAChB;AAMO,IAAI,6BAA6B,SAASC,8BAA6B;AAC5E,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAI,YAAY,OAAO,MAAM,GAAG;AAChC,MAAI,MAAM,UAAU,SAAS,IAAI,UAAU,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AACrF,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,qBAAqB,OAAO,GAAG;AAAA,EAC1C;AACF;AACO,IAAI,0BAA0B,SAASC,yBAAwB,SAAS;AAC7E,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,uBAAuB,OAAO,SAAS,GAAG,EAAE,OAAO,YAAY,IAAI,SAAS,OAAO;AAAA,EAC9F;AACF;AACO,IAAI,0BAA0B,SAASC,yBAAwB,SAAS;AAC7E,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,wBAAwB,OAAO,SAAS,GAAG,EAAE,OAAO,YAAY,IAAI,SAAS,OAAO;AAAA,EAC/F;AACF;AACO,IAAI,2BAA2B;AAAA,EACpC,MAAM;AAAA,EACN,SAAS;AACX;AAYO,SAAS,aAAa,MAAM,QAAQ;AACzC,MAAI,eAAe,KAAK,SAAS,4BAA4B,QAAQ,MAAM,MAAM;AACjF,SAAO,CAAC,cAAc,eAAe,OAAO,2BAA2B,MAAM,CAAC;AAChF;AACO,SAAS,cAAc,MAAM,SAAS,SAAS;AACpD,MAAI,UAAU,KAAK,IAAI,GAAG;AACxB,QAAI,UAAU,OAAO,KAAK,UAAU,OAAO,GAAG;AAC5C,UAAI,KAAK,OAAO;AAAS,eAAO,CAAC,OAAO,wBAAwB,OAAO,CAAC;AACxE,UAAI,KAAK,OAAO;AAAS,eAAO,CAAC,OAAO,wBAAwB,OAAO,CAAC;AAAA,IAC1E,WAAW,UAAU,OAAO,KAAK,KAAK,OAAO;AAAS,aAAO,CAAC,OAAO,wBAAwB,OAAO,CAAC;AAAA,aAAW,UAAU,OAAO,KAAK,KAAK,OAAO;AAAS,aAAO,CAAC,OAAO,wBAAwB,OAAO,CAAC;AAAA,EAC5M;AAEA,SAAO,CAAC,MAAM,IAAI;AACpB;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAa,UAAU;AAC1C;AAeO,SAAS,iBAAiB,MAAM;AACrC,MAAI,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,UAAU,KAAK,SACf,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,YAAY,KAAK;AAErB,MAAI,CAAC,YAAY,MAAM,SAAS,KAAK,YAAY,YAAY,KAAK,MAAM,SAAS,UAAU;AACzF,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,MAAM,SAAU,MAAM;AACjC,QAAI,gBAAgB,aAAa,MAAM,MAAM,GACzC,iBAAiB,eAAe,eAAe,CAAC,GAChD,WAAW,eAAe,CAAC;AAE/B,QAAI,iBAAiB,cAAc,MAAM,SAAS,OAAO,GACrD,kBAAkB,eAAe,gBAAgB,CAAC,GAClD,YAAY,gBAAgB,CAAC;AAEjC,QAAI,eAAe,YAAY,UAAU,IAAI,IAAI;AACjD,WAAO,YAAY,aAAa,CAAC;AAAA,EACnC,CAAC;AACH;AAIO,SAAS,qBAAqB,OAAO;AAC1C,MAAI,OAAO,MAAM,yBAAyB,YAAY;AACpD,WAAO,MAAM,qBAAqB;AAAA,EACpC,WAAW,OAAO,MAAM,iBAAiB,aAAa;AACpD,WAAO,MAAM;AAAA,EACf;AAEA,SAAO;AACT;AACO,SAAS,eAAe,OAAO;AACpC,MAAI,CAAC,MAAM,cAAc;AACvB,WAAO,CAAC,CAAC,MAAM,UAAU,CAAC,CAAC,MAAM,OAAO;AAAA,EAC1C;AAIA,SAAO,MAAM,UAAU,KAAK,KAAK,MAAM,aAAa,OAAO,SAAU,MAAM;AACzE,WAAO,SAAS,WAAW,SAAS;AAAA,EACtC,CAAC;AACH;AAKO,SAAS,mBAAmB,OAAO;AACxC,QAAM,eAAe;AACvB;AAEA,SAAS,KAAK,WAAW;AACvB,SAAO,UAAU,QAAQ,MAAM,MAAM,MAAM,UAAU,QAAQ,UAAU,MAAM;AAC/E;AAEA,SAAS,OAAO,WAAW;AACzB,SAAO,UAAU,QAAQ,OAAO,MAAM;AACxC;AAEO,SAAS,aAAa;AAC3B,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,OAAO,UAAU;AACrG,SAAO,KAAK,SAAS,KAAK,OAAO,SAAS;AAC5C;AAYO,SAAS,uBAAuB;AACrC,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AAEA,SAAO,SAAU,OAAO;AACtB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,WAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,IACnC;AAEA,WAAO,IAAI,KAAK,SAAU,IAAI;AAC5B,UAAI,CAAC,qBAAqB,KAAK,KAAK,IAAI;AACtC,WAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,MACvC;AAEA,aAAO,qBAAqB,KAAK;AAAA,IACnC,CAAC;AAAA,EACH;AACF;AAOO,SAAS,4BAA4B;AAC1C,SAAO,wBAAwB;AACjC;AASO,SAAS,wBAAwB,QAAQ;AAC9C,MAAI,UAAU,MAAM,GAAG;AACrB,QAAI,kBAAkB,OAAO,QAAQ,MAAM,EAAE,OAAO,SAAU,OAAO;AACnE,UAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,WAAW,MAAM,CAAC,GAClB,MAAM,MAAM,CAAC;AAEjB,UAAI,KAAK;AAET,UAAI,CAAC,WAAW,QAAQ,GAAG;AACzB,gBAAQ,KAAK,YAAa,OAAO,UAAU,uKAAwK,CAAC;AACpN,aAAK;AAAA,MACP;AAEA,UAAI,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,MAAM,KAAK,GAAG;AAC5C,gBAAQ,KAAK,YAAa,OAAO,UAAU,mDAAoD,CAAC;AAChG,aAAK;AAAA,MACP;AAEA,aAAO;AAAA,IACT,CAAC,EAAE,OAAO,SAAU,KAAK,OAAO;AAC9B,UAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,WAAW,MAAM,CAAC,GAClB,MAAM,MAAM,CAAC;AAEjB,aAAO,cAAc,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,UAAU,GAAG,CAAC;AAAA,IACrF,GAAG,CAAC,CAAC;AACL,WAAO,CAAC;AAAA;AAAA,MAEN,aAAa;AAAA,MACb,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAOO,SAAS,uBAAuB,QAAQ;AAC7C,MAAI,UAAU,MAAM,GAAG;AACrB,WAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,SAAU,GAAG,OAAO;AACvD,UAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,WAAW,MAAM,CAAC,GAClB,MAAM,MAAM,CAAC;AAEjB,aAAO,CAAC,EAAE,OAAO,mBAAmB,CAAC,GAAG,CAAC,QAAQ,GAAG,mBAAmB,GAAG,CAAC;AAAA,IAC7E,GAAG,CAAC,CAAC,EACJ,OAAO,SAAU,GAAG;AACnB,aAAO,WAAW,CAAC,KAAK,MAAM,CAAC;AAAA,IACjC,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,SAAO;AACT;AASO,SAAS,QAAQ,GAAG;AACzB,SAAO,aAAa,iBAAiB,EAAE,SAAS,gBAAgB,EAAE,SAAS,EAAE;AAC/E;AASO,SAAS,gBAAgB,GAAG;AACjC,SAAO,aAAa,iBAAiB,EAAE,SAAS,mBAAmB,EAAE,SAAS,EAAE;AAClF;AASO,SAAS,WAAW,GAAG;AAC5B,SAAO,MAAM,aAAa,MAAM,aAAa,MAAM,aAAa,MAAM,YAAY,MAAM,mBAAmB,iBAAiB,KAAK,CAAC;AACpI;AAMO,SAAS,MAAM,GAAG;AACvB,SAAO,cAAc,KAAK,CAAC;AAC7B;;;AHxUA,IAAI,YAAY,CAAC,UAAU;AAA3B,IACI,aAAa,CAAC,MAAM;AADxB,IAEI,aAAa,CAAC,UAAU,QAAQ,aAAa,WAAW,UAAU,WAAW,eAAe,cAAc,eAAe,QAAQ;AAFrI,IAGI,aAAa,CAAC,UAAU,YAAY,SAAS;AAEjD,SAASC,oBAAmB,KAAK;AAAE,SAAOC,oBAAmB,GAAG,KAAKC,kBAAiB,GAAG,KAAKC,6BAA4B,GAAG,KAAKC,oBAAmB;AAAG;AAExJ,SAASA,sBAAqB;AAAE,QAAM,IAAI,UAAU,sIAAsI;AAAG;AAE7L,SAASF,kBAAiB,MAAM;AAAE,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK;AAAM,WAAO,MAAM,KAAK,IAAI;AAAG;AAE7J,SAASD,oBAAmB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAOI,mBAAkB,GAAG;AAAG;AAE1F,SAASC,gBAAe,KAAK,GAAG;AAAE,SAAOC,iBAAgB,GAAG,KAAKC,uBAAsB,KAAK,CAAC,KAAKL,6BAA4B,KAAK,CAAC,KAAKM,kBAAiB;AAAG;AAE7J,SAASA,oBAAmB;AAAE,QAAM,IAAI,UAAU,2IAA2I;AAAG;AAEhM,SAASN,6BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC;AAAG;AAAQ,MAAI,OAAO,MAAM;AAAU,WAAOE,mBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAOA,mBAAkB,GAAG,MAAM;AAAG;AAE/Z,SAASA,mBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK;AAAE,SAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAAG;AAAE,SAAO;AAAM;AAEtL,SAASG,uBAAsB,KAAK,GAAG;AAAE,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAAG,MAAI,MAAM;AAAM;AAAQ,MAAI,OAAO,CAAC;AAAG,MAAI,KAAK;AAAM,MAAI,KAAK;AAAO,MAAI,IAAI;AAAI,MAAI;AAAE,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAAE,WAAK,KAAK,GAAG,KAAK;AAAG,UAAI,KAAK,KAAK,WAAW;AAAG;AAAA,IAAO;AAAA,EAAE,SAAS,KAAK;AAAE,SAAK;AAAM,SAAK;AAAA,EAAK,UAAE;AAAU,QAAI;AAAE,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;AAAM,WAAG,QAAQ,EAAE;AAAA,IAAG,UAAE;AAAU,UAAI;AAAI,cAAM;AAAA,IAAI;AAAA,EAAE;AAAE,SAAO;AAAM;AAEhgB,SAASD,iBAAgB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO;AAAK;AAEpE,SAASG,SAAQ,QAAQ,gBAAgB;AAAE,MAAI,OAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAAG;AAAE,SAAO;AAAM;AAEpV,SAASC,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAID,SAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,MAAAE,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAIF,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAEzf,SAASE,iBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAEhN,SAAS,yBAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU;AAAM,WAAO,CAAC;AAAG,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAE3e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU;AAAM,WAAO,CAAC;AAAG,MAAI,SAAS,CAAC;AAAG,MAAI,aAAa,OAAO,KAAK,MAAM;AAAG,MAAI,KAAK;AAAG,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,UAAM,WAAW,CAAC;AAAG,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAAU,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAAG;AAAE,SAAO;AAAQ;AAsBlT,IAAI,eAAwB,yBAAW,SAAU,MAAM,KAAK;AAC1D,MAAI,WAAW,KAAK,UAChB,SAAS,yBAAyB,MAAM,SAAS;AAErD,MAAI,eAAe,YAAY,MAAM,GACjC,OAAO,aAAa,MACpB,QAAQ,yBAAyB,cAAc,UAAU;AAE7D,wCAAoB,KAAK,WAAY;AACnC,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AAET,SAAoB,aAAAC,QAAM,cAAc,uBAAU,MAAM,SAASF,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IAC3G;AAAA,EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,SAAS,cAAc;AAEvB,IAAI,eAAe;AAAA,EACjB,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,uBAAuB;AAAA,EACvB,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AACb;AACA,SAAS,eAAe;AACxB,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBnB,UAAU,kBAAAG,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,QAAQ,kBAAAA,QAAU,SAAS,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAK9D,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKpB,uBAAuB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKjC,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKtB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKlB,sBAAsB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKhC,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAK7B,oBAAoB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAK9B,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAK1B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCtB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS1B,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1B,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,WAAW,kBAAAA,QAAU;AACvB;AACA,IAAO,aAAQ;AAsEf,IAAI,eAAe;AAAA,EACjB,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe,CAAC;AAAA,EAChB,gBAAgB,CAAC;AACnB;AA8EO,SAAS,cAAc;AAC5B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEjF,MAAI,sBAAsBH,eAAcA,eAAc,CAAC,GAAG,YAAY,GAAG,KAAK,GAC1E,SAAS,oBAAoB,QAC7B,WAAW,oBAAoB,UAC/B,oBAAoB,oBAAoB,mBACxC,UAAU,oBAAoB,SAC9B,UAAU,oBAAoB,SAC9B,WAAW,oBAAoB,UAC/B,WAAW,oBAAoB,UAC/B,cAAc,oBAAoB,aAClC,cAAc,oBAAoB,aAClC,aAAa,oBAAoB,YACjC,SAAS,oBAAoB,QAC7B,iBAAiB,oBAAoB,gBACrC,iBAAiB,oBAAoB,gBACrC,qBAAqB,oBAAoB,oBACzC,mBAAmB,oBAAoB,kBACvC,iBAAiB,oBAAoB,gBACrC,YAAY,oBAAoB,WAChC,wBAAwB,oBAAoB,uBAC5C,UAAU,oBAAoB,SAC9B,aAAa,oBAAoB,YACjC,SAAS,oBAAoB,QAC7B,uBAAuB,oBAAoB,sBAC3C,UAAU,oBAAoB,SAC9B,YAAY,oBAAoB;AAEpC,MAAI,iBAAa,sBAAQ,WAAY;AACnC,WAAO,uBAAuB,MAAM;AAAA,EACtC,GAAG,CAAC,MAAM,CAAC;AACX,MAAI,kBAAc,sBAAQ,WAAY;AACpC,WAAO,wBAAwB,MAAM;AAAA,EACvC,GAAG,CAAC,MAAM,CAAC;AACX,MAAI,yBAAqB,sBAAQ,WAAY;AAC3C,WAAO,OAAO,qBAAqB,aAAa,mBAAmB;AAAA,EACrE,GAAG,CAAC,gBAAgB,CAAC;AACrB,MAAI,2BAAuB,sBAAQ,WAAY;AAC7C,WAAO,OAAO,uBAAuB,aAAa,qBAAqB;AAAA,EACzE,GAAG,CAAC,kBAAkB,CAAC;AAMvB,MAAI,cAAU,qBAAO,IAAI;AACzB,MAAI,eAAW,qBAAO,IAAI;AAE1B,MAAI,kBAAc,yBAAW,SAAS,YAAY,GAC9C,eAAeL,gBAAe,aAAa,CAAC,GAC5C,QAAQ,aAAa,CAAC,GACtB,WAAW,aAAa,CAAC;AAE7B,MAAI,YAAY,MAAM,WAClB,qBAAqB,MAAM;AAC/B,MAAI,0BAAsB,qBAAO,OAAO,WAAW,eAAe,OAAO,mBAAmB,kBAAkB,0BAA0B,CAAC;AAEzI,MAAI,gBAAgB,SAASS,iBAAgB;AAE3C,QAAI,CAAC,oBAAoB,WAAW,oBAAoB;AACtD,iBAAW,WAAY;AACrB,YAAI,SAAS,SAAS;AACpB,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,CAAC,MAAM,QAAQ;AACjB,qBAAS;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,iCAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAEA,8BAAU,WAAY;AACpB,WAAO,iBAAiB,SAAS,eAAe,KAAK;AACrD,WAAO,WAAY;AACjB,aAAO,oBAAoB,SAAS,eAAe,KAAK;AAAA,IAC1D;AAAA,EACF,GAAG,CAAC,UAAU,oBAAoB,sBAAsB,mBAAmB,CAAC;AAC5E,MAAI,qBAAiB,qBAAO,CAAC,CAAC;AAE9B,MAAI,iBAAiB,SAASC,gBAAe,OAAO;AAClD,QAAI,QAAQ,WAAW,QAAQ,QAAQ,SAAS,MAAM,MAAM,GAAG;AAE7D;AAAA,IACF;AAEA,UAAM,eAAe;AACrB,mBAAe,UAAU,CAAC;AAAA,EAC5B;AAEA,8BAAU,WAAY;AACpB,QAAI,uBAAuB;AACzB,eAAS,iBAAiB,YAAY,oBAAoB,KAAK;AAC/D,eAAS,iBAAiB,QAAQ,gBAAgB,KAAK;AAAA,IACzD;AAEA,WAAO,WAAY;AACjB,UAAI,uBAAuB;AACzB,iBAAS,oBAAoB,YAAY,kBAAkB;AAC3D,iBAAS,oBAAoB,QAAQ,cAAc;AAAA,MACrD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,qBAAqB,CAAC;AAEnC,8BAAU,WAAY;AACpB,QAAI,CAAC,YAAY,aAAa,QAAQ,SAAS;AAC7C,cAAQ,QAAQ,MAAM;AAAA,IACxB;AAEA,WAAO,WAAY;AAAA,IAAC;AAAA,EACtB,GAAG,CAAC,SAAS,WAAW,QAAQ,CAAC;AACjC,MAAI,cAAU,0BAAY,SAAU,GAAG;AACrC,QAAI,SAAS;AACX,cAAQ,CAAC;AAAA,IACX,OAAO;AAEL,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AACZ,MAAI,oBAAgB,0BAAY,SAAU,OAAO;AAC/C,UAAM,eAAe;AAErB,UAAM,QAAQ;AACd,oBAAgB,KAAK;AACrB,mBAAe,UAAU,CAAC,EAAE,OAAOhB,oBAAmB,eAAe,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAE7F,QAAI,eAAe,KAAK,GAAG;AACzB,cAAQ,QAAQ,kBAAkB,KAAK,CAAC,EAAE,KAAK,SAAU,OAAO;AAC9D,YAAI,qBAAqB,KAAK,KAAK,CAAC,sBAAsB;AACxD;AAAA,QACF;AAEA,YAAI,YAAY,MAAM;AACtB,YAAI,eAAe,YAAY,KAAK,iBAAiB;AAAA,UACnD;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,eAAe,YAAY,KAAK,CAAC;AACrC,iBAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAED,YAAI,aAAa;AACf,sBAAY,KAAK;AAAA,QACnB;AAAA,MACF,CAAC,EAAE,MAAM,SAAU,GAAG;AACpB,eAAO,QAAQ,CAAC;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,mBAAmB,aAAa,SAAS,sBAAsB,YAAY,SAAS,SAAS,UAAU,UAAU,SAAS,CAAC;AAC/H,MAAI,mBAAe,0BAAY,SAAU,OAAO;AAC9C,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,oBAAgB,KAAK;AACrB,QAAI,WAAW,eAAe,KAAK;AAEnC,QAAI,YAAY,MAAM,cAAc;AAClC,UAAI;AACF,cAAM,aAAa,aAAa;AAAA,MAClC,SAAS,SAAS;AAAA,MAAC;AAAA,IAGrB;AAEA,QAAI,YAAY,YAAY;AAC1B,iBAAW,KAAK;AAAA,IAClB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,oBAAoB,CAAC;AACrC,MAAI,oBAAgB,0BAAY,SAAU,OAAO;AAC/C,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,oBAAgB,KAAK;AAErB,QAAI,UAAU,eAAe,QAAQ,OAAO,SAAU,QAAQ;AAC5D,aAAO,QAAQ,WAAW,QAAQ,QAAQ,SAAS,MAAM;AAAA,IAC3D,CAAC;AAGD,QAAI,YAAY,QAAQ,QAAQ,MAAM,MAAM;AAE5C,QAAI,cAAc,IAAI;AACpB,cAAQ,OAAO,WAAW,CAAC;AAAA,IAC7B;AAEA,mBAAe,UAAU;AAEzB,QAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,IACF;AAEA,aAAS;AAAA,MACP,MAAM;AAAA,MACN,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,IAChB,CAAC;AAED,QAAI,eAAe,KAAK,KAAK,aAAa;AACxC,kBAAY,KAAK;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,oBAAoB,CAAC;AAC/C,MAAI,eAAW,0BAAY,SAAU,OAAO,OAAO;AACjD,QAAI,gBAAgB,CAAC;AACrB,QAAI,iBAAiB,CAAC;AACtB,UAAM,QAAQ,SAAU,MAAM;AAC5B,UAAI,gBAAgB,aAAa,MAAM,UAAU,GAC7C,iBAAiBM,gBAAe,eAAe,CAAC,GAChD,WAAW,eAAe,CAAC,GAC3B,cAAc,eAAe,CAAC;AAElC,UAAI,iBAAiB,cAAc,MAAM,SAAS,OAAO,GACrD,kBAAkBA,gBAAe,gBAAgB,CAAC,GAClD,YAAY,gBAAgB,CAAC,GAC7B,YAAY,gBAAgB,CAAC;AAEjC,UAAI,eAAe,YAAY,UAAU,IAAI,IAAI;AAEjD,UAAI,YAAY,aAAa,CAAC,cAAc;AAC1C,sBAAc,KAAK,IAAI;AAAA,MACzB,OAAO;AACL,YAAI,SAAS,CAAC,aAAa,SAAS;AAEpC,YAAI,cAAc;AAChB,mBAAS,OAAO,OAAO,YAAY;AAAA,QACrC;AAEA,uBAAe,KAAK;AAAA,UAClB;AAAA,UACA,QAAQ,OAAO,OAAO,SAAU,GAAG;AACjC,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,QAAI,CAAC,YAAY,cAAc,SAAS,KAAK,YAAY,YAAY,KAAK,cAAc,SAAS,UAAU;AAEzG,oBAAc,QAAQ,SAAU,MAAM;AACpC,uBAAe,KAAK;AAAA,UAClB;AAAA,UACA,QAAQ,CAAC,wBAAwB;AAAA,QACnC,CAAC;AAAA,MACH,CAAC;AACD,oBAAc,OAAO,CAAC;AAAA,IACxB;AAEA,aAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA,cAAc,eAAe,SAAS;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AACV,aAAO,eAAe,gBAAgB,KAAK;AAAA,IAC7C;AAEA,QAAI,eAAe,SAAS,KAAK,gBAAgB;AAC/C,qBAAe,gBAAgB,KAAK;AAAA,IACtC;AAEA,QAAI,cAAc,SAAS,KAAK,gBAAgB;AAC9C,qBAAe,eAAe,KAAK;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,YAAY,SAAS,SAAS,UAAU,QAAQ,gBAAgB,gBAAgB,SAAS,CAAC;AAClH,MAAI,eAAW,0BAAY,SAAU,OAAO;AAC1C,UAAM,eAAe;AAErB,UAAM,QAAQ;AACd,oBAAgB,KAAK;AACrB,mBAAe,UAAU,CAAC;AAE1B,QAAI,eAAe,KAAK,GAAG;AACzB,cAAQ,QAAQ,kBAAkB,KAAK,CAAC,EAAE,KAAK,SAAU,OAAO;AAC9D,YAAI,qBAAqB,KAAK,KAAK,CAAC,sBAAsB;AACxD;AAAA,QACF;AAEA,iBAAS,OAAO,KAAK;AAAA,MACvB,CAAC,EAAE,MAAM,SAAU,GAAG;AACpB,eAAO,QAAQ,CAAC;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,mBAAmB,UAAU,SAAS,oBAAoB,CAAC;AAE/D,MAAI,qBAAiB,0BAAY,WAAY;AAG3C,QAAI,oBAAoB,SAAS;AAC/B,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,yBAAmB;AAEnB,UAAI,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AACA,aAAO,mBAAmB,IAAI,EAAE,KAAK,SAAU,SAAS;AACtD,eAAO,kBAAkB,OAAO;AAAA,MAClC,CAAC,EAAE,KAAK,SAAU,OAAO;AACvB,iBAAS,OAAO,IAAI;AACpB,iBAAS;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAU,GAAG;AAEpB,YAAI,QAAQ,CAAC,GAAG;AACd,+BAAqB,CAAC;AACtB,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH,WAAW,gBAAgB,CAAC,GAAG;AAC7B,8BAAoB,UAAU;AAG9B,cAAI,SAAS,SAAS;AACpB,qBAAS,QAAQ,QAAQ;AACzB,qBAAS,QAAQ,MAAM;AAAA,UACzB,OAAO;AACL,oBAAQ,IAAI,MAAM,+JAA+J,CAAC;AAAA,UACpL;AAAA,QACF,OAAO;AACL,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF,CAAC;AACD;AAAA,IACF;AAEA,QAAI,SAAS,SAAS;AACpB,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,yBAAmB;AACnB,eAAS,QAAQ,QAAQ;AACzB,eAAS,QAAQ,MAAM;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,UAAU,oBAAoB,sBAAsB,gBAAgB,UAAU,SAAS,aAAa,QAAQ,CAAC;AAEjH,MAAI,kBAAc,0BAAY,SAAU,OAAO;AAE7C,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,QAAQ,YAAY,MAAM,MAAM,GAAG;AAClE;AAAA,IACF;AAEA,QAAI,MAAM,QAAQ,OAAO,MAAM,QAAQ,WAAW,MAAM,YAAY,MAAM,MAAM,YAAY,IAAI;AAC9F,YAAM,eAAe;AACrB,qBAAe;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,SAAS,cAAc,CAAC;AAE5B,MAAI,gBAAY,0BAAY,WAAY;AACtC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,eAAW,0BAAY,WAAY;AACrC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,MAAI,gBAAY,0BAAY,WAAY;AACtC,QAAI,SAAS;AACX;AAAA,IACF;AAKA,QAAI,WAAW,GAAG;AAChB,iBAAW,gBAAgB,CAAC;AAAA,IAC9B,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,SAAS,cAAc,CAAC;AAE5B,MAAI,iBAAiB,SAASW,gBAAe,IAAI;AAC/C,WAAO,WAAW,OAAO;AAAA,EAC3B;AAEA,MAAI,yBAAyB,SAASC,wBAAuB,IAAI;AAC/D,WAAO,aAAa,OAAO,eAAe,EAAE;AAAA,EAC9C;AAEA,MAAI,qBAAqB,SAASC,oBAAmB,IAAI;AACvD,WAAO,SAAS,OAAO,eAAe,EAAE;AAAA,EAC1C;AAEA,MAAI,kBAAkB,SAASC,iBAAgB,OAAO;AACpD,QAAI,sBAAsB;AACxB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,mBAAe,sBAAQ,WAAY;AACrC,WAAO,WAAY;AACjB,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC7E,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,OAAO,MAAM,MACb,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,SAAS,MAAM,QACf,UAAU,MAAM,SAChBC,eAAc,MAAM,aACpBC,cAAa,MAAM,YACnBC,eAAc,MAAM,aACpBC,UAAS,MAAM,QACf,OAAO,yBAAyB,OAAO,UAAU;AAErD,aAAOb,eAAcA,eAAcC,iBAAgB;AAAA,QACjD,WAAW,uBAAuB,qBAAqB,WAAW,WAAW,CAAC;AAAA,QAC9E,SAAS,uBAAuB,qBAAqB,SAAS,SAAS,CAAC;AAAA,QACxE,QAAQ,uBAAuB,qBAAqB,QAAQ,QAAQ,CAAC;AAAA,QACrE,SAAS,eAAe,qBAAqB,SAAS,SAAS,CAAC;AAAA,QAChE,aAAa,mBAAmB,qBAAqBS,cAAa,aAAa,CAAC;AAAA,QAChF,YAAY,mBAAmB,qBAAqBC,aAAY,YAAY,CAAC;AAAA,QAC7E,aAAa,mBAAmB,qBAAqBC,cAAa,aAAa,CAAC;AAAA,QAChF,QAAQ,mBAAmB,qBAAqBC,SAAQ,QAAQ,CAAC;AAAA,QACjE,MAAM,OAAO,SAAS,YAAY,SAAS,KAAK,OAAO;AAAA,MACzD,GAAG,QAAQ,OAAO,GAAG,CAAC,YAAY,CAAC,aAAa;AAAA,QAC9C,UAAU;AAAA,MACZ,IAAI,CAAC,CAAC,GAAG,IAAI;AAAA,IACf;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,WAAW,UAAU,WAAW,eAAe,cAAc,eAAe,UAAU,YAAY,QAAQ,QAAQ,CAAC;AAC7I,MAAI,0BAAsB,0BAAY,SAAU,OAAO;AACrD,UAAM,gBAAgB;AAAA,EACxB,GAAG,CAAC,CAAC;AACL,MAAI,oBAAgB,sBAAQ,WAAY;AACtC,WAAO,WAAY;AACjB,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC7E,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,WAAW,MAAM,UACjB,UAAU,MAAM,SAChB,OAAO,yBAAyB,OAAO,UAAU;AAErD,UAAI,aAAaZ,iBAAgB;AAAA,QAC/B,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN,OAAO;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,QACd;AAAA,QACA,UAAU,eAAe,qBAAqB,UAAU,QAAQ,CAAC;AAAA,QACjE,SAAS,eAAe,qBAAqB,SAAS,mBAAmB,CAAC;AAAA,QAC1E,UAAU;AAAA,MACZ,GAAG,QAAQ,QAAQ;AAEnB,aAAOD,eAAcA,eAAc,CAAC,GAAG,UAAU,GAAG,IAAI;AAAA,IAC1D;AAAA,EACF,GAAG,CAAC,UAAU,QAAQ,UAAU,UAAU,QAAQ,CAAC;AACnD,SAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IACjD,WAAW,aAAa,CAAC;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,eAAe,cAAc;AAAA,EACrC,CAAC;AACH;AAOA,SAAS,QAAQ,OAAO,QAAQ;AAE9B,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,WAAW;AAAA,MACb,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,WAAW;AAAA,MACb,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,QACxD,oBAAoB;AAAA,MACtB,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,oBAAoB;AAAA,MACtB,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,cAAc,OAAO;AAAA,QACrB,cAAc,OAAO;AAAA,QACrB,cAAc,OAAO;AAAA,MACvB,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,eAAe,OAAO;AAAA,QACtB,gBAAgB,OAAO;AAAA,QACvB,cAAc,OAAO;AAAA,MACvB,CAAC;AAAA,IAEH,KAAK;AACH,aAAOA,eAAc,CAAC,GAAG,YAAY;AAAA,IAEvC;AACE,aAAO;AAAA,EACX;AACF;AAEA,SAAS,OAAO;AAAC;", "names": ["file", "_accepts", "getInvalidTypeRejectionErr", "getTooLargeRejectionErr", "getTooSmallRejectionErr", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_nonIterableRest", "ownKeys", "_objectSpread", "_defineProperty", "React", "PropTypes", "onWindowFocus", "onDocumentDrop", "<PERSON><PERSON><PERSON><PERSON>", "composeKeyboardHandler", "composeDragHandler", "stopPropagation", "onDragEnter", "onDragOver", "onDragLeave", "onDrop"]}