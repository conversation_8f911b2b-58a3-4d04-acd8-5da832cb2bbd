import{c as o}from"./createLucideIcon-6c39ff51.js";import{a as f,I as y,C as h,J as x,B as w,v as M,D as k,F as i}from"./vendor-8eb8bd34.js";import{f as g,C as n}from"./index-3b130772.js";import{u as s}from"./index-fcfcda09.js";/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=o("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=o("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=o("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=o("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),c=f(g),C=async r=>{try{const e=y(h(c,n.DOCUMENTS),x("userId","==",r)),d=await w(e);let t=0;const m=d.docs.map(async p=>{const a=p.id,u=M(c,n.DOCUMENTS,a);try{return await k(u,{readers:i(r),writers:i(r),owner:r,permissionsLastUpdated:new Date,permissionRepaired:!0}),t++,!0}catch(l){return console.error(`Error repairing permissions for document ${a}:`,l),!1}});return await Promise.all(m),{success:!0,fixed:t}}catch(e){return console.error("Error repairing user document permissions:",e),{success:!1,fixed:0}}},T=async r=>{if(!r){s.error("You must be logged in to fix document permissions");return}s.loading("Fixing document permissions...");try{const e=await C(r);e.success?s.success(`Fixed permissions for ${e.fixed} documents`):s.error("Failed to fix document permissions")}catch(e){console.error("Error in fixAllDocumentPermissions:",e),s.error("An error occurred while fixing permissions")}finally{s.dismiss()}};export{P as A,E as F,b as L,H as P,T as f};
