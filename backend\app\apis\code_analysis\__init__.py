from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import databutton as db
import openai
import json

router = APIRouter()

# --- Pydantic Models ---
class CodeAnalysisRequest(BaseModel):
    code: str
    language: str = Field(default="python", description="The programming language of the code.")

class SyntaxErrorDetail(BaseModel):
    line: int
    column: int = Field(description="The column number where the issue starts (1-indexed).") # Ensure column is always present
    message: str
    severity: str = Field(default="error", description="Severity of the issue, e.g., 'error' or 'warning'.")

class CodeAnalysisResponse(BaseModel):
    isValid: bool = Field(description="Overall validity based on found errors. True if no 'error' severity issues are found.")
    errors: list[SyntaxErrorDetail] = Field(default=[], description="List of syntax errors or warnings found in the code.")
    rawLLMResponse: str | None = Field(default=None, description="Optional: The raw JSON string from the LLM for debugging.")

# --- OpenAI Client Initialization ---
# Ensure the OPENAI_API_KEY secret is set in Databutton
# Ensure the DEEPSEEK_API_KEY secret is set in Databutton
client = openai.OpenAI(
    api_key=db.secrets.get("Deepseek_API_KEY"),
    base_url="https://api.deepseek.com/v1"
)

# --- API Endpoint ---
