import {
  BodySummarizeUploadedFiles,
  CheckHealthData,
  CodeValidationRequest,
  DeleteExportData,
  DocumentExportRequest,
  DocumentationRequest,
  DownloadDocumentData,
  DownloadFromStorageData,
  ExportDocumentData,
  GenerateDocumentationData,
  GetExportStatusData,
  LlmValidateCodeSyntaxEndpointData,
  LlmValidateCodeSyntaxRequest,
  RunTestEndpointData,
  SummarizeUploadedFilesData,
  TestingDocumentationLanguageData,
  ValidateCodeEndpoint2Data,
  ValidateCodeEndpointData,
} from "./data-contracts";

export namespace Brain {
  /**
   * @description Check health of application. Returns 200 when OK, 500 when not.
   * @name check_health
   * @summary Check Health
   * @request GET:/_healthz
   */
  export namespace check_health {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CheckHealthData;
  }

  /**
   * @description Test endpoint for documentation generation
   * @tags dbtn/module:testing, dbtn/hasAuth
   * @name run_test_endpoint
   * @summary Run Test Endpoint
   * @request GET:/routes/test
   */
  export namespace run_test_endpoint {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = RunTestEndpointData;
  }

  /**
   * @description Test endpoint for documentation language settings. Returns the language that would be used for documentation.
   * @tags dbtn/module:testing, dbtn/hasAuth
   * @name testing_documentation_language
   * @summary Testing Documentation Language
   * @request POST:/routes/test_language
   */
  export namespace testing_documentation_language {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = DocumentationRequest;
    export type RequestHeaders = {};
    export type ResponseBody = TestingDocumentationLanguageData;
  }

  /**
   * @description Test endpoint for documentation generation
   * @tags dbtn/module:test_utils, dbtn/hasAuth
   * @name validate_code_endpoint2
   * @summary Validate Code Endpoint2
   * @request GET:/routes/test-documentation
   */
  export namespace validate_code_endpoint2 {
    export type RequestParams = {};
    export type RequestQuery = {
      /**
       * Language
       * Documentation language code (en, de, fr, it, es)
       * @default "en"
       */
      language?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ValidateCodeEndpoint2Data;
  }

  /**
   * @description Download a file directly from storage using its key.
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name download_from_storage
   * @summary Download From Storage
   * @request GET:/routes/export/download/{storage_key}
   */
  export namespace download_from_storage {
    export type RequestParams = {
      /** Storage Key */
      storageKey: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = DownloadFromStorageData;
  }

  /**
   * @description Export a document in the specified format, using cache if available.
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name export_document
   * @summary Export Document
   * @request POST:/routes/export/document
   */
  export namespace export_document {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = DocumentExportRequest;
    export type RequestHeaders = {};
    export type ResponseBody = ExportDocumentData;
  }

  /**
   * @description Get the status of an export task.
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name get_export_status
   * @summary Get Export Status
   * @request GET:/routes/export/document/{export_id}/status
   */
  export namespace get_export_status {
    export type RequestParams = {
      /** Export Id */
      exportId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GetExportStatusData;
  }

  /**
   * @description Download a previously generated document.
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name download_document
   * @summary Download Document
   * @request GET:/routes/export/document/{export_id}/download
   */
  export namespace download_document {
    export type RequestParams = {
      /** Export Id */
      exportId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = DownloadDocumentData;
  }

  /**
   * @description Delete an export task and its associated document.
   * @tags dbtn/module:export, dbtn/hasAuth
   * @name delete_export
   * @summary Delete Export
   * @request DELETE:/routes/export/document/{export_id}
   */
  export namespace delete_export {
    export type RequestParams = {
      /** Export Id */
      exportId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = DeleteExportData;
  }

  /**
   * No description
   * @tags Context Processor, dbtn/module:context_processor, dbtn/hasAuth
   * @name summarize_uploaded_files
   * @summary Summarize Uploaded Files
   * @request POST:/routes/context-processor/summarize-uploaded-files
   */
  export namespace summarize_uploaded_files {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = BodySummarizeUploadedFiles;
    export type RequestHeaders = {};
    export type ResponseBody = SummarizeUploadedFilesData;
  }

  /**
   * No description
   * @tags dbtn/module:llm_validator, dbtn/hasAuth
   * @name llm_validate_code_syntax_endpoint
   * @summary Llm Validate Code Syntax Endpoint
   * @request POST:/routes/llm_validate_code_syntax
   */
  export namespace llm_validate_code_syntax_endpoint {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = LlmValidateCodeSyntaxRequest;
    export type RequestHeaders = {};
    export type ResponseBody = LlmValidateCodeSyntaxEndpointData;
  }

  /**
   * @description Generate documentation for code. This endpoint generates detailed documentation for the provided code, leveraging LLM services like OpenAI's GPT models or DeepSeek's coding models. The documentation will include: - Textual description of what the code does - Parameters (input, output, and hardcoded) - Tools and functions used in the code - Workflow steps showing the execution flow Args: request (DocumentationRequest): The request containing: - code: The source code to document - language: Programming language (default: Python) - provider: LLM provider to use ("openai", "deepseek", or "auto") - format: Output format (default: "markdown") - documentationLanguage: Language for generated docs (en, de, etc.) - documentation_id: Optional ID of existing doc to fetch context for Returns: DocumentationGenerationResponse: The generated documentation or error details
   * @tags dbtn/module:documentation_generator, dbtn/hasAuth
   * @name generate_documentation
   * @summary Generate Documentation
   * @request POST:/routes/documentation/generate
   */
  export namespace generate_documentation {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = DocumentationRequest;
    export type RequestHeaders = {};
    export type ResponseBody = GenerateDocumentationData;
  }

  /**
   * @description Validate the provided code and detect its language. Args: request (CodeValidationRequest): The code to validate and optional language Returns: CodeValidationResponse: The validation result
   * @tags dbtn/module:code_validation, dbtn/hasAuth
   * @name validate_code_endpoint
   * @summary Validate Code Endpoint
   * @request POST:/routes/validate
   */
  export namespace validate_code_endpoint {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CodeValidationRequest;
    export type RequestHeaders = {};
    export type ResponseBody = ValidateCodeEndpointData;
  }
}
