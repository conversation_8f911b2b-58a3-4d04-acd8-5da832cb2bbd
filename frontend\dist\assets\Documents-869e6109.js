import{m as l,v as G,a as W,x as _,B as ge,C as he,D as Qe,j as t,A as et}from"./vendor-8eb8bd34.js";import{f as q,C as H,P as tt,a as nt,b as ot,A as ee}from"./index-14585119.js";import{useDocumentStore as rt}from"./documentStore-31541230.js";import{f as pe,L as at,F as fe,A as st,P as te}from"./documentPermissions-1e8b15bf.js";import{u as g,P as I}from"./index-fcfcda09.js";import{e as F,f as it,u as L,g as lt,B as S,C as ne,a as oe,b as re,d as ae,c as se,h as ct}from"./card-b935221e.js";import{L as dt,I as ut}from"./label-ef207e6f.js";import{c as xe,a as R,P as $,D as mt,b as gt,u as De,d as V,e as ht}from"./index-89be2a26.js";import{R as pt,h as ft,u as xt,F as Dt,P as kt,C as vt,a as yt}from"./react-icons.esm-600f1a11.js";import{u as wt}from"./index-a23265aa.js";import{c as C}from"./createLucideIcon-6c39ff51.js";import{D as ie,a as le,b as ce,c as O,d as de}from"./dropdown-menu-752f7312.js";/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bt=C("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=C("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=C("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=C("FileCode",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Et=C("FileType",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M9 13v-1h6v1",key:"1bb014"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"M11 18h2",key:"12mj7e"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=C("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nt=C("LockOpen",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const St=C("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ft=C("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pt=C("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const It=C("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);/**
 * @license lucide-react v0.439.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tt=C("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function Rt({user:e,docId:n,viewMode:o,editMode:i}){const[a,r]=l.useState(null),[m,x]=l.useState(null),[k,p]=l.useState(!1),[D,f]=l.useState(null),u=async(d,c)=>{var y,v,s;if(!(e!=null&&e.uid)){g.error("Sie müssen angemeldet sein, um dieses Dokument zu laden");return}p(!0),f(null);try{g.loading("Dokument wird geladen...");const h=G(W(q),H.DOCUMENTS,d),b=await _(h);if(!b.exists())throw new Error("Dokument nicht gefunden");const w=b.data();if(console.log("[Dokument Loader] MetaData:",w),console.log("[Dokument Loader] Benutzer UID:",e==null?void 0:e.uid),console.log("[Dokument Loader] Dokument UserID:",w==null?void 0:w.userId),w.userId!==e.uid&&!w.isPublic){console.log("[Dokument Loader] Berechtigungen werden repariert..."),g.loading("Dokumentberechtigungen werden repariert..."),await pe(e.uid);const A=await _(h);if(!A.exists()||A.data().userId!==e.uid&&!A.data().isPublic)throw new Error("Fehlende oder unzureichende Berechtigungen")}console.log("[Dokument Loader] Inhalts-Subkollektion wird abgerufen...");const j=await ge(he(h,"content"));if(j.empty)throw new Error("Dokumentinhalt nicht gefunden");const N=j.docs[0].data(),T={id:d,title:w.title,description:w.description,createdAt:(y=w.createdAt)==null?void 0:y.toDate(),updatedAt:(v=w.updatedAt)==null?void 0:v.toDate(),userId:w.userId,language:w.language,codeSize:w.codeSize,isPublic:w.isPublic,tags:w.tags||[],project_context:w.project_context,content:{originalCode:N.originalCode,generatedDocumentation:N.generatedDocumentation,parameters:N.parameters,toolsUsed:N.toolsUsed,workflow:N.workflow,manualEdits:(s=N.manualEdits)==null?void 0:s.map(A=>{var Q;return{...A,timestamp:(Q=A.timestamp)==null?void 0:Q.toDate()}}),exportCache:N.exportCache||[]}};console.log("[Dokument Loader] Dokument erfolgreich geladen:",T),c==="view"?(r(T),g.success(`Dokument wird angezeigt: ${T.title}`)):c==="edit"&&(x(T),g.success(`Dokument wird bearbeitet: ${T.title}`))}catch(h){console.error("Fehler beim Laden des Dokuments:",h);const b=h instanceof Error?h.message:"Unbekannter Fehler";f(b),b.includes("Berechtigungen")?g.error('Berechtigungsfehler: Versuchen Sie zuerst die Schaltfläche "Berechtigungen reparieren"'):g.error(`Fehler beim Laden des Dokuments: ${b}`)}finally{p(!1),g.dismiss()}};return l.useEffect(()=>{n&&(e!=null&&e.uid)&&(o?u(n,"view"):i&&u(n,"edit"))},[n,o,i,e==null?void 0:e.uid]),{viewingDocument:a,editingDocument:m,setViewingDocument:r,setEditingDocument:x,loadDocument:u,isLoading:k,error:D}}function Mt(e){return new Intl.DateTimeFormat("de-DE",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}function At({user:e,userPlan:n,documentsCount:o,onDocumentDeleted:i,onRefreshDocuments:a}){const[r,m]=l.useState(null),[x,k]=l.useState(!1),p=tt[n].maxDocuments,D=o<p;return{deleteTargetId:r,isDeleting:x,canCreateNew:D,handleCreateNew:()=>{if(!D){g.error(`Sie haben Ihr Plan-Limit von ${p} Dokumenten erreicht. Upgraden Sie für mehr Dokumente.`);return}if(!(e!=null&&e.uid)){g.error("Sie müssen angemeldet sein, um ein Dokument zu erstellen");return}try{g.success("Neues Dokument wird erstellt...");const c=window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"/";window.location.href="./editor?new=true"}catch(c){console.error("Fehler bei der Navigation zum Editor:",c),g.error("Navigation zum Editor fehlgeschlagen. Bitte versuchen Sie es erneut.")}},handleSelectDocument:c=>{window.location.pathname.endsWith("/")?window.location.pathname:window.location.pathname+"",window.location.href=`./editor?id=${c}`},handleDeleteDocument:async c=>{if(console.log("Dokument löschen ausgelöst für ID:",c),!window.confirm("Sind Sie sicher, dass Sie dieses Dokument löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.")){console.log("Löschbestätigung abgelehnt");return}k(!0),m(c);try{const y=g.loading("Dokument wird gelöscht...");if(console.log("Löschbestätigung akzeptiert, Toast ID:",y),!(e!=null&&e.uid)){g.error("Sie müssen angemeldet sein, um Dokumente zu löschen");return}console.log("Dokument in Firestore referenzieren:",c);const v=G(W(q),H.DOCUMENTS,c);console.log("Dokument-Snapshot abrufen");const s=await _(v);if(!s.exists()){g.error("Dokument nicht gefunden");return}const h=s.data();if(console.log("Dokumentdaten abgerufen:",h),h.userId!==e.uid){g.error("Sie haben keine Berechtigung, dieses Dokument zu löschen");return}h.needsPermissionFix&&(console.log("Berechtigungen vor dem Löschen reparieren"),await pe(e.uid)),console.log("Soft-Delete-Operation durchführen"),await Qe(v,{isDeleted:!0,deletedAt:new Date}),console.log("Soft-Delete erfolgreich abgeschlossen"),i(c),console.log("Dokument aus lokalem Zustand entfernt"),e!=null&&e.uid&&(console.log("Dokumentenliste aktualisieren"),await a()),g.success("Dokument erfolgreich gelöscht"),console.log("Löschvorgang erfolgreich abgeschlossen")}catch(y){console.error("Fehler beim Löschen des Dokuments:",y),g.error('Berechtigungsfehler: Versuchen Sie zuerst die Schaltfläche "Berechtigungen reparieren" und versuchen Sie es dann erneut')}finally{k(!1),g.dismiss(),setTimeout(()=>m(null),300)}},formatDate:Mt}}const Ot=(e,n,o)=>{const i=new Blob([e],{type:o}),a=URL.createObjectURL(i),r=document.createElement("a");r.href=a,r.download=n,r.click(),URL.revokeObjectURL(a)};function Lt({user:e}){const[n,o]=l.useState(null),[i,a]=l.useState(!1),[r,m]=l.useState(!1);return{exportDocument:n,exportDialogOpen:i,isExporting:r,setExportDialogOpen:a,handleExportDialog:async p=>{var D,f,u,d;console.log("Export-Dialog ausgelöst für Dokument ID:",p);try{g.loading("Dokument für Export vorbereiten...");const c=G(W(q),H.DOCUMENTS,p);console.log("Dokument aus Firestore abrufen:",c.path);const y=await _(c);if(!y.exists()){console.error("Dokument nicht gefunden"),g.error("Dokument nicht gefunden");return}console.log("Dokument existiert, Metadaten abrufen");const v=y.data();console.log("Inhalts-Subkollektion abrufen");const s=await ge(he(c,"content"));if(s.empty){console.error("Inhalts-Subkollektion ist leer"),g.error("Dokumentinhalt nicht gefunden");return}const h=s.docs[0].data();console.log("Inhaltsdaten erfolgreich abgerufen");const b={id:p,title:v.title||"Unbenanntes Dokument",description:v.description||"",createdAt:((D=v.createdAt)==null?void 0:D.toDate())||new Date,updatedAt:((f=v.updatedAt)==null?void 0:f.toDate())||new Date,userId:v.userId,language:v.language||"python",codeSize:v.codeSize||0,isPublic:v.isPublic||!1,tags:v.tags||[],content:{originalCode:h.originalCode||"",generatedDocumentation:h.generatedDocumentation||null,parameters:h.parameters||[],toolsUsed:h.toolsUsed||[],workflow:h.workflow||null,manualEdits:((u=h.manualEdits)==null?void 0:u.map(w=>{var j;return{...w,timestamp:((j=w.timestamp)==null?void 0:j.toDate())||new Date}}))||[],exportCache:h.exportCache||[]}};console.log("Dokument-Objekt erstellt, Zustand setzen und Export-Dialog öffnen"),console.log("Dokument hat Inhalt:",!!b.content,"Original-Code-Länge:",((d=b.content.originalCode)==null?void 0:d.length)||0),console.log("UI-Zustand vor dem Öffnen des Export-Dialogs:",{exportDialogCurrentlyOpen:i,currentExportDocument:n?n.id:"none"}),o(b),a(!0),console.log("UI-Zustand für Export-Dialog aktualisiert:",{exportDialogOpen:!0,documentId:b.id}),g.dismiss()}catch(c){console.error("Fehler beim Abrufen des Dokuments für Export:",c),g.error("Fehler beim Vorbereiten des Dokuments für Export"),g.dismiss()}},handleExportDocument:async(p,D)=>{if(!(e!=null&&e.uid)){console.error("Benutzer nicht angemeldet"),g.error("Sie müssen angemeldet sein, um Dokumente zu exportieren");return}m(!0);try{console.log("Export-Prozess starten für Dokument:",p.id,"Formate:",D),g.loading("Export wird vorbereitet...");const f=JSON.parse(JSON.stringify(p));if(!f||!f.content){console.error("Dokumentinhalt fehlt:",f),g.error("Dokumentinhalt fehlt oder ist ungültig");return}console.log("Dokument validiert, mit Export fortfahren");const u={},d=p.content.exportCache||[];console.log("Gecachte Formate:",d.map(s=>`${s.format} (${new Date(s.updatedAt).toLocaleString()})`));const c=D.map(async s=>{try{console.log(`Format verarbeiten: ${s}`);const h=d.find(b=>b&&b.format===s&&new Date().getTime()-new Date(b.updatedAt).getTime()<864e5);if(h&&(console.log(`Gecachte Version für ${s} gefunden:`,h),s==="markdown"||s==="html"||s==="text")){const b=s==="markdown"?".md":s==="html"?".html":".txt",w=s==="markdown"?"text/markdown":s==="html"?"text/html":"text/plain",j=p.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()+b;console.log(`Gecachte ${s}-Daten verwenden, als ${j} herunterladen`),g.info(`Gecachte ${s.toUpperCase()}-Datei verwenden`),Ot(h.data,j,w),u[s]=!0;return}console.log(`Neuen Export für ${s} generieren`),g.loading(`${s.toUpperCase()} wird generiert...`),await new Promise(b=>setTimeout(b,1e3)),u[s]=!0,g.success(`${s.toUpperCase()} erfolgreich exportiert`)}catch(h){console.error(`Fehler beim Exportieren von ${s}:`,h),g.error(`Fehler beim Exportieren von ${s.toUpperCase()}`),u[s]=!1}});await Promise.all(c);const y=Object.values(u).filter(Boolean).length,v=D.length;y===v?g.success(`Alle ${v} Formate erfolgreich exportiert`):y>0?g.warning(`${y} von ${v} Formaten erfolgreich exportiert`):g.error("Export fehlgeschlagen"),a(!1),o(null)}catch(f){console.error("Fehler beim Exportieren des Dokuments:",f),g.error("Export fehlgeschlagen: "+(f instanceof Error?f.message:"Unbekannter Fehler"))}finally{m(!1),g.dismiss()}}}}function _t({documents:e}){const[n,o]=l.useState(""),[i,a]=l.useState(null),[r,m]=l.useState([]),x=l.useMemo(()=>Array.from(new Set(e.flatMap(D=>D.tags||[]))).sort(),[e]),k=l.useMemo(()=>n.trim()!==""||i!==null,[n,i]),p=()=>{o(""),a(null)};return l.useEffect(()=>{if(!e){m([]);return}let D=[...e];if(n.trim()){const f=n.toLowerCase();D=D.filter(u=>{var d;return u.title.toLowerCase().includes(f)||((d=u.description)==null?void 0:d.toLowerCase().includes(f))||u.tags&&u.tags.some(c=>c.toLowerCase().includes(f))})}i&&(D=D.filter(f=>f.tags&&f.tags.includes(i))),m(D)},[n,i,e]),{searchTerm:n,setSearchTerm:o,activeTag:i,setActiveTag:a,filteredDocs:r,allTags:x,clearFilters:p,hasActiveFilters:k}}const zt=it("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function me({className:e,variant:n,...o}){return t.jsx("div",{className:F(zt({variant:n}),e),...o})}var U="Dialog",[ve,vn]=xe(U),[$t,E]=ve(U),ye=e=>{const{__scopeDialog:n,children:o,open:i,defaultOpen:a,onOpenChange:r,modal:m=!0}=e,x=l.useRef(null),k=l.useRef(null),[p,D]=De({prop:i,defaultProp:a??!1,onChange:r,caller:U});return t.jsx($t,{scope:n,triggerRef:x,contentRef:k,contentId:V(),titleId:V(),descriptionId:V(),open:p,onOpenChange:D,onOpenToggle:l.useCallback(()=>D(f=>!f),[D]),modal:m,children:o})};ye.displayName=U;var we="DialogTrigger",Ut=l.forwardRef((e,n)=>{const{__scopeDialog:o,...i}=e,a=E(we,o),r=L(n,a.triggerRef);return t.jsx(I.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":J(a.open),...i,ref:r,onClick:R(e.onClick,a.onOpenToggle)})});Ut.displayName=we;var Z="DialogPortal",[Bt,be]=ve(Z,{forceMount:void 0}),Ce=e=>{const{__scopeDialog:n,forceMount:o,children:i,container:a}=e,r=E(Z,n);return t.jsx(Bt,{scope:n,forceMount:o,children:l.Children.map(i,m=>t.jsx($,{present:o||r.open,children:t.jsx(kt,{asChild:!0,container:a,children:m})}))})};Ce.displayName=Z;var z="DialogOverlay",je=l.forwardRef((e,n)=>{const o=be(z,e.__scopeDialog),{forceMount:i=o.forceMount,...a}=e,r=E(z,e.__scopeDialog);return r.modal?t.jsx($,{present:i||r.open,children:t.jsx(Gt,{...a,ref:n})}):null});je.displayName=z;var Vt=lt("DialogOverlay.RemoveScroll"),Gt=l.forwardRef((e,n)=>{const{__scopeDialog:o,...i}=e,a=E(z,o);return t.jsx(pt,{as:Vt,allowPinchZoom:!0,shards:[a.contentRef],children:t.jsx(I.div,{"data-state":J(a.open),...i,ref:n,style:{pointerEvents:"auto",...i.style}})})}),M="DialogContent",Ee=l.forwardRef((e,n)=>{const o=be(M,e.__scopeDialog),{forceMount:i=o.forceMount,...a}=e,r=E(M,e.__scopeDialog);return t.jsx($,{present:i||r.open,children:r.modal?t.jsx(Wt,{...a,ref:n}):t.jsx(qt,{...a,ref:n})})});Ee.displayName=M;var Wt=l.forwardRef((e,n)=>{const o=E(M,e.__scopeDialog),i=l.useRef(null),a=L(n,o.contentRef,i);return l.useEffect(()=>{const r=i.current;if(r)return ft(r)},[]),t.jsx(Ne,{...e,ref:a,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,r=>{var m;r.preventDefault(),(m=o.triggerRef.current)==null||m.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,r=>{const m=r.detail.originalEvent,x=m.button===0&&m.ctrlKey===!0;(m.button===2||x)&&r.preventDefault()}),onFocusOutside:R(e.onFocusOutside,r=>r.preventDefault())})}),qt=l.forwardRef((e,n)=>{const o=E(M,e.__scopeDialog),i=l.useRef(!1),a=l.useRef(!1);return t.jsx(Ne,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var m,x;(m=e.onCloseAutoFocus)==null||m.call(e,r),r.defaultPrevented||(i.current||(x=o.triggerRef.current)==null||x.focus(),r.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:r=>{var k,p;(k=e.onInteractOutside)==null||k.call(e,r),r.defaultPrevented||(i.current=!0,r.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const m=r.target;((p=o.triggerRef.current)==null?void 0:p.contains(m))&&r.preventDefault(),r.detail.originalEvent.type==="focusin"&&a.current&&r.preventDefault()}})}),Ne=l.forwardRef((e,n)=>{const{__scopeDialog:o,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:r,...m}=e,x=E(M,o),k=l.useRef(null),p=L(n,k);return xt(),t.jsxs(t.Fragment,{children:[t.jsx(Dt,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:r,children:t.jsx(mt,{role:"dialog",id:x.contentId,"aria-describedby":x.descriptionId,"aria-labelledby":x.titleId,"data-state":J(x.open),...m,ref:p,onDismiss:()=>x.onOpenChange(!1)})}),t.jsxs(t.Fragment,{children:[t.jsx(Ht,{titleId:x.titleId}),t.jsx(Kt,{contentRef:k,descriptionId:x.descriptionId})]})]})}),K="DialogTitle",Se=l.forwardRef((e,n)=>{const{__scopeDialog:o,...i}=e,a=E(K,o);return t.jsx(I.h2,{id:a.titleId,...i,ref:n})});Se.displayName=K;var Fe="DialogDescription",Pe=l.forwardRef((e,n)=>{const{__scopeDialog:o,...i}=e,a=E(Fe,o);return t.jsx(I.p,{id:a.descriptionId,...i,ref:n})});Pe.displayName=Fe;var Ie="DialogClose",Te=l.forwardRef((e,n)=>{const{__scopeDialog:o,...i}=e,a=E(Ie,o);return t.jsx(I.button,{type:"button",...i,ref:n,onClick:R(e.onClick,()=>a.onOpenChange(!1))})});Te.displayName=Ie;function J(e){return e?"open":"closed"}var Re="DialogTitleWarning",[yn,Me]=gt(Re,{contentName:M,titleName:K,docsSlug:"dialog"}),Ht=({titleId:e})=>{const n=Me(Re),o=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(o))},[o,e]),null},Zt="DialogDescriptionWarning",Kt=({contentRef:e,descriptionId:n})=>{const i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Me(Zt).contentName}}.`;return l.useEffect(()=>{var r;const a=(r=e.current)==null?void 0:r.getAttribute("aria-describedby");n&&a&&(document.getElementById(n)||console.warn(i))},[i,e,n]),null},Jt=ye,Xt=Ce,Ae=je,Oe=Ee,Le=Se,_e=Pe,Yt=Te;const Qt=Jt,en=Xt,ze=l.forwardRef(({className:e,...n},o)=>t.jsx(Ae,{ref:o,className:F("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...n}));ze.displayName=Ae.displayName;const $e=l.forwardRef(({className:e,children:n,...o},i)=>t.jsxs(en,{children:[t.jsx(ze,{}),t.jsxs(Oe,{ref:i,className:F("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...o,children:[n,t.jsxs(Yt,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(vt,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));$e.displayName=Oe.displayName;const Ue=({className:e,...n})=>t.jsx("div",{className:F("flex flex-col space-y-1.5 text-center sm:text-left",e),...n});Ue.displayName="DialogHeader";const Be=({className:e,...n})=>t.jsx("div",{className:F("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...n});Be.displayName="DialogFooter";const Ve=l.forwardRef(({className:e,...n},o)=>t.jsx(Le,{ref:o,className:F("text-lg font-semibold leading-none tracking-tight",e),...n}));Ve.displayName=Le.displayName;const Ge=l.forwardRef(({className:e,...n},o)=>t.jsx(_e,{ref:o,className:F("text-sm text-muted-foreground",e),...n}));Ge.displayName=_e.displayName;var B="Checkbox",[tn,wn]=xe(B),[nn,X]=tn(B);function on(e){const{__scopeCheckbox:n,checked:o,children:i,defaultChecked:a,disabled:r,form:m,name:x,onCheckedChange:k,required:p,value:D="on",internal_do_not_use_render:f}=e,[u,d]=De({prop:o,defaultProp:a??!1,onChange:k,caller:B}),[c,y]=l.useState(null),[v,s]=l.useState(null),h=l.useRef(!1),b=c?!!m||!!c.closest("form"):!0,w={checked:u,disabled:r,setChecked:d,control:c,setControl:y,name:x,form:m,value:D,hasConsumerStoppedPropagationRef:h,required:p,defaultChecked:P(a)?!1:a,isFormControl:b,bubbleInput:v,setBubbleInput:s};return t.jsx(nn,{scope:n,...w,children:rn(f)?f(w):i})}var We="CheckboxTrigger",qe=l.forwardRef(({__scopeCheckbox:e,onKeyDown:n,onClick:o,...i},a)=>{const{control:r,value:m,disabled:x,checked:k,required:p,setControl:D,setChecked:f,hasConsumerStoppedPropagationRef:u,isFormControl:d,bubbleInput:c}=X(We,e),y=L(a,D),v=l.useRef(k);return l.useEffect(()=>{const s=r==null?void 0:r.form;if(s){const h=()=>f(v.current);return s.addEventListener("reset",h),()=>s.removeEventListener("reset",h)}},[r,f]),t.jsx(I.button,{type:"button",role:"checkbox","aria-checked":P(k)?"mixed":k,"aria-required":p,"data-state":Xe(k),"data-disabled":x?"":void 0,disabled:x,value:m,...i,ref:y,onKeyDown:R(n,s=>{s.key==="Enter"&&s.preventDefault()}),onClick:R(o,s=>{f(h=>P(h)?!0:!h),c&&d&&(u.current=s.isPropagationStopped(),u.current||s.stopPropagation())})})});qe.displayName=We;var Y=l.forwardRef((e,n)=>{const{__scopeCheckbox:o,name:i,checked:a,defaultChecked:r,required:m,disabled:x,value:k,onCheckedChange:p,form:D,...f}=e;return t.jsx(on,{__scopeCheckbox:o,checked:a,defaultChecked:r,disabled:x,required:m,onCheckedChange:p,name:i,form:D,value:k,internal_do_not_use_render:({isFormControl:u})=>t.jsxs(t.Fragment,{children:[t.jsx(qe,{...f,ref:n,__scopeCheckbox:o}),u&&t.jsx(Je,{__scopeCheckbox:o})]})})});Y.displayName=B;var He="CheckboxIndicator",Ze=l.forwardRef((e,n)=>{const{__scopeCheckbox:o,forceMount:i,...a}=e,r=X(He,o);return t.jsx($,{present:i||P(r.checked)||r.checked===!0,children:t.jsx(I.span,{"data-state":Xe(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:n,style:{pointerEvents:"none",...e.style}})})});Ze.displayName=He;var Ke="CheckboxBubbleInput",Je=l.forwardRef(({__scopeCheckbox:e,...n},o)=>{const{control:i,hasConsumerStoppedPropagationRef:a,checked:r,defaultChecked:m,required:x,disabled:k,name:p,value:D,form:f,bubbleInput:u,setBubbleInput:d}=X(Ke,e),c=L(o,d),y=wt(r),v=ht(i);l.useEffect(()=>{const h=u;if(!h)return;const b=window.HTMLInputElement.prototype,j=Object.getOwnPropertyDescriptor(b,"checked").set,N=!a.current;if(y!==r&&j){const T=new Event("click",{bubbles:N});h.indeterminate=P(r),j.call(h,P(r)?!1:r),h.dispatchEvent(T)}},[u,y,r,a]);const s=l.useRef(P(r)?!1:r);return t.jsx(I.input,{type:"checkbox","aria-hidden":!0,defaultChecked:m??s.current,required:x,disabled:k,name:p,value:D,form:f,...n,tabIndex:-1,ref:c,style:{...n.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});Je.displayName=Ke;function rn(e){return typeof e=="function"}function P(e){return e==="indeterminate"}function Xe(e){return P(e)?"indeterminate":e?"checked":"unchecked"}const Ye=l.forwardRef(({className:e,...n},o)=>t.jsx(Y,{ref:o,className:F("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...n,children:t.jsx(Ze,{className:F("flex items-center justify-center text-current"),children:t.jsx(yt,{className:"h-4 w-4"})})}));Ye.displayName=Y.displayName;const an=[{id:"markdown",label:"Markdown",icon:t.jsx(fe,{size:16}),mimeType:"text/markdown",extension:".md"},{id:"html",label:"HTML",icon:t.jsx(ke,{size:16}),mimeType:"text/html",extension:".html"},{id:"text",label:"Plain Text",icon:t.jsx(ue,{size:16}),mimeType:"text/plain",extension:".txt"},{id:"docx",label:"Word Document",icon:t.jsx(ue,{size:16}),mimeType:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",extension:".docx"},{id:"pdf",label:"PDF Document",icon:t.jsx(Et,{size:16}),mimeType:"application/pdf",extension:".pdf"}];function sn({document:e,isOpen:n,onClose:o,onExport:i}){const[a,r]=l.useState(["markdown"]),[m,x]=l.useState(!1),[k,p]=l.useState(!0);console.log("ExportDialog rendered with document:",e==null?void 0:e.id),l.useEffect(()=>{var u,d;if(!e){console.error("Document is undefined in ExportDialog"),p(!1);return}if(!e.content){console.error("Document content is undefined in ExportDialog:",e.id),p(!1);return}console.log("ExportDialog document structure check:",{documentId:e.id,contentExists:!!e.content,originalCodeExists:!!e.content.originalCode,originalCodeLength:((u=e.content.originalCode)==null?void 0:u.length)||0,generatedDocExists:!!e.content.generatedDocumentation,generatedDocLength:((d=e.content.generatedDocumentation)==null?void 0:d.length)||0}),e.content.originalCode||console.warn("Document original code is missing in ExportDialog, but continuing anyway"),e.content.generatedDocumentation||console.warn("Document generated documentation is missing in ExportDialog, but continuing anyway"),console.log("Document validated in ExportDialog:",e.id),p(!0)},[e]);const D=u=>{r(d=>d.includes(u)?d.filter(c=>c!==u):[...d,u])},f=async()=>{var u;if(a.length===0){g.error("Please select at least one format");return}if(!k){console.error("Invalid document for export (isDocumentValid is false)"),g.error("Document data is invalid or missing");return}if(!e){console.error("Document is undefined in handleExport"),g.error("Document data is missing");return}if(!e.content){console.error("Document content is undefined in handleExport:",e.id),g.error("Document content is missing");return}console.log("Starting export with:",{documentId:e.id,formats:a,contentAvailable:!!e.content,codeAvailable:!!e.content.originalCode,docAvailable:!!e.content.generatedDocumentation,cachedFormats:((u=e.content.exportCache)==null?void 0:u.map(d=>d.format))||[]}),console.log("Export button clicked in dialog, formats:",a,"document:",e.id),a.map(d=>`export-${d}-${Date.now()}`),g.loading(`Preparing export for ${a.length} format(s)...`,{id:"export-all"}),x(!0);try{const d=JSON.parse(JSON.stringify(e));console.log("Calling onExport handler with formats:",a),await i(d,a),console.log("Export completed successfully"),g.success(`Documents exported successfully in ${a.length} format(s)`,{id:"export-all"}),o()}catch(d){console.error("Export error:",d),g.error("Failed to export document: "+(d instanceof Error?d.message:"Unknown error"),{id:"export-all"}),d instanceof Error&&console.error("Error stack:",d.stack);let c="Unknown error occurred";d instanceof Error&&(c=d.message,d.message.includes("CORS")||d.message.includes("cross-origin")?c="Browser security prevented download. Try again or use a different format.":d.message.includes("network")||d.message.includes("fetch")?c="Network error. Check your internet connection and try again.":d.message.includes("timeout")&&(c="Export timed out. Try again or use a simpler format.")),g.error(`Export failed: ${c}`,{id:"export-all"})}finally{x(!1)}};return t.jsx(Qt,{open:n,onOpenChange:o,children:t.jsxs($e,{className:"sm:max-w-md",children:[t.jsxs(Ue,{children:[t.jsx(Ve,{children:"Export Document"}),t.jsx(Ge,{children:"Select the formats you want to export your document to. Text formats (Markdown, HTML, Text) are generated instantly, while rich formats (Word, PDF) may take a moment to process."})]}),k?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"grid gap-4 py-4",children:an.map(u=>t.jsxs("div",{className:"flex items-center space-x-3 space-y-0",children:[t.jsx(Ye,{id:`format-${u.id}`,checked:a.includes(u.id),onCheckedChange:()=>D(u.id)}),t.jsxs(dt,{htmlFor:`format-${u.id}`,className:"flex items-center space-x-2 cursor-pointer",children:[u.icon,t.jsx("span",{children:u.label})]})]},u.id))}),t.jsxs(Be,{children:[t.jsx(S,{variant:"outline",onClick:o,disabled:m,children:"Cancel"}),t.jsx(S,{onClick:f,disabled:m||a.length===0,children:m?t.jsxs(t.Fragment,{children:[t.jsx(at,{size:16,className:"mr-2 animate-spin"}),"Exporting..."]}):t.jsxs(t.Fragment,{children:["Export Selected (",a.length,")"]})})]})]}):t.jsxs("div",{className:"my-6 text-center",children:[t.jsx("p",{className:"text-destructive mb-4",children:"Document data is missing or invalid"}),t.jsx(S,{variant:"outline",onClick:o,children:"Close"})]})]})})}function bn(){et();const{user:e}=nt(),{profile:n}=ot(),{documents:o,fetchUserDocuments:i,error:a}=rt(),[r,m]=l.useState(!1),[x,k]=l.useState(null),p=new URLSearchParams(window.location.search),D=p.get("id"),f=p.get("view")==="true",u=p.get("edit")==="true",d=Rt({user:e,docId:D||void 0,viewMode:f,editMode:u}),c=_t({documents:o||[]}),y=At({user:e,userPlan:(n==null?void 0:n.accountPlan)||ee.FREE,documentsCount:(o==null?void 0:o.length)||0,onDocumentDeleted:s=>{},onRefreshDocuments:()=>i((e==null?void 0:e.uid)||"")}),v=Lt({user:e});return l.useEffect(()=>{e!=null&&e.uid&&(m(!0),i(e.uid).finally(()=>m(!1)))},[e==null?void 0:e.uid,i]),l.useEffect(()=>{a&&k(a)},[a]),d.viewingDocument?t.jsxs("div",{className:"container mx-auto p-6",children:[t.jsx("div",{className:"mb-4",children:t.jsxs(S,{variant:"outline",onClick:()=>d.setViewingDocument(null),className:"mb-4",children:[t.jsx(st,{className:"w-4 h-4 mr-2"}),"Zurück zur Dokumentenliste"]})}),t.jsxs(ne,{children:[t.jsxs(oe,{children:[t.jsx(re,{children:d.viewingDocument.title}),t.jsx(ae,{children:d.viewingDocument.description})]}),t.jsx(se,{children:t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"font-semibold mb-2",children:"Original Code:"}),t.jsx("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto",children:d.viewingDocument.content.originalCode})]}),d.viewingDocument.content.generatedDocumentation&&t.jsxs("div",{children:[t.jsx("h3",{className:"font-semibold mb-2",children:"Generierte Dokumentation:"}),t.jsx("div",{className:"prose max-w-none",children:d.viewingDocument.content.generatedDocumentation})]})]})})]})]}):t.jsxs("div",{className:"container mx-auto p-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("div",{children:[t.jsx("h1",{className:"text-3xl font-bold",children:"Meine Dokumente"}),t.jsxs("p",{className:"text-gray-600 mt-2",children:[(o==null?void 0:o.length)||0," Dokumente • Plan: ",(n==null?void 0:n.accountPlan)||ee.FREE]})]}),t.jsxs(S,{onClick:y.handleCreateNew,disabled:!y.canCreateNew,className:"flex items-center gap-2",children:[t.jsx(te,{className:"w-4 h-4"}),"Neues Dokument"]})]}),t.jsxs("div",{className:"flex gap-4 mb-6",children:[t.jsxs("div",{className:"flex-1 relative",children:[t.jsx(Pt,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),t.jsx(ut,{placeholder:"Dokumente durchsuchen...",value:c.searchTerm,onChange:s=>c.setSearchTerm(s.target.value),className:"pl-10"})]}),c.allTags.length>0&&t.jsxs(ie,{children:[t.jsx(le,{asChild:!0,children:t.jsxs(S,{variant:"outline",className:"flex items-center gap-2",children:[t.jsx(It,{className:"w-4 h-4"}),"Tags ",c.activeTag&&`(${c.activeTag})`]})}),t.jsxs(ce,{children:[t.jsx(O,{onClick:()=>c.setActiveTag(null),children:"Alle Tags"}),t.jsx(de,{}),c.allTags.map(s=>t.jsx(O,{onClick:()=>c.setActiveTag(s),children:s},s))]})]}),c.hasActiveFilters&&t.jsx(S,{variant:"ghost",onClick:c.clearFilters,className:"text-gray-500",children:"Filter löschen"})]}),r?t.jsx("div",{className:"text-center py-8",children:t.jsx("p",{children:"Dokumente werden geladen..."})}):c.filteredDocs.length===0?t.jsxs("div",{className:"text-center py-8",children:[t.jsx(ke,{className:"w-16 h-16 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Keine Dokumente gefunden"}),t.jsx("p",{className:"text-gray-600 mb-4",children:c.hasActiveFilters?"Keine Dokumente entsprechen Ihren Filterkriterien.":"Erstellen Sie Ihr erstes Dokument, um zu beginnen."}),!c.hasActiveFilters&&t.jsxs(S,{onClick:y.handleCreateNew,children:[t.jsx(te,{className:"w-4 h-4 mr-2"}),"Erstes Dokument erstellen"]})]}):t.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:c.filteredDocs.map(s=>t.jsxs(ne,{className:`cursor-pointer transition-all hover:shadow-md ${y.deleteTargetId===s.id?"opacity-50":""}`,children:[t.jsx(oe,{children:t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx(re,{className:"text-lg line-clamp-2",children:s.title}),t.jsx(ae,{className:"line-clamp-2 mt-1",children:s.description||"Keine Beschreibung"})]}),t.jsxs(ie,{children:[t.jsx(le,{asChild:!0,children:t.jsx(S,{variant:"ghost",size:"sm",children:"•••"})}),t.jsxs(ce,{children:[t.jsxs(O,{onClick:()=>y.handleSelectDocument(s.id),children:[t.jsx(Ft,{className:"w-4 h-4 mr-2"}),"Bearbeiten"]}),t.jsxs(O,{onClick:()=>d.loadDocument(s.id,"view"),children:[t.jsx(jt,{className:"w-4 h-4 mr-2"}),"Anzeigen"]}),t.jsxs(O,{onClick:()=>v.handleExportDialog(s.id),children:[t.jsx(Ct,{className:"w-4 h-4 mr-2"}),"Exportieren"]}),t.jsx(de,{}),t.jsxs(O,{onClick:()=>y.handleDeleteDocument(s.id),className:"text-red-600",children:[t.jsx(Tt,{className:"w-4 h-4 mr-2"}),"Löschen"]})]})]})]})}),t.jsxs(se,{children:[t.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500 mb-3",children:[t.jsx(bt,{className:"w-4 h-4"}),y.formatDate(s.updatedAt||s.createdAt)]}),t.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500 mb-3",children:[t.jsx(fe,{className:"w-4 h-4"}),s.language," • ",s.codeSize," Zeichen"]}),s.tags&&s.tags.length>0&&t.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.tags.slice(0,3).map(h=>t.jsx(me,{variant:"secondary",className:"text-xs",children:h},h)),s.tags.length>3&&t.jsxs(me,{variant:"outline",className:"text-xs",children:["+",s.tags.length-3]})]})]}),t.jsx(ct,{className:"pt-0",children:t.jsxs("div",{className:"flex items-center gap-2 w-full",children:[s.isPublic?t.jsx(Nt,{className:"w-4 h-4 text-green-600"}):t.jsx(St,{className:"w-4 h-4 text-gray-400"}),t.jsx("span",{className:"text-xs text-gray-500 flex-1",children:s.isPublic?"Öffentlich":"Privat"})]})})]},s.id))}),t.jsx(sn,{isOpen:v.exportDialogOpen,onClose:()=>v.setExportDialogOpen(!1),document:v.exportDocument,onExport:v.handleExportDocument})]})}export{bn as default};
