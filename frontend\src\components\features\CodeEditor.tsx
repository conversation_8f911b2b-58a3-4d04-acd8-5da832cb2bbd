import React, { forwardRef } from 'react';
import { SimpleCodeEditor } from './SimpleCodeEditor';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  height?: string;
  placeholder?: string;
  highlightedLines?: number[];
}

export const CodeEditor = forwardRef<any, CodeEditorProps>((props, ref) => {
  return <SimpleCodeEditor {...props} ref={ref} />;
});

CodeEditor.displayName = 'CodeEditor';