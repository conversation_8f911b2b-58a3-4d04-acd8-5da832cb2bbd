/**
 * Copyright 2015 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import "../variables";
@import "../mixins";

.mdl-menu__container {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  position: absolute;
  overflow: visible;
  height: 0;
  width: 0;
  visibility: hidden;
  z-index: -1;

  &.is-visible,
  &.is-animating {
    z-index: 999;
    visibility: visible;
  }
}

.mdl-menu__outline {
  display: block;
  background: $default-dropdown-bg-color;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 2px;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  opacity: 0;
  transform: scale(0);
  transform-origin: 0 0;
  @include shadow-2dp();
  will-change: transform;
  transition: transform $menu-expand-duration $animation-curve-default,
      opacity $menu-fade-duration $animation-curve-default;
  z-index: -1;

  .mdl-menu__container.is-visible & {
    opacity: 1;
    transform: scale(1);
    z-index: 999;
  }

  &.mdl-menu--bottom-right {
    transform-origin: 100% 0;
  }

  &.mdl-menu--top-left {
    transform-origin: 0 100%;
  }

  &.mdl-menu--top-right {
    transform-origin: 100% 100%;
  }
}

.mdl-menu {
  position: absolute;
  list-style: none;
  top: 0;
  left: 0;
  height: auto;
  width: auto;
  min-width: 124px;
  padding: 8px 0;
  margin: 0;
  opacity: 0;
  clip: rect(0 0 0 0);
  z-index: -1;

  .mdl-menu__container.is-visible & {
    opacity: 1;
    z-index: 999;
  }

  &.is-animating {
    transition: opacity $menu-fade-duration $animation-curve-default,
        clip $menu-expand-duration $animation-curve-default;
  }

  &.mdl-menu--bottom-right {
    left: auto;
    right: 0;
  }

  &.mdl-menu--top-left {
    top: auto;
    bottom: 0;
  }

  &.mdl-menu--top-right {
    top: auto;
    left: auto;
    bottom: 0;
    right: 0;
  }

  &.mdl-menu--unaligned {
    top: auto;
    left: auto;
  }
}

.mdl-menu__item {
  display: block;
  border: none;
  color: $default-item-text-color;
  background-color: transparent;
  text-align: left;
  margin: 0;
  padding: 0 16px;
  outline-color: $default-item-outline-color;
  position: relative;
  overflow: hidden;
  @include typo-body-1();
  text-decoration: none;
  cursor: pointer;
  height: 48px;
  line-height: 48px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity $menu-fade-duration $animation-curve-default;
  user-select: none;

  .mdl-menu__container.is-visible & {
    opacity: 1;
  }

  &::-moz-focus-inner {
    border: 0;
  }

  &--full-bleed-divider {
    border-bottom: 1px solid $default-item-divider-color;
  }

  &[disabled], &[data-mdl-disabled] {
    color: $disabled-item-text-color;
    background-color: transparent;
    cursor: auto;

    &:hover {
      background-color: transparent;
    }

    &:focus {
      background-color: transparent;
    }

    & .mdl-ripple {
      background: transparent;
    }
  }

  &:hover {
    background-color: $default-item-hover-bg-color;
  }

  &:focus {
    outline: none;
    background-color: $default-item-focus-bg-color;
  }

  &:active {
    background-color: $default-item-active-bg-color;
  }
}


.mdl-menu__item--ripple-container {
  display: block;
  height: 100%;
  left: 0px;
  position: absolute;
  top: 0px;
  width: 100%;
  z-index: 0;
  overflow: hidden;
}
