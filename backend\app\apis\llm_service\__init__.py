from fastapi import APIRouter
import databutton as db
from typing import Dict, List, Optional, Any, Union, Literal
import openai
from openai import OpenAI
import requests
import json
import re
import time
from enum import Enum

# Create empty router for compatibility
router = APIRouter()

# LLM Service class for managing different LLM backends
class LLMProvider(str, Enum):
    """Enum of supported LLM providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    AUTO = "auto"

class ModelType(str, Enum):
    """Enum of supported model types"""
    GENERAL = "general"
    CODE = "code"

class LLMService:
    """Service to interact with different LLM backends"""
    
    def __init__(self):
        """Initialize the LLM service with available API keys"""
        self.openai_api_key = self._get_api_key("OpenAI_API_KEy")
        self.deepseek_api_key = self._get_api_key("Deepseek_API_KEY")
        
        # Initialize clients if API keys are available
        self.openai_client = None
        self.deepseek_available = False
        
        # Track provider status and models
        self.providers = {}
        
        # Configure OpenAI if available
        if self.openai_api_key:
            self.openai_client = OpenAI(api_key=self.openai_api_key)
            self.providers[LLMProvider.OPENAI] = {
                "available": True,
                "models": {
                    ModelType.GENERAL: "gpt-4o-mini",
                    ModelType.CODE: "gpt-4o-mini"
                },
                "rate_limit": {
                    "tpm": 10000,  # Tokens per minute (approximate)
                    "rpm": 500     # Requests per minute (approximate)
                }
            }
        
        # Configure DeepSeek if available
        if self.deepseek_api_key:
            self.deepseek_available = True
            self.providers[LLMProvider.DEEPSEEK] = {
                "available": True,
                "models": {
                    ModelType.GENERAL: "deepseek-chat",
                    ModelType.CODE: "deepseek-coder"
                },
                "rate_limit": {
                    "tpm": 10000,  # Tokens per minute (approximate)
                    "rpm": 100     # Requests per minute (approximate)
                }
            }
        
        # Track rate limiting
        self.last_request_time = {}
        self.consecutive_errors = {}
            
    def _get_api_key(self, key_name: str) -> Optional[str]:
        """Get API key from secrets, with fallback handling"""
        try:
            return db.secrets.get(key_name)
        except Exception:
            print(f"Warning: {key_name} not available in secrets")
            return None
    
    def is_available(self, provider: str) -> bool:
        """Check if a specific LLM provider is available"""
        if provider.lower() == LLMProvider.AUTO:
            return len(self.providers) > 0
        return provider.lower() in self.providers and self.providers[provider.lower()]["available"]
    
    def list_available_providers(self) -> List[str]:
        """List all available LLM providers"""
        return [provider for provider in self.providers.keys()]
    
    def get_provider_for_task(self, requested_provider: str, task_type: str = ModelType.CODE) -> str:
        """Get the best provider for a specific task"""
        if requested_provider.lower() != LLMProvider.AUTO and self.is_available(requested_provider.lower()):
            return requested_provider.lower()
        
        # Choose based on availability and priority
        available_providers = self.list_available_providers()
        
        if not available_providers:
            raise ValueError("No LLM providers available")
            
        # Prioritize OpenAI for code-related tasks, DeepSeek for general tasks
        # This could be enhanced with more sophisticated provider selection logic
        if task_type == ModelType.CODE and LLMProvider.OPENAI in available_providers:
            return LLMProvider.OPENAI
        elif task_type == ModelType.GENERAL and LLMProvider.DEEPSEEK in available_providers:
            return LLMProvider.DEEPSEEK
            
        # Default to first available provider
        return available_providers[0]
    
    def _handle_rate_limiting(self, provider: str) -> None:
        """Handle rate limiting for a provider"""
        if provider not in self.providers:
            return
            
        now = time.time()
        provider_config = self.providers[provider]
        
        # Initialize rate limiting tracking if not present
        if provider not in self.last_request_time:
            self.last_request_time[provider] = now
            return
            
        # Calculate time since last request
        elapsed = now - self.last_request_time[provider]
        min_interval = 60 / provider_config["rate_limit"]["rpm"]
        
        # Add a buffer to avoid rate limit errors
        min_interval *= 1.1
        
        # Sleep if we need to wait
        if elapsed < min_interval:
            sleep_time = min_interval - elapsed
            time.sleep(sleep_time)
    
    async def analyze_code_with_openai(self, code: str, prompt: str, model_type: str = ModelType.CODE, system_prompt: str = None, project_context: Optional[str] = None) -> str:
        """Analyze code using OpenAI API
        
        Args:
            code: The code to analyze
            prompt: The analysis prompt
            model_type: The type of model to use
            
        Returns:
            The analysis result as text
        """
        if not self.openai_client:
            raise ValueError("OpenAI API key not available")
        
        # Check if provider is available
        provider = LLMProvider.OPENAI
        if not self.is_available(provider):
            raise ValueError(f"{provider} not available")
            
        # Get model based on type
        model = self.providers[provider]["models"].get(model_type, "gpt-4o-mini")
        
        # Handle rate limiting
        self._handle_rate_limiting(provider)
        
        # Track request time for rate limiting
        self.last_request_time[provider] = time.time()
        
        try:
            # Use custom system prompt if provided, otherwise use default
            system_message = system_prompt if system_prompt else "You are a GIS programming expert that analyzes Python code and extracts information about it in a structured format."
            
            user_content = prompt
            if project_context:
                user_content += f"\n\nAdditional Project Context to consider:\n{project_context}"
            user_content += f"\n\nHere is the code to analyze:\n```python\n{code}\n```"

            completion = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_content}
                ],
                temperature=0.2,  # Lower temperature for more consistent, analytical results
            )
            
            # Reset consecutive errors on success
            self.consecutive_errors[provider] = 0
            
            return completion.choices[0].message.content
        except Exception as e:
            # Track consecutive errors
            if provider not in self.consecutive_errors:
                self.consecutive_errors[provider] = 0
            self.consecutive_errors[provider] += 1
            
            # If we've had multiple consecutive errors, mark provider as unavailable
            if self.consecutive_errors[provider] >= 3:
                print(f"Marking {provider} as unavailable after multiple errors")
                self.providers[provider]["available"] = False
            
            error_msg = f"OpenAI API error: {str(e)}"
            print(error_msg)
            raise ValueError(error_msg)
    
    async def analyze_code_with_deepseek(self, code: str, prompt: str, model_type: str = ModelType.CODE, system_prompt: str = None, project_context: Optional[str] = None) -> str:
        """Analyze code using DeepSeek API
        
        Args:
            code: The code to analyze
            prompt: The analysis prompt
            model_type: The type of model to use
            
        Returns:
            The analysis result as text
        """
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key not available")
            
        # Check if provider is available
        provider = LLMProvider.DEEPSEEK
        if not self.is_available(provider):
            raise ValueError(f"{provider} not available")
        
        # Get model based on type
        model = self.providers[provider]["models"].get(model_type, "deepseek-coder")
        
        # Handle rate limiting
        self._handle_rate_limiting(provider)
        
        # Track request time for rate limiting
        self.last_request_time[provider] = time.time()
        
        try:
            headers = {
                "Authorization": f"Bearer {self.deepseek_api_key}",
                "Content-Type": "application/json"
            }
            
            # Use custom system prompt if provided, otherwise use default
            system_message = system_prompt if system_prompt else "You are a GIS programming expert that analyzes Python code and extracts information about it in a structured format."
            
            user_content_deepseek = prompt
            if project_context:
                user_content_deepseek += f"\n\nAdditional Project Context to consider:\n{project_context}"
            user_content_deepseek += f"\n\nHere is the code to analyze:\n```python\n{code}\n```"

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_content_deepseek}
                ],
                "temperature": 0.2,
                "max_tokens": 1500,
                "stream": False
            }
            
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=60  # Add timeout to prevent hanging requests
            )
            
            if response.status_code != 200:
                raise ValueError(f"DeepSeek API error: {response.status_code} - {response.text}")
                
            result = response.json()
            
            # Reset consecutive errors on success
            self.consecutive_errors[provider] = 0
            
            return result['choices'][0]['message']['content']
            
        except Exception as e:
            # Track consecutive errors
            if provider not in self.consecutive_errors:
                self.consecutive_errors[provider] = 0
            self.consecutive_errors[provider] += 1
            
            # If we've had multiple consecutive errors, mark provider as unavailable
            if self.consecutive_errors[provider] >= 3:
                print(f"Marking {provider} as unavailable after multiple errors")
                self.providers[provider]["available"] = False
            
            error_msg = f"DeepSeek API error: {str(e)}"
            print(error_msg)
            raise ValueError(error_msg)

    async def summarize_text_with_openai(self, text_to_summarize: str, prompt: str, system_prompt: str) -> str:
        if not self.openai_client:
            raise ValueError("OpenAI API key not available")
        provider = LLMProvider.OPENAI
        if not self.is_available(provider):
            raise ValueError(f"{provider} not available")
        model = self.providers[provider]["models"].get(ModelType.GENERAL, "gpt-4o-mini")
        self._handle_rate_limiting(provider)
        self.last_request_time[provider] = time.time()
        try:
            completion = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt} # Prompt now includes the text_to_summarize
                ],
                temperature=0.3, # Slightly higher for summarization creativity
            )
            self.consecutive_errors[provider] = 0
            return completion.choices[0].message.content
        except Exception as e:
            if provider not in self.consecutive_errors:
                self.consecutive_errors[provider] = 0
            self.consecutive_errors[provider] += 1
            if self.consecutive_errors[provider] >= 3:
                print(f"Marking {provider} as unavailable after multiple errors")
                self.providers[provider]["available"] = False
            error_msg = f"OpenAI API summarization error: {str(e)}"
            print(error_msg)
            raise ValueError(error_msg)

    async def summarize_text_with_deepseek(self, text_to_summarize: str, prompt: str, system_prompt: str) -> str:
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key not available")
        provider = LLMProvider.DEEPSEEK
        if not self.is_available(provider):
            raise ValueError(f"{provider} not available")
        model = self.providers[provider]["models"].get(ModelType.GENERAL, "deepseek-chat")
        self._handle_rate_limiting(provider)
        self.last_request_time[provider] = time.time()
        try:
            headers = {
                "Authorization": f"Bearer {self.deepseek_api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt} # Prompt now includes the text_to_summarize
                ],
                "temperature": 0.3,
                "max_tokens": 500, # Max tokens for summary
                "stream": False
            }
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=45
            )
            if response.status_code != 200:
                raise ValueError(f"DeepSeek API summarization error: {response.status_code} - {response.text}")
            result = response.json()
            self.consecutive_errors[provider] = 0
            return result['choices'][0]['message']['content']
        except Exception as e:
            if provider not in self.consecutive_errors:
                self.consecutive_errors[provider] = 0
            self.consecutive_errors[provider] += 1
            if self.consecutive_errors[provider] >= 3:
                print(f"Marking {provider} as unavailable after multiple errors")
                self.providers[provider]["available"] = False
            error_msg = f"DeepSeek API summarization error: {str(e)}"
            print(error_msg)
            raise ValueError(error_msg)

    async def summarize_text(
        self,
        text_to_summarize: str,
        target_language: str = "de",
        provider: str = LLMProvider.AUTO,
        retry_on_failure: bool = True,
        custom_system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        available_providers = self.list_available_providers()
        if not available_providers:
            return {"success": False, "error": "No LLM providers available.", "provider_used": None, "result": None}

        try:
            provider_to_use = self.get_provider_for_task(provider, ModelType.GENERAL)
        except ValueError as e:
            return {"success": False, "error": str(e), "provider_used": None, "result": None}

        system_prompt_to_use = custom_system_prompt if custom_system_prompt else "You are a helpful assistant designed to summarize technical texts concisely."
        
        # Construct the user prompt for summarization
        user_prompt = (
            f"Bitte fasse den folgenden Text prägnant zusammen. "
            f"Der Text liefert Kontext für ein Software-Skript oder eine technische Dokumentation. "
            f"Konzentriere dich auf die wichtigsten Kernaussagen, den Zweck und die beschriebenen Hauptfunktionen. "
            f"Die Zusammenfassung soll helfen, ein zugehöriges Code-Skript besser zu dokumentieren. "
            f"Halte die Zusammenfassung kurz und präzise, idealerweise nicht mehr als 4-5 Sätze. "
            f"Der gesamte Output MUSS in der Sprache '{target_language}' sein. "
            f"Hier ist der zu analysierende Text:\n\n---\n{text_to_summarize}\n---"
        )

        try:
            if provider_to_use == LLMProvider.OPENAI:
                result = await self.summarize_text_with_openai(text_to_summarize, user_prompt, system_prompt_to_use)
            elif provider_to_use == LLMProvider.DEEPSEEK:
                result = await self.summarize_text_with_deepseek(text_to_summarize, user_prompt, system_prompt_to_use)
            else:
                raise ValueError(f"Unsupported provider: {provider_to_use}")
            return {"success": True, "provider_used": provider_to_use, "result": result}
        except Exception as e:
            if retry_on_failure and len(available_providers) > 1:
                fallback_providers = [p for p in available_providers if p != provider_to_use]
                if not fallback_providers:
                    return {"success": False, "error": f"Provider {provider_to_use} failed (summary) and no fallback: {str(e)}"}
                
                fallback_provider = fallback_providers[0]
                print(f"Summarization with {provider_to_use} failed. Retrying with {fallback_provider}.")
                try:
                    if fallback_provider == LLMProvider.OPENAI:
                        result = await self.summarize_text_with_openai(text_to_summarize, user_prompt, system_prompt_to_use)
                    elif fallback_provider == LLMProvider.DEEPSEEK:
                        result = await self.summarize_text_with_deepseek(text_to_summarize, user_prompt, system_prompt_to_use)
                    else:
                        raise ValueError(f"Unsupported fallback provider: {fallback_provider}")
                    return {"success": True, "provider_used": fallback_provider, "result": result, "warning": f"Fallback to {fallback_provider} for summarization."}
                except Exception as fallback_error:
                    return {"success": False, "error": f"All providers failed summarization. Original: {str(e)}. Fallback: {str(fallback_error)}"}
            return {"success": False, "error": f"Provider {provider_to_use} failed summarization: {str(e)}"}
    
    async def analyze_code(self, code: str, prompt: str, provider: str = LLMProvider.AUTO, retry_on_failure: bool = True, model_type: str = ModelType.CODE, system_prompt: str = None, is_gis_code: bool = False, project_context: Optional[str] = None) -> Dict[str, Any]:
        """Analyze code using the specified provider or automatically choose an available one
        
        Args:
            code: The code to analyze
            prompt: The analysis prompt
            provider: Provider to use (from LLMProvider enum)
            retry_on_failure: Whether to try another provider if the first one fails
            model_type: Type of model to use (from ModelType enum)
            
        Returns:
            Dict with analysis results and metadata
        """
        available_providers = self.list_available_providers()
        
        if not available_providers:
            return {
                "success": False,
                "error": "No LLM providers available. Please configure API keys.",
                "provider_used": None,
                "result": None,
                "available_providers": []
            }
        
        # Determine which provider to use
        try:
            provider_to_use = self.get_provider_for_task(provider, model_type)
        except ValueError as e:
            return {
                "success": False,
                "error": str(e),
                "provider_used": None,
                "result": None,
                "available_providers": available_providers
            }
        
        try:
            # If it's GIS code, use the specialized system prompt if one isn't provided
            if is_gis_code and not system_prompt:
                system_prompt = "You are an expert in GIS (Geographic Information Systems) programming and documentation. Analyze the provided code focusing specifically on GIS operations, spatial data types, and geospatial workflows."
                
            if provider_to_use == LLMProvider.OPENAI:
                result = await self.analyze_code_with_openai(code, prompt, model_type, system_prompt, project_context)
            elif provider_to_use == LLMProvider.DEEPSEEK:
                result = await self.analyze_code_with_deepseek(code, prompt, model_type, system_prompt, project_context)
            else:
                raise ValueError(f"Unsupported provider: {provider_to_use}")
            
            return {
                "success": True,
                "provider_used": provider_to_use,
                "result": result,
                "available_providers": available_providers
            }
            
        except Exception as e:
            # If the preferred provider fails, try another one if available
            if retry_on_failure and len(available_providers) > 1:
                # Get fallback provider different from the one that failed
                fallback_providers = [p for p in available_providers if p != provider_to_use]
                if not fallback_providers:
                    return {
                        "success": False,
                        "error": f"Provider {provider_to_use} failed and no fallback providers available: {str(e)}",
                        "provider_used": None,
                        "result": None,
                        "available_providers": available_providers
                    }
                    
                fallback_provider = fallback_providers[0]
                
                try:
                    # If it's GIS code, use the specialized system prompt if one isn't provided
                    if is_gis_code and not system_prompt:
                        system_prompt = "You are an expert in GIS (Geographic Information Systems) programming and documentation. Analyze the provided code focusing specifically on GIS operations, spatial data types, and geospatial workflows."
                    
                    if fallback_provider == LLMProvider.OPENAI:
                        result = await self.analyze_code_with_openai(code, prompt, model_type, system_prompt, project_context)
                    elif fallback_provider == LLMProvider.DEEPSEEK:
                        result = await self.analyze_code_with_deepseek(code, prompt, model_type, system_prompt, project_context)
                    else:
                        raise ValueError(f"Unsupported fallback provider: {fallback_provider}")
                    
                    return {
                        "success": True,
                        "provider_used": fallback_provider,
                        "result": result,
                        "warning": f"Fallback to {fallback_provider} after {provider_to_use} failed: {str(e)}",
                        "available_providers": available_providers
                    }
                except Exception as fallback_error:
                    return {
                        "success": False,
                        "error": f"All providers failed. Original error: {str(e)}. Fallback error: {str(fallback_error)}",
                        "provider_used": None,
                        "result": None,
                        "available_providers": available_providers
                    }
            
            return {
                "success": False,
                "error": f"Provider {provider_to_use} failed: {str(e)}",
                "provider_used": None,
                "result": None,
                "available_providers": available_providers
            }

# Create a global instance of the LLM service
llm_service = LLMService()

# Export functions for use in other modules
async def analyze_code_with_llm(code: str, prompt: str, provider: str = LLMProvider.AUTO, retry_on_failure: bool = True, model_type: str = ModelType.CODE, system_prompt: str = None, is_gis_code: bool = False, project_context: Optional[str] = None) -> Dict[str, Any]:
    """Analyze code using available LLM providers
    
    Args:
        code: The code to analyze
        prompt: The analysis prompt
        provider: Provider to use (from LLMProvider enum)
        retry_on_failure: Whether to try another provider if the first one fails
        model_type: Type of model to use (from ModelType enum)
        system_prompt: Custom system prompt to use (overrides default)
        is_gis_code: Whether the code is GIS-related (enables GIS-specific processing)
        project_context: Optional additional context about the project or code's purpose.
        
    Returns:
        Dict with analysis results and metadata
    """
    return await llm_service.analyze_code(code, prompt, provider, retry_on_failure, model_type, system_prompt, is_gis_code, project_context)

async def get_available_llm_providers() -> List[str]:
    """Get list of available LLM providers
    
    Returns:
        List of provider names that are available
    """
    return llm_service.list_available_providers()

async def get_provider_for_task(requested_provider: str, task_type: str = ModelType.CODE) -> str:
    """Get the best provider for a specific task
    
    Args:
        requested_provider: Requested provider
        task_type: Type of task
        
    Returns:
        Provider to use
    """
    return llm_service.get_provider_for_task(requested_provider, task_type)

# --- New Text Summarization Functionality ---
async def summarize_text_with_llm(
    text_to_summarize: str,
    target_language: str = "de", # Default to German as per app context
    provider: str = LLMProvider.AUTO,
    retry_on_failure: bool = True,
    custom_system_prompt: Optional[str] = None
) -> Dict[str, Any]:
    """Summarize text using available LLM providers.

    Args:
        text_to_summarize: The text to summarize.
        target_language: The language for the summary (e.g., 'de', 'en').
        provider: Provider to use (from LLMProvider enum).
        retry_on_failure: Whether to try another provider if the first one fails.
        custom_system_prompt: Optional custom system prompt for summarization.

    Returns:
        Dict with summarization results and metadata.
    """
    return await llm_service.summarize_text(
        text_to_summarize=text_to_summarize,
        target_language=target_language,
        provider=provider,
        retry_on_failure=retry_on_failure,
        custom_system_prompt=custom_system_prompt
    )
# --- End New Text Summarization Functionality ---
