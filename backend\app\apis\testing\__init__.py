"""Test utilities for documentation generator"""

import asyncio
from typing import Dict, Any

from fastapi import APIRouter
from app.apis.documentation_models import DocumentationRequest
from app.apis.documentation_generator import generate_documentation

# Define the router
router = APIRouter()

@router.get("/test")
async def run_test_endpoint():
    """Test endpoint for documentation generation"""
    result = await test_documentation_generator()
    return result

def get_language_name(language_code: str) -> str:
    """
    Get the full language name from the language code
    """
    language_map = {
        "en": "English",
        "de": "German (Deutsch)",
        "fr": "French (Français)",
        "it": "Italian (Italiano)",
        "es": "Spanish (Español)"
    }
    
    return language_map.get(language_code, "Unknown language")

@router.post("/test_language")
async def testing_documentation_language(request: DocumentationRequest):
    """
    Test endpoint for documentation language settings.
    Returns the language that would be used for documentation.
    """
    return {
        "success": True,
        "requested_language": request.documentationLanguage,
        "language_name": get_language_name(request.documentationLanguage)
    }

# Simple GIS script sample - avoids syntax issues with triple quotes
SIMPLE_GIS_SCRIPT = "import arcpy\nimport os\nfrom arcpy import env\nfrom arcpy.sa import *\n\n# Set workspace environment\nworkspace = 'C:/GISData/Project'\nenv.workspace = workspace\nenv.overwriteOutput = True\n\n# Input parameters\ninput_points = os.path.join(workspace, 'sensor_locations.shp')\ninput_dem = os.path.join(workspace, 'elevation.tif')\noutput_buffer = os.path.join(workspace, 'sensor_buffers.shp')\n\n# Buffer distance in meters\nbuffer_distance = 100.0\n\ndef main():\n    # Create buffer around sensor points\n    arcpy.Buffer_analysis(input_points, output_buffer, str(buffer_distance) + ' Meters')\n    return True\n\nif __name__ == '__main__':\n    main()"

# Simple non-GIS script
SIMPLE_NON_GIS_SCRIPT = "import pandas as pd\nimport numpy as np\n\ndef analyze_data(filepath):\n    data = pd.read_csv(filepath)\n    result = data.describe()\n    return result\n\nif __name__ == '__main__':\n    analyze_data('data.csv')"

async def test_documentation_generator(gis_script: bool = True, documentation_language: str = "en") -> Dict[str, Any]:
    """Test the documentation generator with a sample script
    
    Args:
        gis_script: Whether to use the GIS script (True) or non-GIS script (False)
        
    Returns:
        The documentation generation response as a dictionary
    """
    # Create a test request
    request = DocumentationRequest(
        code=SIMPLE_GIS_SCRIPT if gis_script else SIMPLE_NON_GIS_SCRIPT,
        language="python",
        provider="auto",
        format="markdown",
        documentationLanguage=documentation_language
    )
    
    # Generate documentation
    response = await generate_documentation(request)
    
    # Convert to dictionary for easier inspection
    response_dict = response.model_dump()
    
    return response_dict

# Runner function
def run_test():
    """Run the test and print results"""
    import asyncio
    
    result = asyncio.run(test_documentation_generator())
    print(f"Success: {result.get('success')}")
    print(f"Provider used: {result.get('provider_used')}")
    
    if result.get('success'):
        doc = result.get('documentation', {})
        print("\nDocumentation generated:")
        print(f"\nTextual Description:\n{doc.get('textualDescription')[:200]}...")
        
        print(f"\nParameters ({len(doc.get('parameters', []))}):") 
        for param in doc.get('parameters', []):
            print(f"- {param['name']} ({param['type']}): {param['description'][:50]}...")
        
        print(f"\nWorkflow steps ({len(doc.get('workflow', {}).get('steps', []))}):") 
        for step in doc.get('workflow', {}).get('steps', []):
            print(f"- Step {step['id']}: {step['name']}")
            
        print(f"\nTools used ({len(doc.get('toolsUsed', []))}):") 
        for tool in doc.get('toolsUsed', [])[:5]:  # Show first 5 tools
            print(f"- {tool}")
