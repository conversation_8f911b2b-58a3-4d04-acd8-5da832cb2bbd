var q=function(U){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},q(U)},D=function(U,J){var Y=Object.keys(U);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(U);J&&(I=I.filter(function(x){return Object.getOwnPropertyDescriptor(U,x).enumerable})),Y.push.apply(Y,I)}return Y},N=function(U){for(var J=1;J<arguments.length;J++){var Y=arguments[J]!=null?arguments[J]:{};J%2?D(Object(Y),!0).forEach(function(I){CC(U,I,Y[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(Y)):D(Object(Y)).forEach(function(I){Object.defineProperty(U,I,Object.getOwnPropertyDescriptor(Y,I))})}return U},CC=function(U,J,Y){if(J=BC(J),J in U)Object.defineProperty(U,J,{value:Y,enumerable:!0,configurable:!0,writable:!0});else U[J]=Y;return U},BC=function(U){var J=GC(U,"string");return q(J)=="symbol"?J:String(J)},GC=function(U,J){if(q(U)!="object"||!U)return U;var Y=U[Symbol.toPrimitive];if(Y!==void 0){var I=Y.call(U,J||"default");if(q(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,Y=function C(H,B){for(var G in B)J(H,G,{get:B[G],enumerable:!0,configurable:!0,set:function X(Z){return B[G]=function(){return Z}}})},I={lessThanXSeconds:{one:"1\uCD08 \uBBF8\uB9CC",other:"{{count}}\uCD08 \uBBF8\uB9CC"},xSeconds:{one:"1\uCD08",other:"{{count}}\uCD08"},halfAMinute:"30\uCD08",lessThanXMinutes:{one:"1\uBD84 \uBBF8\uB9CC",other:"{{count}}\uBD84 \uBBF8\uB9CC"},xMinutes:{one:"1\uBD84",other:"{{count}}\uBD84"},aboutXHours:{one:"\uC57D 1\uC2DC\uAC04",other:"\uC57D {{count}}\uC2DC\uAC04"},xHours:{one:"1\uC2DC\uAC04",other:"{{count}}\uC2DC\uAC04"},xDays:{one:"1\uC77C",other:"{{count}}\uC77C"},aboutXWeeks:{one:"\uC57D 1\uC8FC",other:"\uC57D {{count}}\uC8FC"},xWeeks:{one:"1\uC8FC",other:"{{count}}\uC8FC"},aboutXMonths:{one:"\uC57D 1\uAC1C\uC6D4",other:"\uC57D {{count}}\uAC1C\uC6D4"},xMonths:{one:"1\uAC1C\uC6D4",other:"{{count}}\uAC1C\uC6D4"},aboutXYears:{one:"\uC57D 1\uB144",other:"\uC57D {{count}}\uB144"},xYears:{one:"1\uB144",other:"{{count}}\uB144"},overXYears:{one:"1\uB144 \uC774\uC0C1",other:"{{count}}\uB144 \uC774\uC0C1"},almostXYears:{one:"\uAC70\uC758 1\uB144",other:"\uAC70\uC758 {{count}}\uB144"}},x=function C(H,B,G){var X,Z=I[H];if(typeof Z==="string")X=Z;else if(B===1)X=Z.one;else X=Z.other.replace("{{count}}",B.toString());if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return X+" \uD6C4";else return X+" \uC804";return X};function z(C){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=H.width?String(H.width):C.defaultWidth,G=C.formats[B]||C.formats[C.defaultWidth];return G}}var $={full:"y\uB144 M\uC6D4 d\uC77C EEEE",long:"y\uB144 M\uC6D4 d\uC77C",medium:"y.MM.dd",short:"y.MM.dd"},M={full:"a H\uC2DC mm\uBD84 ss\uCD08 zzzz",long:"a H:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:z({formats:$,defaultWidth:"full"}),time:z({formats:M,defaultWidth:"full"}),dateTime:z({formats:S,defaultWidth:"full"})},R={lastWeek:"'\uC9C0\uB09C' eeee p",yesterday:"'\uC5B4\uC81C' p",today:"'\uC624\uB298' p",tomorrow:"'\uB0B4\uC77C' p",nextWeek:"'\uB2E4\uC74C' eeee p",other:"P"},V=function C(H,B,G,X){return R[H]};function E(C){return function(H,B){var G=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",X;if(G==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,Q=B!==null&&B!==void 0&&B.width?String(B.width):Z;X=C.formattingValues[Q]||C.formattingValues[Z]}else{var T=C.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):C.defaultWidth;X=C.values[K]||C.values[T]}var A=C.argumentCallback?C.argumentCallback(H):H;return X[A]}}var j={narrow:["BC","AD"],abbreviated:["BC","AD"],wide:["\uAE30\uC6D0\uC804","\uC11C\uAE30"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1\uBD84\uAE30","2\uBD84\uAE30","3\uBD84\uAE30","4\uBD84\uAE30"]},v={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1\uC6D4","2\uC6D4","3\uC6D4","4\uC6D4","5\uC6D4","6\uC6D4","7\uC6D4","8\uC6D4","9\uC6D4","10\uC6D4","11\uC6D4","12\uC6D4"],wide:["1\uC6D4","2\uC6D4","3\uC6D4","4\uC6D4","5\uC6D4","6\uC6D4","7\uC6D4","8\uC6D4","9\uC6D4","10\uC6D4","11\uC6D4","12\uC6D4"]},w={narrow:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],short:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],abbreviated:["\uC77C","\uC6D4","\uD654","\uC218","\uBAA9","\uAE08","\uD1A0"],wide:["\uC77C\uC694\uC77C","\uC6D4\uC694\uC77C","\uD654\uC694\uC77C","\uC218\uC694\uC77C","\uBAA9\uC694\uC77C","\uAE08\uC694\uC77C","\uD1A0\uC694\uC77C"]},P={narrow:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},abbreviated:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},wide:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"}},_={narrow:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},abbreviated:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"},wide:{am:"\uC624\uC804",pm:"\uC624\uD6C4",midnight:"\uC790\uC815",noon:"\uC815\uC624",morning:"\uC544\uCE68",afternoon:"\uC624\uD6C4",evening:"\uC800\uB141",night:"\uBC24"}},F=function C(H,B){var G=Number(H),X=String(B===null||B===void 0?void 0:B.unit);switch(X){case"minute":case"second":return String(G);case"date":return G+"\uC77C";default:return G+"\uBC88\uC9F8"}},k={ordinalNumber:F,era:E({values:j,defaultWidth:"wide"}),quarter:E({values:f,defaultWidth:"wide",argumentCallback:function C(H){return H-1}}),month:E({values:v,defaultWidth:"wide"}),day:E({values:w,defaultWidth:"wide"}),dayPeriod:E({values:P,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function O(C){return function(H){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.width,X=G&&C.matchPatterns[G]||C.matchPatterns[C.defaultMatchWidth],Z=H.match(X);if(!Z)return null;var Q=Z[0],T=G&&C.parsePatterns[G]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(T)?b(T,function(W){return W.test(Q)}):h(T,function(W){return W.test(Q)}),A;A=C.valueCallback?C.valueCallback(K):K,A=B.valueCallback?B.valueCallback(A):A;var t=H.slice(Q.length);return{value:A,rest:t}}}var h=function C(H,B){for(var G in H)if(Object.prototype.hasOwnProperty.call(H,G)&&B(H[G]))return G;return},b=function C(H,B){for(var G=0;G<H.length;G++)if(B(H[G]))return G;return};function m(C){return function(H){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=H.match(C.matchPattern);if(!G)return null;var X=G[0],Z=H.match(C.parsePattern);if(!Z)return null;var Q=C.valueCallback?C.valueCallback(Z[0]):Z[0];Q=B.valueCallback?B.valueCallback(Q):Q;var T=H.slice(X.length);return{value:Q,rest:T}}}var c=/^(\d+)(일|번째)?/i,y=/\d+/i,u={narrow:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(기원전|서기)/i},g={any:[/^(bc|기원전)/i,/^(ad|서기)/i]},p={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]사?분기/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^(1[012]|[123456789])/,abbreviated:/^(1[012]|[123456789])월/i,wide:/^(1[012]|[123456789])월/i},i={any:[/^1월?$/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},n={narrow:/^[일월화수목금토]/,short:/^[일월화수목금토]/,abbreviated:/^[일월화수목금토]/,wide:/^[일월화수목금토]요일/},s={any:[/^일/,/^월/,/^화/,/^수/,/^목/,/^금/,/^토/]},o={any:/^(am|pm|오전|오후|자정|정오|아침|저녁|밤)/i},r={any:{am:/^(am|오전)/i,pm:/^(pm|오후)/i,midnight:/^자정/i,noon:/^정오/i,morning:/^아침/i,afternoon:/^오후/i,evening:/^저녁/i,night:/^밤/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(H){return parseInt(H,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(H){return H+1}}),month:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ko",formatDistance:x,formatLong:L,formatRelative:V,localize:k,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{ko:a})})})();

//# debugId=D0F46A27BA1F1F3B64756e2164756e21
