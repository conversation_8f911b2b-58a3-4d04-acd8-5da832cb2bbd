/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Gemeinsame Utility-Funktionen für das gesamte Frontend
 */

/**
 * Formatiert ein Datum für die Anzeige in deutscher Lokalisierung
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('de-DE', {
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

/**
 * Formatiert ein Datum für Export-Zwecke (ISO-Format)
 */
export function formatDateForExport(date: Date): string {
  return date.toISOString().replace(/[:.]/g, "-").substring(0, 19);
}

/**
 * Bereinigt einen Dateinamen von ungültigen Zeichen
 */
export function sanitizeFilename(filename: string): string {
  return filename.replace(/[^a-z0-9]/gi, '_').toLowerCase();
}

/**
 * Validiert eine E-Mail-Adresse
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validiert ein Passwort (mindestens 6 Zeichen)
 */
export function validatePassword(password: string): { isValid: boolean; message?: string } {
  if (password.length < 6) {
    return { isValid: false, message: 'Passwort muss mindestens 6 Zeichen lang sein' };
  }
  return { isValid: true };
}

/**
 * Prüft ob zwei Passwörter übereinstimmen
 */
export function validatePasswordMatch(password: string, confirmPassword: string): { isValid: boolean; message?: string } {
  if (password !== confirmPassword) {
    return { isValid: false, message: 'Passwörter stimmen nicht überein' };
  }
  return { isValid: true };
}

/**
 * Debounce-Funktion für Performance-Optimierung
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Formatiert Dateigröße in menschenlesbarer Form
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Kürzt einen Text auf eine bestimmte Länge
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Konvertiert einen String in Title Case
 */
export function toTitleCase(str: string): string {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

/**
 * Prüft ob ein Wert leer ist (null, undefined, leerer String, leeres Array)
 */
export function isEmpty(value: any): boolean {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

/**
 * Generiert eine zufällige ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Kopiert Text in die Zwischenablage
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Fehler beim Kopieren in die Zwischenablage:', err);
    return false;
  }
}
