from fastapi import APIRouter, UploadFile, HTTPException, File
from typing import List, Dict, Any
import io
import pytesseract
from PIL import Image
import pypdf
import docx
from openai import OpenAI
import os
import re
from pydantic import BaseModel

router = APIRouter(prefix="/context-processor", tags=["Context Processor"])

# Initialize OpenAI client with environment variable
openai_api_key = os.environ.get("OPENAI_API_KEY")
client = None # Initialize client to None
if openai_api_key:
    try:
        client = OpenAI(api_key=openai_api_key)
        print("OpenAI client initialized successfully.")
    except Exception as e:
        print(f"CRITICAL: Failed to initialize OpenAI client with provided API key: {e}")
        # client remains None
else:
    print("CRITICAL: OPENAI_API_KEY not found in secrets. OpenAI client not initialized.")

def sanitize_storage_key(key: str) -> str:
    """Sanitize storage key to only allow alphanumeric and ._- symbols"""
    return re.sub(r'[^a-zA-Z0-9._-]', '', key)

def extract_text_from_txt(file_content: bytes) -> str:
    return file_content.decode('utf-8', errors='ignore')

def extract_text_from_pdf(file_content: bytes) -> str:
    try:
        reader = pypdf.PdfReader(io.BytesIO(file_content))
        text = []
        for page in reader.pages:
            text.append(page.extract_text() or "")
        return "\n".join(text)
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        return "" # Return empty string on error

def extract_text_from_docx(file_content: bytes) -> str:
    try:
        doc = docx.Document(io.BytesIO(file_content))
        text = []
        for para in doc.paragraphs:
            text.append(para.text)
        return "\n".join(text)
    except Exception as e:
        print(f"Error extracting DOCX: {e}")
        return ""

def extract_text_from_image(file_content: bytes) -> str:
    try:
        image = Image.open(io.BytesIO(file_content))
        # Convert to RGB if it's RGBA (Tesseract might have issues with alpha channel)
        if image.mode == 'RGBA':
            image = image.convert('RGB')
        return pytesseract.image_to_string(image, lang='deu+eng') # Added German for OCR
    except Exception as e:
        print(f"Error extracting Image with OCR: {e}")
        return ""

class SummarizeFilesResponse(BaseModel):
    success: bool
    summary: str | None = None
    error_message: str | None = None

# DEAKTIVIERT: Endpunkt wurde zu /docs/summarize-files migriert
# @router.post("/summarize-uploaded-files", response_model=SummarizeFilesResponse)
# async def summarize_uploaded_files(files: List[UploadFile] = File(...)):
    if not client:
        raise HTTPException(status_code=500, detail="OpenAI client not initialized. Check API key.")

    if not files:
        return SummarizeFilesResponse(success=False, error_message="No files provided.")

    extracted_texts = []
    files_processed_count = 0
    processing_errors = []

    for file in files:
        content = await file.read()
        filename = file.filename or "unknown_file"
        text = ""
        
        print(f"Processing file: {filename}")

        if filename.lower().endswith(".txt"):
            text = extract_text_from_txt(content)
        elif filename.lower().endswith(".pdf"):
            text = extract_text_from_pdf(content)
        elif filename.lower().endswith(".docx"):
            text = extract_text_from_docx(content)
        elif filename.lower().endswith((".png", ".jpg", ".jpeg")):
            text = extract_text_from_image(content)
        else:
            processing_errors.append(f"Unsupported file type: {filename}")
            print(f"Unsupported file type: {filename}")
            continue
        
        if text.strip():
            extracted_texts.append(f"--- Content from {filename} ---\n{text.strip()}\n--- End of {filename} ---")
            files_processed_count += 1
            print(f"Successfully extracted text from {filename}")
        else:
            processing_errors.append(f"Could not extract text or empty content from: {filename}")
            print(f"Could not extract text or empty content from: {filename}")


    if not extracted_texts:
        error_detail = "No text could be extracted from the provided files."
        if processing_errors:
            error_detail += " Errors: " + "; ".join(processing_errors)
        return SummarizeFilesResponse(success=False, error_message=error_detail)

    combined_text = "\n\n".join(extracted_texts)
    
    # Limit combined_text size to avoid overly long prompts (e.g., first 50k chars)
    # This is a rough limit, tokenization would be more precise but adds complexity here
    max_prompt_length = 50000 
    if len(combined_text) > max_prompt_length:
        combined_text = combined_text[:max_prompt_length]
        print(f"Combined text was truncated to {max_prompt_length} characters for LLM prompt.")


    system_prompt = (
        "You are an AI assistant specialized in summarizing technical and project-related documents, "
        "including those from the GIS (Geographic Information Systems) domain. "
        "Your task is to create a concise, coherent, and informative summary from the provided text snippets, "
        "which are extracted from one or more uploaded files. Focus on the key information, "
        "main objectives, methodologies, parameters, findings, and any GIS-specific details if present. "
        "The summary should be in German."
    )
    
    user_prompt = (
        f"Please summarize the following combined text, which includes content from {files_processed_count} file(s). "
        "Ensure the summary is well-structured, in German, and captures the essence of the documents. "
        "If there are GIS-specific details, please highlight them.\n\n"
        f"Combined text:\n{combined_text}"
    )

    try:
        print(f"Sending {len(combined_text)} characters to OpenAI for summarization from {files_processed_count} files.")
        completion = client.chat.completions.create(
            model="gpt-4o-mini", # Using a capable model for summarization
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.5, # Slightly creative but still factual
        )
        summary = completion.choices[0].message.content
        print(f"Successfully received summary from OpenAI.")
        
        final_error_message = None
        if processing_errors:
            final_error_message = "Summary generated, but some files had issues: " + "; ".join(processing_errors)
            
        return SummarizeFilesResponse(success=True, summary=summary, error_message=final_error_message)

    except Exception as e:
        print(f"Error calling OpenAI for summarization: {str(e)}")
        error_detail = f"Failed to generate summary using OpenAI: {str(e)}"
        if processing_errors:
             error_detail += " Previous file processing errors: " + "; ".join(processing_errors)
        return SummarizeFilesResponse(success=False, error_message=error_detail)

