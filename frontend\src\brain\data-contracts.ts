/** Body_summarize_uploaded_files */
export interface BodySummarizeUploadedFiles {
  /** Files */
  files: File[];
}

/** CodeValidationRequest */
export interface CodeValidationRequest {
  /** Code */
  code: string;
  /**
   * Language
   * @default ""
   */
  language?: string;
}

/** CodeValidationResponse */
export interface CodeValidationResponse {
  /** Is Valid */
  is_valid: boolean;
  /** Detected Language */
  detected_language: string;
  /**
   * Error Message
   * @default ""
   */
  error_message?: string;
  /**
   * Error Line Number
   * @default 0
   */
  error_line_number?: number | null;
  /**
   * Error Column Number
   * @default 0
   */
  error_column_number?: number | null;
  /**
   * Is Gis Code
   * @default false
   */
  is_gis_code?: boolean;
}

/** DocumentExportRequest */
export interface DocumentExportRequest {
  /** Document Id */
  document_id: string;
  /** Document Data */
  document_data: Record<string, any>;
  /** Format */
  format: string;
}

/**
 * DocumentationGenerationResponse
 * Model for the documentation generation response
 */
export interface DocumentationGenerationResponse {
  /** Success */
  success: boolean;
  /** Error Message */
  error_message?: string | null;
  /** Provider Used */
  provider_used?: string | null;
  /** Language Used */
  language_used?: string | null;
  documentation?: DocumentationResponse | null;
}

/**
 * DocumentationParameter
 * Model for a parameter in the documentation
 */
export interface DocumentationParameter {
  /**
   * Name
   * @default ""
   */
  name?: string;
  /**
   * Type
   * @default "Unknown"
   */
  type?: string;
  /**
   * Description
   * @default ""
   */
  description?: string;
  /**
   * Required
   * @default true
   */
  required?: boolean;
  /**
   * Constraints
   * @default ""
   */
  constraints?: string;
}

/**
 * DocumentationRequest
 * Model for the documentation request
 */
export interface DocumentationRequest {
  /** Code */
  code: string;
  /**
   * Language
   * @default "Python"
   */
  language?: string;
  /**
   * Provider
   * @default "auto"
   */
  provider?: string;
  /**
   * Format
   * @default "markdown"
   */
  format?: string;
  /**
   * Documentationlanguage
   * @default "en"
   */
  documentationLanguage?: string;
  /** Documentation Id */
  documentation_id?: string | null;
  /** Project Context */
  project_context?: string | null;
}

/**
 * DocumentationResponse
 * Model for the documentation response
 */
export interface DocumentationResponse {
  /**
   * Textualdescription
   * @default ""
   */
  textualDescription?: string;
  /**
   * Parameters
   * @default []
   */
  parameters?: DocumentationParameter[];
  /** Model for a workflow in the documentation */
  workflow?: DocumentationWorkflow;
  /**
   * Toolsused
   * @default []
   */
  toolsUsed?: string[];
  /**
   * Originalcode
   * @default ""
   */
  originalCode?: string;
}

/**
 * DocumentationWorkflow
 * Model for a workflow in the documentation
 */
export interface DocumentationWorkflow {
  /**
   * Steps
   * @default []
   */
  steps?: DocumentationWorkflowStep[];
}

/**
 * DocumentationWorkflowStep
 * Model for a workflow step in the documentation
 */
export interface DocumentationWorkflowStep {
  /** Id */
  id: number;
  /** Name */
  name: string;
  /** Description */
  description: string;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** HealthResponse */
export interface HealthResponse {
  /** Status */
  status: string;
}

/** LlmValidateCodeSyntaxRequest */
export interface LlmValidateCodeSyntaxRequest {
  /** Code */
  code: string;
  /**
   * Language
   * @default "python"
   */
  language?: string | null;
}

/** LlmValidateCodeSyntaxResponse */
export interface LlmValidateCodeSyntaxResponse {
  /** Isvalid */
  isValid: boolean;
  /**
   * Errors
   * @default []
   */
  errors?: SyntaxErrorDetail[];
  /** Detected Language */
  detected_language?: string | null;
  /** Raw Llm Response */
  raw_llm_response?: string | null;
}

/** SummarizeFilesResponse */
export interface SummarizeFilesResponse {
  /** Success */
  success: boolean;
  /** Summary */
  summary?: string | null;
  /** Error Message */
  error_message?: string | null;
}

/** SyntaxErrorDetail */
export interface SyntaxErrorDetail {
  /** Line */
  line: number;
  /** Column */
  column: number;
  /** Message */
  message: string;
  /**
   * Severity
   * @default "error"
   */
  severity?: "error" | "warning" | "info" | "hint";
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

export type CheckHealthData = HealthResponse;

export type RunTestEndpointData = any;

export type TestingDocumentationLanguageData = any;

export type TestingDocumentationLanguageError = HTTPValidationError;

export interface ValidateCodeEndpoint2Params {
  /**
   * Language
   * Documentation language code (en, de, fr, it, es)
   * @default "en"
   */
  language?: string;
}

export type ValidateCodeEndpoint2Data = any;

export type ValidateCodeEndpoint2Error = HTTPValidationError;

export interface DownloadFromStorageParams {
  /** Storage Key */
  storageKey: string;
}

export type DownloadFromStorageData = any;

export type DownloadFromStorageError = HTTPValidationError;

export type ExportDocumentData = any;

export type ExportDocumentError = HTTPValidationError;

export interface GetExportStatusParams {
  /** Export Id */
  exportId: string;
}

export type GetExportStatusData = any;

export type GetExportStatusError = HTTPValidationError;

export interface DownloadDocumentParams {
  /** Export Id */
  exportId: string;
}

export type DownloadDocumentData = any;

export type DownloadDocumentError = HTTPValidationError;

export interface DeleteExportParams {
  /** Export Id */
  exportId: string;
}

export type DeleteExportData = any;

export type DeleteExportError = HTTPValidationError;

export type SummarizeUploadedFilesData = SummarizeFilesResponse;

export type SummarizeUploadedFilesError = HTTPValidationError;

export type LlmValidateCodeSyntaxEndpointData = LlmValidateCodeSyntaxResponse;

export type LlmValidateCodeSyntaxEndpointError = HTTPValidationError;

export type GenerateDocumentationData = DocumentationGenerationResponse;

export type GenerateDocumentationError = HTTPValidationError;

export type ValidateCodeEndpointData = CodeValidationResponse;

export type ValidateCodeEndpointError = HTTPValidationError;
