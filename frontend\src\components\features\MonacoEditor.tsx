/**
 * Author: rahn
 * Datum: 21.06.2025
 * Version: 1.0
 * Beschreibung: Monaco Editor Komponente mit Syntax-Highlighting und Fehleranzeige
 */

import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import Editor, { Monaco, OnMount, Range as MonacoRange } from '@monaco-editor/react'; // NEU: Importiere Editor und Typen

// Struktur für einen einzelnen Fehler definieren
export interface EditorError {
  line: number;
  column: number;
  message: string;
  severity?: 'error' | 'warning' | 'info' | 'hint'; // Optional: Monaco unterstützt verschiedene Schweregrade
}

interface MonacoEditorProps {
  value: string;
  onChange: (value: string | undefined) => void; // Geändert: value kann undefined sein
  language: string;
  height?: string;
  placeholder?: string; // Bleibt vorerst, obwohl Monaco das anders handhabt
  readOnly?: boolean;
  theme?: string; // NEU: Theme-Option
  errors?: EditorError[]; // NEU: Prop for passing multiple errors
}

// Zuordnung häufiger Sprachnamen zu Monaco Editor Sprach-IDs
const languageMap: Record<string, string> = {
  'python': 'python',
  'javascript': 'javascript',
  'typescript': 'typescript',
  'r': 'r',
  'sql': 'sql',
  'java': 'java',
  'c': 'cpp', // Monaco verwendet 'cpp' für C
  'c++': 'cpp',
  'cpp': 'cpp',
  'c#': 'csharp',
  'csharp': 'csharp',
  'json': 'json',
  'markdown': 'markdown',
  'css': 'css',
  'html': 'html',
  'xml': 'xml',
  'yaml': 'yaml',
  'plaintext': 'plaintext',
};

export const MonacoEditor = forwardRef<any, MonacoEditorProps>(
  ({ value, onChange, language, height = '400px', placeholder = '', readOnly = false, theme = 'vs-dark', errors = [] }, ref) => { // Added errors prop
    const editorInstanceRef = useRef<any>(null); // Für die Monaco Editor Instanz
    const monacoInstanceRef = useRef<Monaco | null>(null); // Für die Monaco Library Instanz
    const decorationsCollectionRef = useRef<string[]>([]); // Für die IDs der aktuellen Dekorationen

    const getMonacoLanguage = (lang: string): string => {
      return languageMap[lang?.toLowerCase()] || 'plaintext';
    };

    const handleEditorDidMount: OnMount = (editor, monaco) => {
      editorInstanceRef.current = editor;
      monacoInstanceRef.current = monaco;

      // Placeholder-Handling (Monaco hat keinen direkten Placeholder wie eine Textarea)
      // Man könnte eine Decoration verwenden oder ein Overlay, aber das ist komplexer.
      // Fürs Erste lassen wir den Placeholder-Text im Kommentar.
      // console.log("Monaco placeholder (not directly supported):", placeholder);

      // Fokus auf den Editor, wenn er gemountet wird
      // editor.focus();
    };

    useImperativeHandle(ref, () => ({
      focus: () => {
        editorInstanceRef.current?.focus();
      },
      setCursor: (pos: { lineNumber: number; column: number }) => {
        editorInstanceRef.current?.setPosition(pos);
      },
      revealLineInCenter: (lineNumber: number) => {
        editorInstanceRef.current?.revealLineInCenter(lineNumber, monacoInstanceRef.current?.editor.ScrollType.Smooth);
      },
      // Renamed and updated to handle multiple errors
      applyErrorMarkers: (incomingErrors: EditorError[]) => {
        if (editorInstanceRef.current && monacoInstanceRef.current) {
          const monaco = monacoInstanceRef.current;
          const editor = editorInstanceRef.current;
          const model = editor.getModel();

          if (!model) return;

          const newDecorations = incomingErrors.map(error => {
            const lineContent = model.getLineContent(error.line);
            const maxColumn = model.getLineMaxColumn(error.line);
            // Ensure column is within bounds, default to maxColumn if it's too large or 1 if too small
            const validatedColumn = Math.min(Math.max(1, error.column || 1), maxColumn);
            const endColumn = lineContent.includes("\t") || (error.column || 1) >= maxColumn -1 ? maxColumn : validatedColumn + 1; // Highlight at least one char or whole line for tabs

            return {
              range: new monaco.Range(error.line, validatedColumn, error.line, endColumn),
              options: {
                // isWholeLine: false, // We want to highlight specific parts if possible
                className: error.severity === 'warning' ? 'warning-line-highlight' : 'error-line-highlight',
                glyphMarginClassName: error.severity === 'warning' ? 'warning-glyph-margin' : 'error-glyph-margin',
                hoverMessage: { value: error.message },
                overviewRuler: {
                  color: error.severity === 'warning' ? 'rgba(255, 220, 0, 0.7)' : 'rgba(255, 0, 0, 0.7)',
                  position: monaco.editor.OverviewRulerLane.Full
                }
              }
            };
          });
          decorationsCollectionRef.current = editor.deltaDecorations(decorationsCollectionRef.current, newDecorations);
        }
      },
      clearErrorMarkers: () => { // Renamed for clarity, functionality is the same
        if (editorInstanceRef.current) {
          decorationsCollectionRef.current = editorInstanceRef.current.deltaDecorations(decorationsCollectionRef.current, []);
        }
      },
      getValue: () => {
        return editorInstanceRef.current?.getValue();
      }
    }));

    // Effekt, um Highlighting zu entfernen, wenn der Code sich ändert (durch externe `value` Änderung)
    // oder wenn der readOnly Status wechselt (kann Dekorationen beeinflussen)
    useEffect(() => {
      if (editorInstanceRef.current && monacoInstanceRef.current) {
        // Apply new errors when the errors prop changes
        const monaco = monacoInstanceRef.current;
        const editor = editorInstanceRef.current;
        const model = editor.getModel();
        if (!model) return;

        const newDecorations = (errors || [])
          .filter(error => {
            const modelLineCount = model.getLineCount();
            const isValidLine =
              Number.isInteger(error.line) && 
              error.line >= 1 && 
              error.line <= modelLineCount;
            if (!isValidLine) {
              console.warn(
                `MonacoEditor: Skipping error decoration due to invalid line number: ${error.line} (model lines: ${modelLineCount})`,
                error
              );
            }
            return isValidLine;
          })
          .map(error => {
            // const lineContent = model.getLineContent(error.line); // Not strictly needed if highlighting whole line
            const maxColumn = model.getLineMaxColumn(error.line);
            // const validatedColumn = Math.min(Math.max(1, error.column || 1), maxColumn); // Column validation less critical for full line highlight
            
            return {
              range: new monaco.Range(error.line, 1, error.line, maxColumn), // Highlight the full line
              options: {
                isWholeLine: true, // Explicitly state we are highlighting the whole line
                className: error.severity === 'warning' ? 'warning-line-highlight' : 'error-line-highlight',
                glyphMarginClassName: error.severity === 'warning' ? 'warning-glyph-margin' : 'error-glyph-margin',
                hoverMessage: { value: error.message },
                stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
                overviewRuler: {
                  color: error.severity === 'warning' ? 'rgba(255, 220, 0, 0.7)' : 'rgba(255, 0, 0, 0.7)',
                  position: monaco.editor.OverviewRulerLane.Full
                }
              }
            };
          });
        decorationsCollectionRef.current = editor.deltaDecorations(decorationsCollectionRef.current, newDecorations);
      }
    }, [errors, value, readOnly]); // Re-apply decorations if errors, value or readOnly status change


    return (
      <Editor
        height={height}
        language={getMonacoLanguage(language)}
        value={value}
        onChange={onChange}
        onMount={handleEditorDidMount}
        theme={theme} // Standard: 'vs-dark', andere Optionen 'vs-light', 'hc-black'
        options={{
          readOnly: readOnly,
          selectOnLineNumbers: true,
          automaticLayout: true, // Wichtig für korrekte Größenanpassung
          scrollBeyondLastLine: false,
          lineNumbers: 'on', // Zeilennummern anzeigen
          glyphMargin: true, // Platz für Glyphen (z.B. Fehlerindikatoren)
          minimap: { enabled: true }, // Minimap anzeigen
          wordWrap: "on", // Automatischer Zeilenumbruch
          // Placeholder kann hier nicht direkt gesetzt werden
        }}
      />
    );
  }
);

MonacoEditor.displayName = "MonacoEditor";
