import { FileCode2, FileText, Globe, Terminal, Database, BarChart, Lightbulb } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface Props {
  fileExtension: string | undefined;
}

export function FileTypeIndicator({ fileExtension, isGisCode }: Props & { isGisCode?: boolean }) {
  if (!fileExtension) return null;
  
  const extension = fileExtension.toLowerCase();
  
  // Define different file type categories with their extensions
  const fileTypes = {
    python: ['.py', '.pyw', '.pyc', '.pyd', '.pyo', '.pyi'],
    javascript: ['.js', '.jsx', '.mjs', '.cjs'],
    typescript: ['.ts', '.tsx', '.mts', '.cts'],
    r: ['.r', '.rmd', '.rds'],
    sql: ['.sql'],
    java: ['.java', '.class', '.jar'],
    csharp: ['.cs', '.csx'],
    cpp: ['.cpp', '.cc', '.cxx', '.c++', '.h', '.hpp', '.hxx', '.h++'],
    c: ['.c', '.h'],
    other: ['.txt', '.md', '.json', '.xml', '.yaml', '.yml']
  };
  
  // Find the file type category
  let fileType = 'unknown';
  for (const [type, extensions] of Object.entries(fileTypes)) {
    if (extensions.includes(extension)) {
      fileType = type;
      break;
    }
  }
  
  // Enhanced color mapping based on file type with brutalist-inspired design
  const colorMap: Record<string, string> = {
    // Use more saturated colors for GIS-related languages
    python: 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/30',
    javascript: 'bg-yellow-500/10 text-yellow-700 dark:text-yellow-500 border-yellow-500/30',
    typescript: 'bg-blue-400/10 text-blue-700 dark:text-blue-400 border-blue-400/30',
    r: 'bg-purple-500/10 text-purple-700 dark:text-purple-400 border-purple-500/30',
    sql: 'bg-orange-500/10 text-orange-700 dark:text-orange-400 border-orange-500/30',
    java: 'bg-red-500/10 text-red-700 dark:text-red-400 border-red-500/30',
    csharp: 'bg-green-500/10 text-green-700 dark:text-green-400 border-green-500/30',
    cpp: 'bg-pink-500/10 text-pink-700 dark:text-pink-400 border-pink-500/30',
    c: 'bg-blue-300/10 text-blue-800 dark:text-blue-300 border-blue-300/30',
    other: 'bg-gray-500/10 text-gray-700 dark:text-gray-400 border-gray-500/30',
    unknown: 'bg-gray-500/10 text-gray-700 dark:text-gray-400 border-gray-500/30'
  };
  
  // Get the appropriate icon based on file type with more specific icons per language
  const getIconForFileType = (type: string, isGisCode?: boolean) => {
    if (isGisCode) return <Globe size={12} className="mr-1" />;
    
    switch (type) {
      case 'python':
        return <Terminal size={12} className="mr-1" />;
      case 'javascript':
      case 'typescript':
        return <FileCode2 size={12} className="mr-1" />;
      case 'sql':
        return <Database size={12} className="mr-1" />;
      case 'r':
        return <BarChart size={12} className="mr-1" />;
      case 'java':
      case 'csharp':
      case 'cpp':
      case 'c':
        return <Lightbulb size={12} className="mr-1" />;
      default:
        return <FileText size={12} className="mr-1" />;
    }
  };
  
  
  const displayName = {
    python: 'Python',
    javascript: 'JavaScript',
    typescript: 'TypeScript',
    r: 'R',
    sql: 'SQL',
    java: 'Java',
    csharp: 'C#',
    cpp: 'C++',
    c: 'C',
    other: extension.slice(1).toUpperCase(),
    unknown: extension.slice(1).toUpperCase()
  }[fileType];
  
  return (
    <span
      className={`inline-flex items-center ${colorMap[fileType]} font-mono text-[10px] py-0 transition-all hover:shadow-sm rounded-full border px-2.5`}
      style={{
        clipPath: 'polygon(5% 0%, 95% 0%, 100% 50%, 95% 100%, 5% 100%, 0% 50%)',
        paddingLeft: '0.625rem',
        paddingRight: '0.625rem'
      }}
    >
      {getIconForFileType(fileType, isGisCode)}
      {isGisCode ? `GIS ${displayName}` : displayName}
    </span>
  );
}
